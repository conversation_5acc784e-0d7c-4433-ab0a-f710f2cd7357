<?php

namespace App\Http;

class R
{
    /**
     * 成功响应
     *
     * @param mixed $data 响应数据
     * @param string $msg 响应消息
     * @return \Illuminate\Http\JsonResponse
     */
    public static function ok($data = null, $msg = '操作成功')
    {
        return response()->json([
            'code' => 0,
            'msg' => $msg,
            'data' => $data
        ]);
    }

    /**
     * 错误响应
     *
     * @param string $msg 错误消息
     * @param int $code 错误码
     * @param mixed $data 错误数据
     * @return \Illuminate\Http\JsonResponse
     */
    public static function error($msg = '操作失败', $code = 400, $data = null)
    {
        return response()->json([
            'code' => $code,
            'msg' => $msg,
            'data' => $data
        ]);
    }

    /**
     * 未授权响应
     *
     * @param string $msg 错误消息
     * @return \Illuminate\Http\JsonResponse
     */
    public static function unauthorized($msg = '未授权')
    {
        return self::error($msg, 401);
    }

    /**
     * 禁止访问响应
     *
     * @param string $msg 错误消息
     * @return \Illuminate\Http\JsonResponse
     */
    public static function forbidden($msg = '禁止访问')
    {
        return self::error($msg, 403);
    }

    /**
     * 资源不存在响应
     *
     * @param string $msg 错误消息
     * @return \Illuminate\Http\JsonResponse
     */
    public static function notFound($msg = '资源不存在')
    {
        return self::error($msg, 404);
    }

    /**
     * 服务器错误响应
     *
     * @param string $msg 错误消息
     * @return \Illuminate\Http\JsonResponse
     */
    public static function serverError($msg = '服务器错误')
    {
        return self::error($msg, 500);
    }

    /**
     * 空数据响应（当资源不存在时返回空对象而不是错误）
     *
     * @param mixed $emptyData 空数据对象
     * @param string $msg 响应消息
     * @return \Illuminate\Http\JsonResponse
     */
    public static function empty($emptyData = null, $msg = '操作成功')
    {
        return response()->json([
            'code' => 0,
            'msg' => $msg,
            'data' => $emptyData
        ]);
    }
}