<?php

namespace App\Http\Controllers;

use App\Http\R;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class PromptController extends Controller
{
    /**
     * 生成提示词
     */
    public function generate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'content' => 'required|string',
            'count' => 'required|integer|min:1|max:100',
            'brand_name' => 'nullable|string',
            'semantic_word' => 'nullable|string',
            'industry_orientation' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return R::error($validator->errors()->first());
        }

        $content = $request->input('content');
        $count = $request->input('count');
        $brandName = $request->input('brand_name');
        $semanticWord = $request->input('semantic_word');
        $industryOrientation = $request->input('industry_orientation');

        // 这里实现提示词生成逻辑
        // 实际项目中可能需要调用AI接口或其他服务来生成提示词
        $prompts = $this->generatePrompts($content, $count, $brandName, $semanticWord, $industryOrientation);

        return R::ok([
            'prompts' => $prompts,
            'total' => count($prompts)
        ]);
    }

    /**
     * 获取提示词集合列表
     */
    public function listCollections(Request $request)
    {
        $page = $request->input('page', 1);
        $pageSize = $request->input('pageSize', 10);
        $name = $request->input('name');

        $query = DB::table('prompt_collections')
            ->whereNull('deleted_at');

        if ($name) {
            $query->where('name', 'like', "%{$name}%");
        }

        $total = $query->count();
        $collections = $query->orderBy('created_at', 'desc')
            ->offset(($page - 1) * $pageSize)
            ->limit($pageSize)
            ->get();

        return R::ok([
            'list' => $collections,
            'total' => $total
        ]);
    }

    /**
     * 创建提示词集合
     */
    public function createCollection(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'prompts' => 'required|array',
            'prompts.*' => 'required|string'
        ]);

        if ($validator->fails()) {
            return R::error($validator->errors()->first());
        }

        $name = $request->input('name');
        $prompts = $request->input('prompts');

        $collectionId = DB::table('prompt_collections')->insertGetId([
            'name' => $name,
            'prompt_count' => count($prompts),
            'created_at' => now(),
            'updated_at' => now()
        ]);

        foreach ($prompts as $prompt) {
            DB::table('prompts')->insert([
                'collection_id' => $collectionId,
                'content' => $prompt,
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }

        return R::ok(['id' => $collectionId]);
    }

    /**
     * 获取提示词集合详情
     */
    public function getCollection($id)
    {
        $collection = DB::table('prompt_collections')
            ->where('id', $id)
            ->whereNull('deleted_at')
            ->first();

        if (!$collection) {
            // 返回空对象而不是错误
            return R::ok((object)[
                'id' => null,
                'name' => '',
                'prompt_count' => 0,
                'prompts' => [],
                'created_at' => null,
                'updated_at' => null
            ]);
        }

        $prompts = DB::table('prompts')
            ->where('collection_id', $id)
            ->whereNull('deleted_at')
            ->get();

        $collection->prompts = $prompts;

        return R::ok($collection);
    }

    /**
     * 更新提示词集合
     */
    public function updateCollection(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'prompts' => 'nullable|array',
            'prompts.*' => 'required|string'
        ]);

        if ($validator->fails()) {
            return R::error($validator->errors()->first());
        }

        $collection = DB::table('prompt_collections')
            ->where('id', $id)
            ->whereNull('deleted_at')
            ->first();

        if (!$collection) {
            return R::error('提示词集合不存在');
        }

        $name = $request->input('name');
        $prompts = $request->input('prompts');

        DB::table('prompt_collections')
            ->where('id', $id)
            ->update([
                'name' => $name,
                'prompt_count' => count($prompts),
                'updated_at' => now()
            ]);

        if ($prompts) {
            // 删除旧的提示词
            DB::table('prompts')
                ->where('collection_id', $id)
                ->delete();

            // 添加新的提示词
            foreach ($prompts as $prompt) {
                DB::table('prompts')->insert([
                    'collection_id' => $id,
                    'content' => $prompt,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }
        }

        return R::ok();
    }

    /**
     * 删除提示词集合
     */
    public function deleteCollection($id)
    {
        $collection = DB::table('prompt_collections')
            ->where('id', $id)
            ->whereNull('deleted_at')
            ->first();

        if (!$collection) {
            return R::error('提示词集合不存在');
        }

        // 软删除
        DB::table('prompt_collections')
            ->where('id', $id)
            ->update([
                'deleted_at' => now()
            ]);

        // 添加到回收站
        DB::table('recycle_bin')->insert([
            'original_id' => $id,
            'name' => $collection->name,
            'type' => 'prompt_collection',
            'delete_time' => now(),
            'expire_time' => now()->addDays(30),
            'delete_user' => auth()->id()
        ]);

        return R::ok();
    }

    /**
     * 增强提示词集合
     */
    public function enhanceCollection($id)
    {
        $collection = DB::table('prompt_collections')
            ->where('id', $id)
            ->whereNull('deleted_at')
            ->first();

        if (!$collection) {
            return R::error('提示词集合不存在');
        }

        $prompts = DB::table('prompts')
            ->where('collection_id', $id)
            ->whereNull('deleted_at')
            ->get()
            ->pluck('content')
            ->toArray();

        // 这里实现提示词增强逻辑
        // 实际项目中可能需要调用AI接口或其他服务来增强提示词
        $enhancedPrompts = $this->enhancePrompts($prompts);

        // 更新提示词
        DB::table('prompts')
            ->where('collection_id', $id)
            ->delete();

        foreach ($enhancedPrompts as $prompt) {
            DB::table('prompts')->insert([
                'collection_id' => $id,
                'content' => $prompt,
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }

        DB::table('prompt_collections')
            ->where('id', $id)
            ->update([
                'prompt_count' => count($enhancedPrompts),
                'updated_at' => now()
            ]);

        return R::ok([
            'prompts' => $enhancedPrompts,
            'total' => count($enhancedPrompts)
        ]);
    }

    /**
     * 导入提示词
     */
    public function importPrompts(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'file' => 'required|file|mimes:csv,txt,xlsx'
        ]);

        if ($validator->fails()) {
            return R::error($validator->errors()->first());
        }

        $name = $request->input('name');
        $file = $request->file('file');

        // 这里实现文件解析逻辑
        // 实际项目中需要根据文件类型进行不同的解析
        $prompts = $this->parsePromptsFromFile($file);

        $collectionId = DB::table('prompt_collections')->insertGetId([
            'name' => $name,
            'prompt_count' => count($prompts),
            'created_at' => now(),
            'updated_at' => now()
        ]);

        foreach ($prompts as $prompt) {
            DB::table('prompts')->insert([
                'collection_id' => $collectionId,
                'content' => $prompt,
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }

        return R::ok(['id' => $collectionId]);
    }

    /**
     * 导出提示词
     */
    public function exportPrompts($id)
    {
        $collection = DB::table('prompt_collections')
            ->where('id', $id)
            ->whereNull('deleted_at')
            ->first();

        if (!$collection) {
            return R::error('提示词集合不存在');
        }

        $prompts = DB::table('prompts')
            ->where('collection_id', $id)
            ->whereNull('deleted_at')
            ->get()
            ->pluck('content')
            ->toArray();

        // 这里实现导出逻辑
        // 实际项目中需要生成文件并提供下载链接
        $fileName = $this->generateExportFile($collection->name, $prompts);

        return R::ok(['file_url' => url('storage/' . $fileName)]);
    }

    /**
     * 合并提示词集合
     */
    public function mergeCollections(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'collection_ids' => 'required|array',
            'collection_ids.*' => 'required|integer|exists:prompt_collections,id'
        ]);

        if ($validator->fails()) {
            return R::error($validator->errors()->first());
        }

        $name = $request->input('name');
        $collectionIds = $request->input('collection_ids');

        $allPrompts = [];
        foreach ($collectionIds as $collectionId) {
            $prompts = DB::table('prompts')
                ->where('collection_id', $collectionId)
                ->whereNull('deleted_at')
                ->get()
                ->pluck('content')
                ->toArray();

            $allPrompts = array_merge($allPrompts, $prompts);
        }

        // 去重
        $uniquePrompts = array_unique($allPrompts);

        $newCollectionId = DB::table('prompt_collections')->insertGetId([
            'name' => $name,
            'prompt_count' => count($uniquePrompts),
            'created_at' => now(),
            'updated_at' => now()
        ]);

        foreach ($uniquePrompts as $prompt) {
            DB::table('prompts')->insert([
                'collection_id' => $newCollectionId,
                'content' => $prompt,
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }

        return R::ok(['id' => $newCollectionId]);
    }

    /**
     * 生成提示词（模拟实现）
     */
    private function generatePrompts($content, $count, $brandName = null, $semanticWord = null, $industryOrientation = null)
    {
        $prompts = [];
        for ($i = 0; $i < $count; $i++) {
            $prefix = '';
            if ($brandName) {
                $prefix .= "品牌：{$brandName}，";
            }
            if ($semanticWord) {
                $prefix .= "核心语义：{$semanticWord}，";
            }
            if ($industryOrientation) {
                $prefix .= "行业导向：{$industryOrientation}，";
            }
            $prompts[] = $prefix . "提示词" . ($i + 1) . "：" . $content . "的" . rand(1, 100) . "种方式";
        }
        return $prompts;
    }

    /**
     * 增强提示词（模拟实现）
     */
    private function enhancePrompts($prompts)
    {
        $enhancedPrompts = [];
        foreach ($prompts as $prompt) {
            $enhancedPrompts[] = $prompt;
            $enhancedPrompts[] = "增强版：" . $prompt;
        }
        return $enhancedPrompts;
    }

    /**
     * 从文件解析提示词（模拟实现）
     */
    private function parsePromptsFromFile($file)
    {
        // 模拟从文件中解析提示词
        return [
            "从文件导入的提示词1",
            "从文件导入的提示词2",
            "从文件导入的提示词3",
        ];
    }

    /**
     * 生成导出文件（模拟实现）
     */
    private function generateExportFile($name, $prompts)
    {
        $fileName = 'exports/' . $name . '_' . time() . '.txt';
        $filePath = storage_path('app/public/' . $fileName);
        
        // 确保目录存在
        if (!file_exists(dirname($filePath))) {
            mkdir(dirname($filePath), 0755, true);
        }
        
        file_put_contents($filePath, implode("\n", $prompts));
        
        return $fileName;
    }
}