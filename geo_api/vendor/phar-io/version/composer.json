{"name": "phar-io/version", "description": "Library for handling version information and constraints", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "support": {"issues": "https://github.com/phar-io/version/issues"}, "require": {"php": "^7.2 || ^8.0"}, "autoload": {"classmap": ["src/"]}}