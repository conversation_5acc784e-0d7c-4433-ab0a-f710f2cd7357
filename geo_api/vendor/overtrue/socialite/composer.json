{"name": "overtrue/socialite", "description": "A collection of OAuth 2 packages.", "keywords": ["o<PERSON>h", "social", "login", "weibo", "wechat", "qq", "feishu", "qcloud"], "autoload": {"files": ["src/Contracts/FactoryInterface.php", "src/Contracts/UserInterface.php", "src/Contracts/ProviderInterface.php"], "psr-4": {"Overtrue\\Socialite\\": "src/"}}, "require": {"php": ">=8.0.2", "ext-json": "*", "ext-openssl": "*", "guzzlehttp/guzzle": "^7.0"}, "require-dev": {"mockery/mockery": "^1.3", "phpunit/phpunit": "^9.0", "jetbrains/phpstorm-attributes": "^1.0", "phpstan/phpstan": "^1.7", "laravel/pint": "^1.2"}, "license": "MIT", "authors": [{"name": "overtrue", "email": "<EMAIL>"}], "scripts": {"check-style": "@php vendor/bin/pint --test", "fix-style": "@php vendor/bin/pint", "test": "@php vendor/bin/phpunit --colors=always", "phpstan": "@php vendor/bin/phpstan analyse src"}}