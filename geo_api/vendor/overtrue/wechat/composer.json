{"name": "overtrue/wechat", "description": "微信SDK", "keywords": ["easywechat", "wechat", "weixin", "weixin-sdk", "sdk"], "license": "MIT", "authors": [{"name": "overtrue", "email": "<EMAIL>"}], "require": {"php": ">=7.4", "ext-fileinfo": "*", "ext-openssl": "*", "ext-simplexml": "*", "easywechat-composer/easywechat-composer": "^1.1", "guzzlehttp/guzzle": "^6.2 || ^7.0", "monolog/monolog": "^1.22 || ^2.0 || ^3.0", "overtrue/socialite": "^3.2 || ^4.0", "pimple/pimple": "^3.0", "psr/simple-cache": "^1.0||^2.0||^3.0", "symfony/cache": "^3.3 || ^4.3 || ^5.0 || ^6.0", "symfony/event-dispatcher": "^4.3 || ^5.0 || ^6.0", "symfony/http-foundation": "^2.7 || ^3.0 || ^4.0 || ^5.0 || ^6.0", "ext-libxml": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.5.0", "brainmaestro/composer-git-hooks": "^2.7", "mikey179/vfsstream": "^1.6", "mockery/mockery": "^1.2.3", "phpstan/phpstan": "^0.12.0", "phpunit/phpunit": "^9.3", "dms/phpunit-arraysubset-asserts": "^0.2.0"}, "autoload": {"psr-4": {"EasyWeChat\\": "src/"}, "files": ["src/Kernel/Support/Helpers.php", "src/Kernel/Helpers.php"]}, "autoload-dev": {"psr-4": {"EasyWeChat\\Tests\\": "tests/"}}, "extra": {"hooks": {"pre-commit": ["composer test", "composer fix-style"], "pre-push": ["composer test", "composer fix-style"]}}, "scripts": {"post-update-cmd": ["cghooks update"], "post-merge": "composer install", "post-install-cmd": ["cghooks add --ignore-lock", "cghooks update"], "phpstan": "vendor/bin/phpstan analyse", "check-style": "vendor/bin/php-cs-fixer fix --using-cache=no --diff --config=.php-cs-fixer.dist.php --dry-run --ansi", "fix-style": "vendor/bin/php-cs-fixer fix --using-cache=no --config=.php-cs-fixer.dist.php --ansi", "test": "vendor/bin/phpunit --colors=always --testdox"}, "config": {"allow-plugins": {"easywechat-composer/easywechat-composer": true}}}