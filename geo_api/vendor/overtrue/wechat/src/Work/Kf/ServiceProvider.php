<?php

/*
 * This file is part of the overtrue/wechat.
 *
 * (c) overtrue <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace EasyWeChat\Work\Kf;

use <PERSON><PERSON>\Container;
use <PERSON><PERSON>\ServiceProviderInterface;

/**
 * Class ServiceProvider.
 *
 * @package EasyWeChat\Work\Kf
 *
 * <AUTHOR> <<EMAIL>>
 */
class ServiceProvider implements ServiceProviderInterface
{
    /**
     * {@inheritdoc}.
     */
    public function register(Container $app)
    {
        $app['kf_account'] = function ($app) {
            return new AccountClient($app);
        };

        $app['kf_servicer'] = function ($app) {
            return new ServicerClient($app);
        };

        $app['kf_message'] = function ($app) {
            return new MessageClient($app);
        };
    }
}
