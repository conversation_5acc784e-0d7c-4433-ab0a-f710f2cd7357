<?php

/*
 * This file is part of the overtrue/wechat.
 *
 * (c) overtrue <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace EasyWeChat\Work\Mobile;

use Pimple\Container;
use <PERSON>mple\ServiceProviderInterface;
use EasyWeChat\Work\Mobile\Auth\Client;

/**
 * ServiceProvider.
 *
 * <AUTHOR> <<EMAIL>>
 */
class ServiceProvider implements ServiceProviderInterface
{
    protected $app;

    /**
     * @param Container $app
     */
    public function register(Container $app)
    {
        $app['mobile'] = function ($app) {
            return new Client($app);
        };
    }
}
