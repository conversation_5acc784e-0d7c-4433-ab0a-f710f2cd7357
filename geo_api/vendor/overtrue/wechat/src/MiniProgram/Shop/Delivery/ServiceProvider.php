<?php

namespace EasyWeChat\MiniProgram\Shop\Delivery;

use <PERSON><PERSON>\Container;
use <PERSON><PERSON>\ServiceProviderInterface;

/**
 * 自定义版交易组件及开放接口 - 物流接口
 *
 * @package EasyWeChat\MiniProgram\Shop\Delivery
 * <AUTHOR> <<EMAIL>>
 */
class ServiceProvider implements ServiceProviderInterface
{
    /**
     * @inheritDoc
     */
    public function register(Container $app)
    {
        $app['shop_delivery'] = function ($app) {
            return new Client($app);
        };
    }
}
