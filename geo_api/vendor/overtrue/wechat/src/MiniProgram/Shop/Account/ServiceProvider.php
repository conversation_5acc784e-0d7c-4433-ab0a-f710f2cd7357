<?php

namespace EasyWeChat\MiniProgram\Shop\Account;

use <PERSON><PERSON>\Container;
use <PERSON><PERSON>\ServiceProviderInterface;

/**
 * 自定义版交易组件及开放接口 - 商家入驻接口
 *
 * @package EasyWeChat\MiniProgram\Shop\Account
 * <AUTHOR> <<EMAIL>>
 */
class ServiceProvider implements ServiceProviderInterface
{
    /**
     * {@inheritdoc}
     */
    public function register(Container $app)
    {
        $app['shop_account'] = function ($app) {
            return new Client($app);
        };
    }
}
