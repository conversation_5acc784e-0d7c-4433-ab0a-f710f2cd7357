<?php

namespace EasyWeChat\MiniProgram\Shop\Basic;

use <PERSON><PERSON>\Container;
use <PERSON><PERSON>\ServiceProviderInterface;

/**
 * 自定义版交易组件及开放接口 - 接入商品前必需接口
 *
 * @package EasyWeChat\MiniProgram\Shop\Basic
 * <AUTHOR> <<EMAIL>>
 */
class ServiceProvider implements ServiceProviderInterface
{
    /**
     * @inheritDoc
     */
    public function register(Container $app)
    {
        $app['shop_basic'] = function ($app) {
            return new Client($app);
        };
    }
}
