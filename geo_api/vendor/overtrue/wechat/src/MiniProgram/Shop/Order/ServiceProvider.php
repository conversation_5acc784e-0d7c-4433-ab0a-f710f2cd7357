<?php

namespace EasyWeChat\MiniProgram\Shop\Order;

use <PERSON><PERSON>\Container;
use <PERSON><PERSON>\ServiceProviderInterface;

/**
 * 自定义版交易组件及开放接口 - 订单接口
 *
 * @package EasyWeChat\MiniProgram\Shop\Order
 * <AUTHOR> <<EMAIL>>
 */
class ServiceProvider implements ServiceProviderInterface
{
    /**
     * @inheritDoc
     */
    public function register(Container $app)
    {
        $app['shop_order'] = function ($app) {
            return new Client($app);
        };
    }
}
