<?php

namespace EasyWeChat\MiniProgram\Shop\Spu;

use <PERSON><PERSON>\Container;
use <PERSON><PERSON>\ServiceProviderInterface;

/**
 * 自定义版交易组件及开放接口 - SPU接口
 *
 * <AUTHOR> <<EMAIL>>
 * @package EasyWeChat\MiniProgram\Shop\Spu
 */
class ServiceProvider implements ServiceProviderInterface
{
    /**
     * {@inheritdoc}
     */
    public function register(Container $app)
    {
        $app['shop_spu'] = function ($app) {
            return new Client($app);
        };
    }
}
