<?php

/*
 * This file is part of the overtrue/wechat.
 *
 */

namespace EasyWeChat\MiniProgram\Business;

use <PERSON>mple\Container;
use <PERSON><PERSON>\ServiceProviderInterface;

/**
 * Class ServiceProvider.
 *
 * <AUTHOR> <<EMAIL>>
 */
class ServiceProvider implements ServiceProviderInterface
{
    /**
     * {@inheritdoc}.
     */
    public function register(Container $app)
    {
        $app['business'] = function ($app) {
            return new Client($app);
        };
    }
}
