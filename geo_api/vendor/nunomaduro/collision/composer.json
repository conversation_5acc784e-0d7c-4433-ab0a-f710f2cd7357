{"name": "nunomaduro/collision", "description": "Cli error handling for console/command-line PHP applications.", "keywords": ["console", "command-line", "php", "cli", "error", "handling", "laravel-zero", "laravel", "artisan", "symfony"], "license": "MIT", "support": {"issues": "https://github.com/nunomaduro/collision/issues", "source": "https://github.com/nunomaduro/collision"}, "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.0.0", "filp/whoops": "^2.14.5", "symfony/console": "^6.0.2"}, "require-dev": {"brianium/paratest": "^6.4.1", "laravel/framework": "^9.26.1", "laravel/pint": "^1.1.1", "nunomaduro/larastan": "^1.0.3", "nunomaduro/mock-final-classes": "^1.1.0", "orchestra/testbench": "^7.7", "phpunit/phpunit": "^9.5.23", "spatie/ignition": "^1.4.1"}, "autoload-dev": {"psr-4": {"Tests\\Unit\\": "tests/Unit", "Tests\\FakeProgram\\": "tests/FakeProgram", "Tests\\": "tests/LaravelApp/tests", "App\\": "tests/LaravelApp/app/"}}, "minimum-stability": "dev", "prefer-stable": true, "autoload": {"psr-4": {"NunoMaduro\\Collision\\": "src/"}}, "config": {"preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "extra": {"branch-alias": {"dev-develop": "6.x-dev"}, "laravel": {"providers": ["NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider"]}}, "scripts": {"lint": "pint -v", "test:lint": "pint --test -v", "test:types": "phpstan analyse --ansi", "test:unit": "phpunit --colors=always", "test": ["@test:lint", "@test:types", "@test:unit"]}}