{"name": "repeat-element", "description": "Create an array by repeating the given value n times.", "version": "1.1.4", "homepage": "https://github.com/jonschlinkert/repeat-element", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/repeat-element", "bugs": {"url": "https://github.com/jonschlinkert/repeat-element/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"benchmarked": "^2.0.0", "chalk": "^2.4.1", "glob": "^7.1.2", "gulp-format-md": "^1.0.0", "minimist": "^1.2.0", "mocha": "^3.5.3"}, "keywords": ["array", "element", "repeat", "string"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}}