{"name": "timsort", "version": "0.3.0", "author": {"name": "<PERSON>", "url": "http://mziccard.me/"}, "description": "TimSort: Fast Sorting for Node.js", "homepage": "https://github.com/mziccard/node-timsort", "main": "index.js", "directories": {"test": "./test", "benchmark": "./benchmark"}, "dependencies": {}, "devDependencies": {"assert": "~1.3.0", "babel-eslint": "^4.0.5", "eslint": "^1.1.0", "grunt": "^0.4.5", "grunt-babel": "^5.0.1", "grunt-banner": "^0.5.0", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-uglify": "^0.9.1", "mocha": "~2.2.5"}, "scripts": {"test": "mocha --timeout 5000", "lint": "eslint <PERSON>le.js src/ test/ benchmark/index.js", "benchmark": "node benchmark/index.js"}, "repository": {"type": "git", "url": "https://github.com/mziccard/node-timsort.git"}, "keywords": ["sort", "compare", "<PERSON><PERSON><PERSON>", "algorithm", "python", "performance"], "license": "MIT", "bugs": {"url": "https://github.com/mziccard/node-timsort/issues"}}