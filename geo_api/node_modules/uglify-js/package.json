{"name": "uglify-js", "description": "JavaScript parser, mangler/compressor and beautifier toolkit", "author": "<PERSON><PERSON> <<EMAIL>> (http://lisperator.net/)", "license": "BSD-2-<PERSON><PERSON>", "version": "3.4.10", "engines": {"node": ">=0.8.0"}, "maintainers": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>> (http://lisperator.net/)"], "repository": "mishoo/UglifyJS2", "main": "tools/node.js", "bin": {"uglifyjs": "bin/uglifyjs"}, "files": ["bin", "lib", "tools", "LICENSE"], "dependencies": {"commander": "~2.19.0", "source-map": "~0.6.1"}, "devDependencies": {"acorn": "~6.1.1", "semver": "~5.6.0"}, "scripts": {"test": "node test/run-tests.js"}, "keywords": ["cli", "compress", "compressor", "ecma", "ecmascript", "es", "es5", "javascript", "js", "jsmin", "min", "minification", "minifier", "minify", "optimize", "optimizer", "pack", "packer", "parse", "parser", "uglifier", "uglify"]}