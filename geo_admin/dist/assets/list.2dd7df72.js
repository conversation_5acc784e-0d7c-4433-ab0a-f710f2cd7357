import{_ as B,c as V,a as y,b as l,w as o,j as D,k as N,f as p,r,l as K,o as m,m as z,e as w,d as E,g as s,t as h}from"./index.ef8171f5.js";const M={data(){return{query:{name:"",remarks:"",major:"",is_open:"",is_show:"",page:1,size:10},loading:!1,total:0,list:[]}},created(){this.updateInit()},methods:{updateInit(){this.query.page=1,this.update()},async update(){this.loading=!0;try{const n=await this.$http.get("/exam",{params:this.query});this.list=n.data.list,this.total=n.data.total}catch(n){console.error(n)}this.loading=!1},async save(n){try{const e=await this.$http.post(`/exam/${n.id}`,n);if(e.code>=0)this.$message.success("\u64CD\u4F5C\u6210\u529F\uFF01"),this.update();else throw e.msg}catch(e){console.error(e)}},async del(n){try{await this.$confirm("\u6B64\u64CD\u4F5C\u5C06\u6C38\u4E45\u5220\u9664\u8BE5\u6570\u636E, \u662F\u5426\u7EE7\u7EED?","\u63D0\u793A",{type:"warning","show-close":!1})}catch{return}try{const e=await this.$http.delete(`/exam/${n.id}`);if(console.warn(e),e.code>=0)this.$message.success("\u64CD\u4F5C\u6210\u529F\uFF01"),this.update();else throw e.msg}catch(e){console.error(e)}}}},A={class:"query-form-box"},L={class:"query-form"},P=s("\u5168\u90E8"),R=s("\u672A\u5F00\u542F"),T=s("\u5F00\u542F"),G=s("\u5168\u90E8"),H=s("\u4E0D\u663E\u793A"),J=s("\u663E\u793A"),O={class:"query-btn"},Q={class:"text-warp"},W=s("\u91CD\u7F6E "),X=s("\u67E5\u8BE2"),Y=s("\u65B0\u5EFA"),Z=s("\u4FDD\u5B58"),ee={key:1},te={key:1},le=s("\u7F16\u8F91 "),oe=s(" \u7BA1\u7406\u9898\u76EE "),ne=s("\u5220\u9664"),ae={class:"float-right"};function re(n,e,se,ie,a,i){const g=r("el-input"),f=r("el-form-item"),U=r("ex-major-select"),c=r("el-radio"),q=r("el-radio-group"),C=r("el-form"),_=r("el-button"),I=r("el-divider"),d=r("el-table-column"),b=r("InfoFilled"),v=r("el-icon"),k=r("el-tooltip"),x=r("el-switch"),j=r("el-table"),F=r("el-pagination"),$=r("ex-exam-form"),S=K("loading");return m(),V("div",null,[y("div",A,[y("div",L,[l(C,{onSubmit:e[10]||(e[10]=D(()=>{},["prevent"])),model:a.query,ref:"queryForm",class:"query-form",inline:"","inline-message":""},{default:o(()=>[l(f,{label:"\u9898\u5E93\u540D\u79F0",prop:"name"},{default:o(()=>[l(g,{clearable:"",onKeydown:e[0]||(e[0]=z(t=>i.updateInit(),["enter"])),placeholder:"\u8BF7\u8F93\u5165\u9898\u5E93\u540D\u79F0",modelValue:a.query.name,"onUpdate:modelValue":e[1]||(e[1]=t=>a.query.name=t)},null,8,["modelValue"])]),_:1}),l(f,{label:"\u5907\u6CE8",prop:"remarks"},{default:o(()=>[l(g,{clearable:"",onKeydown:e[2]||(e[2]=z(t=>i.updateInit(),["enter"])),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",modelValue:a.query.remarks,"onUpdate:modelValue":e[3]||(e[3]=t=>a.query.remarks=t)},null,8,["modelValue"])]),_:1}),l(f,{label:"\u4E13\u4E1A",prop:"major"},{default:o(()=>[l(U,{modelValue:a.query.major,"onUpdate:modelValue":e[4]||(e[4]=t=>a.query.major=t),onChange:e[5]||(e[5]=t=>i.updateInit()),placeholder:"\u8BF7\u9009\u62E9\u4E13\u4E1A"},null,8,["modelValue"])]),_:1}),l(f,{label:"\u5F00\u542F\u72B6\u6001",prop:"is_open"},{default:o(()=>[l(q,{modelValue:a.query.is_open,"onUpdate:modelValue":e[6]||(e[6]=t=>a.query.is_open=t),onChange:e[7]||(e[7]=t=>i.updateInit())},{default:o(()=>[l(c,{label:""},{default:o(()=>[P]),_:1}),l(c,{label:0},{default:o(()=>[R]),_:1}),l(c,{label:1},{default:o(()=>[T]),_:1})]),_:1},8,["modelValue"])]),_:1}),l(f,{label:"\u663E\u793A\u72B6\u6001",prop:"is_show"},{default:o(()=>[l(q,{modelValue:a.query.is_show,"onUpdate:modelValue":e[8]||(e[8]=t=>a.query.is_show=t),onChange:e[9]||(e[9]=t=>i.updateInit())},{default:o(()=>[l(c,{label:""},{default:o(()=>[G]),_:1}),l(c,{label:0},{default:o(()=>[H]),_:1}),l(c,{label:1},{default:o(()=>[J]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),y("div",O,[y("div",Q,[l(_,{icon:"RefreshLeft",onClick:e[11]||(e[11]=t=>{n.$refs.queryForm.resetFields(),i.updateInit()})},{default:o(()=>[W]),_:1}),l(_,{icon:"Search",type:"primary",onClick:e[12]||(e[12]=t=>i.updateInit())},{default:o(()=>[X]),_:1})])])]),l(I,{style:{"margin-top":"0"}}),l(C,{inline:""},{default:o(()=>[n.$ispower("30001")?(m(),p(f,{key:0},{default:o(()=>[l(_,{icon:"Plus",type:"primary",onClick:e[13]||(e[13]=t=>n.$refs["ex-exam-form"].show())},{default:o(()=>[Y]),_:1})]),_:1})):w("",!0)]),_:1}),N((m(),p(j,{data:a.list,border:"","row-key":"id","header-cell-style":{backgroundColor:"#FAFBFD"}},{default:o(()=>[l(d,{type:"index",label:"\u5E8F\u53F7",width:"55",align:"center",index:t=>t+(a.query.page-1)*a.query.size+1},null,8,["index"]),l(d,{prop:"major",label:"\u4E13\u4E1A","min-width":"130"}),l(d,{prop:"name",label:"\u9898\u5E93\u540D\u79F0","min-width":""}),l(d,{prop:"remarks",label:"\u5907\u6CE8"}),l(d,{prop:"item_count",label:"\u9898\u76EE\u6570\u91CF",width:"100",align:"center"}),l(d,{prop:"created_at",label:"\u6DFB\u52A0\u65F6\u95F4",width:"190"}),l(d,{prop:"updated_at",label:"\u6700\u65B0\u66F4\u65B0\u65F6\u95F4",width:"190"}),l(d,{fixed:"right",prop:"sort",label:"\u6392\u5E8F",width:"120",align:"center"},E({header:o(t=>[s(h(t.column.label)+" ",1),l(k,{class:"box-item",effect:"dark",content:"\u6570\u503C\u8D8A\u5927\u8D8A\u9760\u524D",placement:"top"},{default:o(()=>[l(v,null,{default:o(()=>[l(b)]),_:1})]),_:1})]),_:2},[n.$ispower("30001")?{name:"default",fn:o(t=>[l(g,{clearable:"",modelValue:t.row.sort,"onUpdate:modelValue":u=>t.row.sort=u},{suffix:o(()=>[l(_,{size:"small",link:"",type:"primary",onClick:u=>i.save(t.row)},{default:o(()=>[Z]),_:2},1032,["onClick"])]),_:2},1032,["modelValue","onUpdate:modelValue"])])}:void 0]),1024),l(d,{fixed:"right",prop:"is_open",label:"\u5F00\u542F\u72B6\u6001",width:"100",align:"center"},{header:o(t=>[s(h(t.column.label)+" ",1),l(k,{class:"box-item",effect:"dark",content:"\u662F\u5426\u5F00\u542F\u7EC3\u4E60",placement:"top"},{default:o(()=>[l(v,null,{default:o(()=>[l(b)]),_:1})]),_:1})]),default:o(t=>[n.$ispower("30001")?(m(),p(x,{key:0,modelValue:t.row.is_open,"onUpdate:modelValue":u=>t.row.is_open=u,"active-value":1,"inactive-value":0,onChange:u=>i.save(t.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])):(m(),V("span",ee,h(t.row.is_open==1?"\u5F00\u542F":"\u5173\u95ED"),1))]),_:1}),l(d,{fixed:"right",prop:"is_show",label:"\u662F\u5426\u663E\u793A",width:"100",align:"center"},{header:o(t=>[s(h(t.column.label)+" ",1),l(k,{class:"box-item",effect:"dark",content:"\u662F\u5426\u5728\u5BA2\u6237\u7AEF\u663E\u793A",placement:"top"},{default:o(()=>[l(v,null,{default:o(()=>[l(b)]),_:1})]),_:1})]),default:o(t=>[n.$ispower("30001")?(m(),p(x,{key:0,modelValue:t.row.is_show,"onUpdate:modelValue":u=>t.row.is_show=u,"active-value":1,"inactive-value":0,onChange:u=>i.save(t.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])):(m(),V("span",te,h(t.row.is_show==1?"\u663E\u793A":"\u4E0D\u663E\u793A"),1))]),_:1}),n.$ispower("30001","30002","30003")?(m(),p(d,{key:0,fixed:"right",label:"\u64CD\u4F5C",align:"center",width:"250"},{default:o(t=>[n.$ispower("30001")?(m(),p(_,{key:0,size:"small",link:"",type:"primary",icon:"Edit",onClick:u=>n.$refs["ex-exam-form"].show(t.row.id)},{default:o(()=>[le]),_:2},1032,["onClick"])):w("",!0),n.$ispower("30003")?(m(),p(_,{key:1,size:"small",link:"",type:"primary",icon:"Memo",onClick:u=>n.$router.push(`/exam/items?exam_id=${t.row.id}`)},{default:o(()=>[oe]),_:2},1032,["onClick"])):w("",!0),n.$ispower("30002")?(m(),p(_,{key:2,size:"small",link:"",type:"primary",icon:"Delete",onClick:u=>i.del(t.row)},{default:o(()=>[ne]),_:2},1032,["onClick"])):w("",!0)]),_:1})):w("",!0)]),_:1},8,["data"])),[[S,a.loading]]),y("p",ae,[l(F,{"current-page":a.query.page,"onUpdate:current-page":e[14]||(e[14]=t=>a.query.page=t),"page-size":a.query.size,"onUpdate:page-size":e[15]||(e[15]=t=>a.query.size=t),background:"",layout:"total, sizes, prev, pager, next, jumper",total:a.total,onSizeChange:e[16]||(e[16]=t=>i.updateInit()),onCurrentChange:e[17]||(e[17]=t=>i.update())},null,8,["current-page","page-size","total"])]),l($,{ref:"ex-exam-form",onSuccess:e[18]||(e[18]=t=>i.update())},null,512)])}const ue=B(M,[["render",re]]);export{ue as default};
