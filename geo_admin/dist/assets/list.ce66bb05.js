import{_ as C,c as f,b as r,w as a,k as $,f as s,a as v,r as l,l as z,o as i,e as d,F as q,g as m}from"./index.ef8171f5.js";const x={data(){return{query:{name:"",page:1,size:10},loading:!1,total:0,list:[]}},created(){this.updateInit()},methods:{id_card(t){return t?t.replace(/^(.{6})(?:\w+)(.{4})$/,"$1******$2"):""},updateInit(){this.query.page=1,this.update()},async update(){this.loading=!0;try{const t=await this.$http.get("/organ/tree",{params:this.query});this.list=t.data.tree,this.total=t.data.total}catch(t){console.error(t)}this.loading=!1},async del(t){try{await this.$confirm("\u6B64\u64CD\u4F5C\u5C06\u6C38\u4E45\u5220\u9664\u8BE5\u6570\u636E, \u662F\u5426\u7EE7\u7EED?","\u63D0\u793A",{type:"warning","show-close":!1})}catch{return}try{const e=await this.$http.get(`/organ/del/${t.id}`);if(console.warn(e),e.code>=0)this.$message.success("\u64CD\u4F5C\u6210\u529F\uFF01"),this.update();else throw e.msg}catch(e){console.error(e)}}}},B=m("\u65B0\u5EFA "),F=m("\u6DFB\u52A0\u5B50\u673A\u6784 "),D=m("\u7F16\u8F91 "),N=m("\u5220\u9664"),V={class:"float-right"};function I(t,e,E,P,n,u){const p=l("el-button"),_=l("el-form-item"),y=l("el-form"),c=l("el-table-column"),h=l("el-table"),w=l("el-pagination"),k=l("ex-organ-form"),b=z("loading");return i(),f("div",null,[r(y,{inline:""},{default:a(()=>[r(_,null,{default:a(()=>[t.$ispower("10001")?(i(),s(p,{key:0,icon:"Plus",type:"primary",onClick:e[0]||(e[0]=o=>t.$refs["ex-organ-form"].show())},{default:a(()=>[B]),_:1})):d("",!0)]),_:1})]),_:1}),$((i(),s(h,{"default-expand-all":"",data:n.list,border:"","row-key":"id","header-cell-style":{backgroundColor:"#FAFBFD"}},{default:a(()=>[r(c,{type:"index",label:"\u5E8F\u53F7",width:"55",align:"center",index:o=>o+(n.query.page-1)*n.query.size+1},null,8,["index"]),r(c,{prop:"name",label:"\u673A\u6784\u540D\u79F0","min-width":"130"}),r(c,{prop:"created_at",label:"\u6DFB\u52A0\u65F6\u95F4",width:"190"}),r(c,{fixed:"right",label:"\u64CD\u4F5C",align:"center",width:"250"},{default:a(o=>[t.$ispower("10001")?(i(),s(p,{key:0,size:"small",link:"",type:"primary",icon:"Plus",onClick:g=>t.$refs["ex-organ-form"].show(null,o.row.id)},{default:a(()=>[F]),_:2},1032,["onClick"])):d("",!0),o.row.name!="\u5E73\u53F0\u7BA1\u7406\u5458"?(i(),f(q,{key:1},[t.$ispower("10001")?(i(),s(p,{key:0,size:"small",link:"",type:"primary",icon:"Edit",onClick:g=>t.$refs["ex-organ-form"].show(o.row.id)},{default:a(()=>[D]),_:2},1032,["onClick"])):d("",!0),t.$ispower("10002")?(i(),s(p,{key:1,size:"small",link:"",type:"primary",icon:"Delete",onClick:g=>u.del(o.row)},{default:a(()=>[N]),_:2},1032,["onClick"])):d("",!0)],64)):d("",!0)]),_:1})]),_:1},8,["data"])),[[b,n.loading]]),v("p",V,[r(w,{"current-page":n.query.page,"onUpdate:current-page":e[1]||(e[1]=o=>n.query.page=o),"page-size":n.query.size,"onUpdate:page-size":e[2]||(e[2]=o=>n.query.size=o),background:"",layout:"total",total:n.total,onSizeChange:e[3]||(e[3]=o=>u.updateInit()),onCurrentChange:e[4]||(e[4]=o=>u.update())},null,8,["current-page","page-size","total"])]),r(k,{ref:"ex-organ-form",onSuccess:e[5]||(e[5]=o=>u.update())},null,512)])}const U=C(x,[["render",I]]);export{U as default};
