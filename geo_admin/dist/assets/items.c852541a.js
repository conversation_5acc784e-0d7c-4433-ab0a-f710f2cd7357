import{_ as S,c as V,a as y,b as t,w as o,j as B,k as K,f as q,r,l as N,o as g,m as c,F as j,n as E,t as x,g as s}from"./index.ef8171f5.js";const P={data(){return{query:{title:"",parent_id:"",is_show:"",is_search:"",is_exam:"",type:"",page:1,size:10,exam_id:this.$route.query.exam_id},types:[{value:0,label:"\u5355\u9009\u9898"},{value:1,label:"\u591A\u9009\u9898"},{value:2,label:"\u5224\u65AD\u9898"},{value:3,label:"\u5B9E\u4F8B\u9898"}],loading:!1,total:0,list:[],selectList:[]}},created(){this.updateInit()},methods:{updateInit(){this.query.page=1,this.update()},async update(){this.loading=!0;try{const i=await this.$http.get("/item",{params:this.query});this.list=i.data.list,this.total=i.data.total}catch(i){console.error(i)}this.loading=!1},async save(i){try{const e=await this.$http.post(`/item/${i.id}`,i);if(e.code>=0)this.$message.success("\u64CD\u4F5C\u6210\u529F\uFF01"),this.update();else throw e.msg}catch(e){console.error(e)}},async del(i=[]){try{await this.$confirm("\u6B64\u64CD\u4F5C\u5C06\u6C38\u4E45\u5220\u9664\u8BE5\u6570\u636E, \u662F\u5426\u7EE7\u7EED?","\u63D0\u793A",{type:"warning","show-close":!1})}catch{return}try{const e=await this.$http.post("/item/del",{ids:i});if(console.warn(e),e.code>=0)this.$message.success("\u64CD\u4F5C\u6210\u529F\uFF01"),this.update();else throw e.msg}catch(e){console.error(e)}},async showLoading(i){console.warn(i),this.loading=this.$loading()},async closeLoading(i){console.warn("\u7ED3\u675F"),console.warn(i),this.loading.close(),this.updateInit()}}},A={class:"query-form-box"},M={class:"query-form"},R=s("\u5168\u90E8"),T=s("\u4E0D\u663E\u793A"),G=s("\u663E\u793A"),H=s("\u5168\u90E8"),J=s("\u5426"),O=s("\u662F"),Q=s("\u5168\u90E8"),W=s("\u5426"),X=s("\u662F"),Y={class:"query-btn"},Z=s("\u91CD\u7F6E"),$=s("\u67E5\u8BE2"),ee=s("\u65B0\u5EFA"),le=s("\u6279\u91CF\u5220\u9664"),te=s("\u5BFC\u5165"),oe=s("\u4E0B\u8F7D\u5BFC\u5165\u6A21\u677F"),ne=s("\u7F16\u8F91 "),ae=s("\u5220\u9664 "),ie={class:"float-right"};function se(i,e,re,de,n,a){const f=r("el-input"),p=r("el-form-item"),b=r("el-option"),C=r("el-select"),m=r("el-radio"),h=r("el-radio-group"),v=r("el-form"),_=r("el-button"),k=r("el-divider"),U=r("el-upload"),I=r("el-link"),d=r("el-table-column"),w=r("el-switch"),z=r("el-table"),L=r("el-pagination"),D=r("ex-item-form"),F=N("loading");return g(),V("div",null,[y("div",A,[y("div",M,[t(v,{onSubmit:e[17]||(e[17]=B(()=>{},["prevent"])),model:n.query,ref:"queryForm",class:"query-form",inline:""},{default:o(()=>[t(p,{label:"\u9898\u76EEID",prop:"id"},{default:o(()=>[t(f,{clearable:"",readonly:"",modelValue:n.query.id,"onUpdate:modelValue":e[0]||(e[0]=l=>n.query.id=l)},null,8,["modelValue"])]),_:1}),t(p,{label:"\u9898\u76EE\u540D\u79F0",prop:"title"},{default:o(()=>[t(f,{clearable:"",onKeydown:e[1]||(e[1]=c(l=>a.updateInit(),["enter"])),placeholder:"\u8BF7\u8F93\u5165\u9898\u76EE\u540D\u79F0",modelValue:n.query.title,"onUpdate:modelValue":e[2]||(e[2]=l=>n.query.title=l)},null,8,["modelValue"])]),_:1}),t(p,{label:"\u9898\u76EE\u63CF\u8FF0",prop:"info"},{default:o(()=>[t(f,{clearable:"",onKeydown:e[3]||(e[3]=c(l=>a.updateInit(),["enter"])),placeholder:"\u8BF7\u8F93\u5165\u9898\u76EE\u540D\u79F0",modelValue:n.query.info,"onUpdate:modelValue":e[4]||(e[4]=l=>n.query.info=l)},null,8,["modelValue"])]),_:1}),t(p,{label:"\u9898\u76EE\u89E3\u6790",prop:"analysis"},{default:o(()=>[t(f,{clearable:"",onKeydown:e[5]||(e[5]=c(l=>a.updateInit(),["enter"])),placeholder:"\u8BF7\u8F93\u5165\u9898\u76EE\u89E3\u6790",modelValue:n.query.analysis,"onUpdate:modelValue":e[6]||(e[6]=l=>n.query.analysis=l)},null,8,["modelValue"])]),_:1}),t(p,{label:"\u9898\u76EEParentId",prop:"parent_id"},{default:o(()=>[t(f,{clearable:"",onKeydown:e[7]||(e[7]=c(l=>a.updateInit(),["enter"])),placeholder:"\u8BF7\u8F93\u5165parent_id",modelValue:n.query.parent_id,"onUpdate:modelValue":e[8]||(e[8]=l=>n.query.parent_id=l)},null,8,["modelValue"])]),_:1}),t(p,{label:"\u9898\u76EE\u7C7B\u578B",prop:"type"},{default:o(()=>[t(C,{clearable:"",filterable:"",onChange:e[9]||(e[9]=l=>a.updateInit()),modelValue:n.query.type,"onUpdate:modelValue":e[10]||(e[10]=l=>n.query.type=l),placeholder:"\u8BF7\u9009\u62E9\u9898\u76EE\u7C7B\u578B"},{default:o(()=>[t(b,{label:"\u5168\u90E8",value:""}),(g(!0),V(j,null,E(n.types,(l,u)=>(g(),q(b,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(p,{label:"\u663E\u793A\u72B6\u6001",prop:"is_show"},{default:o(()=>[t(h,{modelValue:n.query.is_show,"onUpdate:modelValue":e[11]||(e[11]=l=>n.query.is_show=l),onChange:e[12]||(e[12]=l=>a.updateInit())},{default:o(()=>[t(m,{label:""},{default:o(()=>[R]),_:1}),t(m,{label:0},{default:o(()=>[T]),_:1}),t(m,{label:1},{default:o(()=>[G]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(p,{label:"\u662F\u5426\u652F\u6301\u641C\u7D22",prop:"is_search"},{default:o(()=>[t(h,{modelValue:n.query.is_search,"onUpdate:modelValue":e[13]||(e[13]=l=>n.query.is_search=l),onChange:e[14]||(e[14]=l=>a.updateInit())},{default:o(()=>[t(m,{label:""},{default:o(()=>[H]),_:1}),t(m,{label:0},{default:o(()=>[J]),_:1}),t(m,{label:1},{default:o(()=>[O]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(p,{label:"\u662F\u5426\u652F\u6301\u8003\u8BD5",prop:"is_exam"},{default:o(()=>[t(h,{modelValue:n.query.is_exam,"onUpdate:modelValue":e[15]||(e[15]=l=>n.query.is_exam=l),onChange:e[16]||(e[16]=l=>a.updateInit())},{default:o(()=>[t(m,{label:""},{default:o(()=>[Q]),_:1}),t(m,{label:0},{default:o(()=>[W]),_:1}),t(m,{label:1},{default:o(()=>[X]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),y("div",Y,[t(_,{icon:"RefreshLeft",onClick:e[18]||(e[18]=l=>{i.$refs.queryForm.resetFields(),a.updateInit()})},{default:o(()=>[Z]),_:1}),t(_,{icon:"Search",type:"primary",onClick:e[19]||(e[19]=l=>a.updateInit())},{default:o(()=>[$]),_:1})])]),t(k,{style:{"margin-top":"0"}}),t(v,{inline:""},{default:o(()=>[t(p,null,{default:o(()=>[t(_,{icon:"Plus",type:"primary",onClick:e[20]||(e[20]=l=>i.$refs["ex-item-form"].show())},{default:o(()=>[ee]),_:1}),t(_,{disabled:n.selectList.length<=0,icon:"Delete",type:"danger",onClick:e[21]||(e[21]=l=>a.del(n.selectList.map(u=>u.id)))},{default:o(()=>[le]),_:1},8,["disabled"])]),_:1}),t(p,{class:"float-right"},{default:o(()=>[t(U,{accept:".xlsx",action:`/api/item/import/${i.$route.query.exam_id}`,"show-file-list":!1,"before-upload":a.showLoading,"on-success":a.closeLoading},{default:o(()=>[t(_,{icon:"Upload",type:"primary"},{default:o(()=>[te]),_:1})]),_:1},8,["action","before-upload","on-success"]),t(I,{style:{"margin-left":"10px"},type:"primary",href:"/\u9898\u76EE\u5BFC\u5165\u6A21\u677F.xlsx"},{default:o(()=>[oe]),_:1})]),_:1})]),_:1}),K((g(),q(z,{data:n.list,border:"","row-key":"id","header-cell-style":{backgroundColor:"#FAFBFD"},onSelectionChange:e[22]||(e[22]=l=>n.selectList=l)},{default:o(()=>[t(d,{"reserve-selection":"",type:"selection",width:"55",align:"center"}),t(d,{type:"index",label:"\u5E8F\u53F7",width:"55",align:"center",index:l=>l+(n.query.page-1)*n.query.size+1},null,8,["index"]),t(d,{prop:"id",width:"100",label:"\u9898\u76EEID"}),t(d,{prop:"parent_id",width:"100",label:"parent_id"}),t(d,{prop:"view_count",width:"100",label:"\u6D4F\u89C8\u6B21\u6570"}),t(d,{prop:"type",label:"\u9898\u76EE\u7C7B\u578B",width:"100",align:"center"},{default:o(l=>[y("span",null,x(n.types.find(u=>u.value==l.row.type).label),1)]),_:1}),t(d,{prop:"title","min-width":"200px",label:"\u9898\u76EE\u6807\u9898","show-overflow-tooltip":""}),t(d,{prop:"info","min-width":"200px",label:"\u63CF\u8FF0","show-overflow-tooltip":""}),t(d,{prop:"length",label:"\u9009\u9879\u6570\u91CF",width:"55",align:"center"},{default:o(l=>[s(x(l.row.options.data.length),1)]),_:1}),t(d,{prop:"created_at",label:"\u6DFB\u52A0\u65F6\u95F4",width:"190"}),t(d,{prop:"updated_at",label:"\u6700\u65B0\u66F4\u65B0\u65F6\u95F4",width:"190"}),t(d,{fixed:"right",prop:"is_show",label:"\u662F\u5426\u663E\u793A",width:"100",align:"center"},{default:o(l=>[t(w,{modelValue:l.row.is_show,"onUpdate:modelValue":u=>l.row.is_show=u,"active-value":1,"inactive-value":0,onChange:u=>a.save(l.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),t(d,{fixed:"right",prop:"is_search",label:"\u53EF\u641C\u7D22",width:"80",align:"center"},{default:o(l=>[t(w,{modelValue:l.row.is_search,"onUpdate:modelValue":u=>l.row.is_search=u,"active-value":1,"inactive-value":0,onChange:u=>a.save(l.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),t(d,{fixed:"right",prop:"is_exam",label:"\u53EF\u8003\u8BD5",width:"80",align:"center"},{default:o(l=>[t(w,{modelValue:l.row.is_exam,"onUpdate:modelValue":u=>l.row.is_exam=u,"active-value":1,"inactive-value":0,onChange:u=>a.save(l.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),t(d,{fixed:"right",label:"\u64CD\u4F5C",align:"center",width:"180"},{default:o(l=>[t(_,{size:"small",link:"",type:"primary",icon:"Edit",onClick:u=>i.$refs["ex-item-form"].show(l.row.id)},{default:o(()=>[ne]),_:2},1032,["onClick"]),t(_,{size:"small",link:"",type:"primary",icon:"Delete",onClick:u=>a.del([l.row.id])},{default:o(()=>[ae]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[F,n.loading]]),y("p",ie,[t(L,{"current-page":n.query.page,"onUpdate:current-page":e[23]||(e[23]=l=>n.query.page=l),"page-size":n.query.size,"onUpdate:page-size":e[24]||(e[24]=l=>n.query.size=l),background:"",layout:"total, sizes, prev, pager, next, jumper",total:n.total,onSizeChange:e[25]||(e[25]=l=>a.updateInit()),onCurrentChange:e[26]||(e[26]=l=>a.update())},null,8,["current-page","page-size","total"])]),t(D,{ref:"ex-item-form",onSuccess:e[27]||(e[27]=l=>a.update())},null,512)])}const pe=S(P,[["render",se]]);export{pe as default};
