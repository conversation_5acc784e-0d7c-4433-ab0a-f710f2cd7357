import{_,f as i,w as r,r as m,o as a,b,c as h,n as f,F as x,g as d,t as $}from"./index.ef8171f5.js";const g={computed:{list(){let o=this.$router.options.routes[0].children,e=[];if(this.$route.meta.breadcrumb&&(e=this.$route.meta.breadcrumb.map(t=>({title:t,path:o.find(s=>s.meta.title==t).path}))),this.$route.meta.title){let t={title:this.$route.meta.title};e.push(t)}return e}},methods:{}},k=d("\u9996\u9875");function B(o,e,t,s,y,l){const c=m("el-breadcrumb-item"),p=m("el-breadcrumb");return a(),i(p,{style:{"margin-bottom":"15px"},replace:""},{default:r(()=>[b(c,{replace:"",to:{path:"/"}},{default:r(()=>[k]),_:1}),(a(!0),h(x,null,f(l.list,(n,u)=>(a(),i(c,{replace:"",to:u<l.list.length-1?n:null,key:u},{default:r(()=>[d($(n.title),1)]),_:2},1032,["to"]))),128))]),_:1})}const C=_(g,[["render",B]]);export{C as default};
