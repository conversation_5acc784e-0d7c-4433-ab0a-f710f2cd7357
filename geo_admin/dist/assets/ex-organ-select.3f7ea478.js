import{_ as s,f as n,r as d,o as i}from"./index.ef8171f5.js";const p={props:{value:[String,Number]},data(){return{model:"",list:[]}},watch:{value:{handler(e){this.model=e},immediate:!0}},mounted(){this.update()},methods:{async update(){this.loading=!0;try{const e=await this.$http.get("/organ/tree");this.list=e.data.tree,this.total=e.data.total}catch(e){console.error(e)}this.loading=!1},updateInput(){this.$emit("input",this.model),this.$emit("change",this.model)}}};function u(e,t,m,c,a,o){const r=d("el-tree-select");return i(),n(r,{"default-expand-all":"",clearable:"","node-key":"id",filterable:"",props:{label:"name"},"check-strictly":"",modelValue:a.model,"onUpdate:modelValue":t[0]||(t[0]=l=>a.model=l),data:a.list,"render-after-expand":!1,onChange:t[1]||(t[1]=l=>o.updateInput())},null,8,["modelValue","data"])}const f=s(p,[["render",u]]);export{f as default};
