import{_ as k,c as g,b as r,w as t,r as a,o as m,a as E,k as j,f,e as c,F as S,j as q,g as w,l as P}from"./index.ef8171f5.js";const A={props:{title:String},data(){return{isShow:!1,loading:!1,isEditPassword:!1,id:"",form:{phone:"",id_card:"",act_time:"7",real_name:"",organ_id:"",category:"",password:"123456",password2:"123456"},rules:{id_card:[{required:!0,message:"\u8BF7\u8F93\u5165\u8EAB\u4EFD\u8BC1\u53F7",trigger:"blur"}],act_time:[{required:!0,message:"\u8BF7\u8F93\u5165\u6709\u6548\u671F",trigger:"blur"}],major:[{required:!0,message:"\u8BF7\u9009\u62E9\u4E13\u4E1A",trigger:"blur"}],organ_id:[{required:!0,message:"\u8BF7\u9009\u62E9\u6240\u5C5E\u673A\u6784",trigger:"blur"}],password:[{required:!0,message:"\u8BF7\u8F93\u5165\u5BC6\u7801",trigger:"blur"}],password2:[{required:!0,message:"\u8BF7\u518D\u6B21\u8F93\u5165\u5BC6\u7801",trigger:"blur"},{validator:(s,e,u)=>{this.form.password!==e?u(new Error("\u4E24\u6B21\u8F93\u5165\u7684\u5BC6\u7801\u4E0D\u4E00\u81F4\uFF01")):u()},trigger:"blur"}]}}},computed:{isAdd(){return!this.id},dialogTitle(){return this.title?this.title:this.isAdd?"\u65B0\u5EFA":"\u7F16\u8F91"}},methods:{async show(s){this.isShow=!0,this.isEditPassword=!1,s?(this.id=s,await this.update()):(this.id="",this.form={phone:"",id_card:"",act_time:"7",real_name:"",organ_id:"",category:"",password:"123456",password2:"123456"})},async hide(){this.isShow=!1,this.isEditPassword=!1},async update(){this.loading=!0;try{const s=await this.$http.get(`/user/${this.id}`);delete s.data.act_time,this.form=s.data}catch(s){console.error(s)}this.loading=!1},async submit(){try{await this.$refs.form.validate()}catch{return}try{const s=await this.$http.post(`/user/${this.id}`,this.form);s.code>=0?(this.$message.success("\u64CD\u4F5C\u6210\u529F\uFF01"),this.$emit("success"),this.hide()):this.$message.warning(s.msg)}catch(s){console.error(s)}}}},C={class:"dialog-footer"},B=w("\u5173\u95ED"),N=w("\u4FDD\u5B58");function F(s,e,u,T,l,i){const n=a("el-input"),d=a("el-form-item"),p=a("el-option"),b=a("el-select"),h=a("ex-major-select"),V=a("ex-organ-select"),y=a("el-checkbox"),v=a("el-form"),_=a("el-button"),x=a("el-dialog"),U=P("loading");return m(),g("div",null,[r(x,{modelValue:l.isShow,"onUpdate:modelValue":e[13]||(e[13]=o=>l.isShow=o),title:i.dialogTitle,width:"30%","show-close":!1,"append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:t(()=>[E("span",C,[r(_,{onClick:e[11]||(e[11]=o=>i.hide())},{default:t(()=>[B]),_:1}),r(_,{type:"primary",loading:l.loading,onClick:e[12]||(e[12]=o=>i.submit())},{default:t(()=>[N]),_:1},8,["loading"])])]),default:t(()=>[j((m(),f(v,{onSubmit:e[10]||(e[10]=q(()=>{},["prevent"])),"label-width":"100px","label-position":"left",ref:"form",model:l.form,rules:l.rules},{default:t(()=>[r(d,{label:"\u624B\u673A\u53F7",prop:"phone"},{default:t(()=>[r(n,{clearable:"",modelValue:l.form.phone,"onUpdate:modelValue":e[0]||(e[0]=o=>l.form.phone=o),placeholder:"\u8BF7\u8F93\u5165\u624B\u673A\u53F7"},null,8,["modelValue"])]),_:1}),r(d,{label:"\u8EAB\u4EFD\u8BC1\u53F7",prop:"id_card"},{default:t(()=>[r(n,{clearable:"",modelValue:l.form.id_card,"onUpdate:modelValue":e[1]||(e[1]=o=>l.form.id_card=o),placeholder:"\u8BF7\u8F93\u5165\u8EAB\u4EFD\u8BC1\u53F7"},null,8,["modelValue"])]),_:1}),r(d,{label:"\u59D3\u540D",prop:"real_name"},{default:t(()=>[r(n,{clearable:"",modelValue:l.form.real_name,"onUpdate:modelValue":e[2]||(e[2]=o=>l.form.real_name=o),placeholder:"\u8BF7\u8F93\u5165\u59D3\u540D"},null,8,["modelValue"])]),_:1}),r(d,{label:"\u6240\u5C5E\u7C7B\u522B",prop:"category"},{default:t(()=>[r(b,{clearable:"",filterable:"",modelValue:l.form.category,"onUpdate:modelValue":e[3]||(e[3]=o=>l.form.category=o),placeholder:"\u8BF7\u9009\u62E9\u6240\u5C5E\u7C7B\u522B"},{default:t(()=>[r(p,{label:"\u5168\u90E8",value:""}),r(p,{label:"\u56DB\u5DDD\u4E13\u9898\uFF08550\uFF09",value:"1"}),r(p,{label:"\u56DB\u5DDD\u4E13\u9898\uFF08380\uFF09",value:"2"})]),_:1},8,["modelValue"])]),_:1}),r(d,{label:"\u4E13\u4E1A",prop:"major"},{default:t(()=>[r(h,{modelValue:l.form.major,"onUpdate:modelValue":e[4]||(e[4]=o=>l.form.major=o),placeholder:"\u8BF7\u9009\u62E9\u4E13\u4E1A"},null,8,["modelValue"])]),_:1}),r(d,{label:"\u6240\u5C5E\u673A\u6784",prop:"organ_id"},{default:t(()=>[r(V,{modelValue:l.form.organ_id,"onUpdate:modelValue":e[5]||(e[5]=o=>l.form.organ_id=o)},null,8,["modelValue"])]),_:1}),i.isAdd?(m(),f(d,{key:0,label:"\u6709\u6548\u671F",prop:"act_time"},{default:t(()=>[r(n,{clearable:"",modelValue:l.form.act_time,"onUpdate:modelValue":e[6]||(e[6]=o=>l.form.act_time=o),modelModifiers:{number:!0},type:"number",placeholder:"\u8BF7\u8F93\u5165\u6709\u6548\u671F"},null,8,["modelValue"])]),_:1})):c("",!0),i.isAdd?c("",!0):(m(),f(y,{key:1,modelValue:l.isEditPassword,"onUpdate:modelValue":e[7]||(e[7]=o=>l.isEditPassword=o),label:"\u4FEE\u6539\u5BC6\u7801"},null,8,["modelValue"])),i.isAdd||l.isEditPassword?(m(),g(S,{key:2},[r(d,{label:"\u5BC6\u7801",prop:"password"},{default:t(()=>[r(n,{clearable:"",modelValue:l.form.password,"onUpdate:modelValue":e[8]||(e[8]=o=>l.form.password=o),type:"password",placeholder:"\u8BF7\u8F93\u5165\u5BC6\u7801","show-password":""},null,8,["modelValue"])]),_:1}),r(d,{label:"\u786E\u8BA4\u5BC6\u7801",prop:"password2"},{default:t(()=>[r(n,{clearable:"",modelValue:l.form.password2,"onUpdate:modelValue":e[9]||(e[9]=o=>l.form.password2=o),type:"password",placeholder:"\u8BF7\u8F93\u5165\u5BC6\u7801","show-password":""},null,8,["modelValue"])]),_:1})],64)):c("",!0)]),_:1},8,["model","rules"])),[[U,l.loading]])]),_:1},8,["modelValue","title"])])}const M=k(A,[["render",F]]);export{M as default};
