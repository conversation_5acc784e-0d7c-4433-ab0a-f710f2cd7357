import{_ as U,c as w,a as _,b as o,w as n,j as B,k as D,f as p,r as s,l as S,o as d,m as N,e as f,g as a,t as v}from"./index.ef8171f5.js";const E={data(){return{query:{name:"",is_open:"",page:1,size:10},loading:!1,total:0,list:[]}},created(){this.updateInit()},methods:{updateInit(){this.query.page=1,this.update()},async update(){this.loading=!0;try{const l=await this.$http.get("/major/list",{params:this.query});this.list=l.data.list,this.total=l.data.total}catch(l){console.error(l)}this.loading=!1},async save(l){try{const e=await this.$http.post(`/major/save/${l.id}`,l);if(e.code>=0)this.$message.success("\u64CD\u4F5C\u6210\u529F\uFF01"),this.update();else throw e.msg}catch(e){console.error(e)}},async del(l){try{await this.$confirm("\u6B64\u64CD\u4F5C\u5C06\u6C38\u4E45\u5220\u9664\u8BE5\u6570\u636E, \u662F\u5426\u7EE7\u7EED?","\u63D0\u793A",{type:"warning","show-close":!1})}catch{return}try{const e=await this.$http.delete(`/major/${l.id}`);if(console.warn(e),e.code>=0)this.$message.success("\u64CD\u4F5C\u6210\u529F\uFF01"),this.update();else throw e.msg}catch(e){console.error(e)}}}},K={class:"query-form-box"},A={class:"query-form"},L=a("\u5168\u90E8"),M=a("\u672A\u5F00\u542F"),P=a("\u5F00\u542F"),R={class:"query-btn"},T={class:"text-warp"},G=a("\u91CD\u7F6E "),H=a("\u67E5\u8BE2"),J=a("\u65B0\u5EFA"),O={key:1},Q=a("\u7F16\u8F91 "),W=a("\u5220\u9664"),X={class:"float-right"};function Y(l,e,Z,ee,i,r){const b=s("el-input"),y=s("el-form-item"),g=s("el-radio"),k=s("el-radio-group"),h=s("el-form"),m=s("el-button"),q=s("el-divider"),u=s("el-table-column"),C=s("InfoFilled"),V=s("el-icon"),x=s("el-tooltip"),z=s("el-switch"),j=s("el-table"),I=s("el-pagination"),F=s("ex-major-form"),$=S("loading");return d(),w("div",null,[_("div",K,[_("div",A,[o(h,{onSubmit:e[4]||(e[4]=B(()=>{},["prevent"])),model:i.query,ref:"queryForm",class:"query-form",inline:"","inline-message":""},{default:n(()=>[o(y,{label:"\u4E13\u4E1A\u540D\u79F0",prop:"name"},{default:n(()=>[o(b,{clearable:"",onKeydown:e[0]||(e[0]=N(t=>r.updateInit(),["enter"])),placeholder:"\u8BF7\u8F93\u5165\u4E13\u4E1A\u540D\u79F0",modelValue:i.query.name,"onUpdate:modelValue":e[1]||(e[1]=t=>i.query.name=t)},null,8,["modelValue"])]),_:1}),o(y,{label:"\u5F00\u542F\u72B6\u6001",prop:"is_open"},{default:n(()=>[o(k,{modelValue:i.query.is_open,"onUpdate:modelValue":e[2]||(e[2]=t=>i.query.is_open=t),onChange:e[3]||(e[3]=t=>r.updateInit())},{default:n(()=>[o(g,{label:""},{default:n(()=>[L]),_:1}),o(g,{label:0},{default:n(()=>[M]),_:1}),o(g,{label:1},{default:n(()=>[P]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_("div",R,[_("div",T,[o(m,{icon:"RefreshLeft",onClick:e[5]||(e[5]=t=>{l.$refs.queryForm.resetFields(),r.updateInit()})},{default:n(()=>[G]),_:1}),o(m,{icon:"Search",type:"primary",onClick:e[6]||(e[6]=t=>r.updateInit())},{default:n(()=>[H]),_:1})])])]),o(q,{style:{"margin-top":"0"}}),o(h,{inline:""},{default:n(()=>[l.$ispower("50001")?(d(),p(y,{key:0},{default:n(()=>[o(m,{icon:"Plus",type:"primary",onClick:e[7]||(e[7]=t=>l.$refs["ex-major-form"].show())},{default:n(()=>[J]),_:1})]),_:1})):f("",!0)]),_:1}),D((d(),p(j,{data:i.list,border:"","row-key":"id","header-cell-style":{backgroundColor:"#FAFBFD"}},{default:n(()=>[o(u,{type:"index",label:"\u5E8F\u53F7",width:"55",align:"center",index:t=>t+(i.query.page-1)*i.query.size+1},null,8,["index"]),o(u,{prop:"name",label:"\u4E13\u4E1A\u540D\u79F0","min-width":"130"}),o(u,{prop:"created_at",label:"\u6DFB\u52A0\u65F6\u95F4",width:"190"}),o(u,{prop:"updated_at",label:"\u6700\u65B0\u66F4\u65B0\u65F6\u95F4",width:"190"}),o(u,{fixed:"right",prop:"is_open",label:"\u5F00\u542F\u72B6\u6001",width:"100",align:"center"},{header:n(t=>[a(v(t.column.label)+" ",1),o(x,{class:"box-item",effect:"dark",content:"\u662F\u5426\u5F00\u542F",placement:"top"},{default:n(()=>[o(V,null,{default:n(()=>[o(C)]),_:1})]),_:1})]),default:n(t=>[l.$ispower("50001")?(d(),p(z,{key:0,modelValue:t.row.is_open,"onUpdate:modelValue":c=>t.row.is_open=c,"active-value":1,"inactive-value":0,onChange:c=>r.save(t.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])):(d(),w("span",O,v(t.row.is_open==1?"\u5F00\u542F":"\u5173\u95ED"),1))]),_:1}),l.$ispower("50001","50002")?(d(),p(u,{key:0,fixed:"right",label:"\u64CD\u4F5C",align:"center",width:"250"},{default:n(t=>[l.$ispower("50001")?(d(),p(m,{key:0,size:"small",link:"",type:"primary",icon:"Edit",onClick:c=>l.$refs["ex-major-form"].show(t.row.id)},{default:n(()=>[Q]),_:2},1032,["onClick"])):f("",!0),l.$ispower("50001")?(d(),p(m,{key:1,size:"small",link:"",type:"primary",icon:"Delete",onClick:c=>r.del(t.row)},{default:n(()=>[W]),_:2},1032,["onClick"])):f("",!0)]),_:1})):f("",!0)]),_:1},8,["data"])),[[$,i.loading]]),_("p",X,[o(I,{"current-page":i.query.page,"onUpdate:current-page":e[8]||(e[8]=t=>i.query.page=t),"page-size":i.query.size,"onUpdate:page-size":e[9]||(e[9]=t=>i.query.size=t),background:"",layout:"total, sizes, prev, pager, next, jumper",total:i.total,onSizeChange:e[10]||(e[10]=t=>r.updateInit()),onCurrentChange:e[11]||(e[11]=t=>r.update())},null,8,["current-page","page-size","total"])]),o(F,{ref:"ex-major-form",onSuccess:e[12]||(e[12]=t=>r.update())},null,512)])}const oe=U(E,[["render",Y]]);export{oe as default};
