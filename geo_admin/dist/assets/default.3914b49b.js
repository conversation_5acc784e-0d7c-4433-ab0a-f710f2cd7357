import{u as S}from"./user.eb20b5fa.js";import{_ as y,c as l,a,b as t,w as n,d as k,r as o,o as r,t as u,e as B,f as C,g as c,p as I,h as N}from"./index.ef8171f5.js";const V={data(){return{user:S()}},watch:{$route:{immediate:!0,handler(e){let s=this.$route.meta.title;s||(s="\u5EFA\u5B89\u8003\u57F9"),document.title=s+" - \u7EC3\u4E60\u7B54\u9898\u7BA1\u7406\u7CFB\u7EDF"}}},mounted(){},methods:{logout(){localStorage.clear(),sessionStorage.clear(),this.$router.push("/login")},async relogin(){console.warn("\u4FEE\u6539\u5B8C\uFF1B "),await this.$alert("\u4FEE\u6539\u5B8C\u6210\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55","\u63D0\u793A",{type:"success",showClose:!1}),this.logout()},menuSelect(e){if(e=="password"){this.$refs["ex-admin-user-form"].show(this.user.info.id);return}if(e=="logout"){this.logout();return}this.$router.push(e)}}},m=e=>(I("data-v-a550c6be"),e=e(),N(),e),z={class:"default-view"},D={class:"default-view-top"},E=m(()=>a("div",{class:"logo"},"\u540E\u53F0\u7BA1\u7406\u7CFB\u7EDF",-1)),T=m(()=>a("div",{style:{"flex-grow":"1"}},null,-1)),U=c("\u6B22\u8FCE\u60A8"),j={key:0},q=c("\u4FEE\u6539\u5BC6\u7801"),A=c("\u9000\u51FA\u767B\u5F55"),F={class:"default-view-box"},G={class:"default-view-menu"},H={class:"default-view-body"},J={key:1,style:{"font-size":"18px"}};function K(e,s,L,M,i,d){const _=o("el-menu-item"),f=o("el-sub-menu"),p=o("el-menu"),h=o("ex-menu"),v=o("ex-breadcrumb"),g=o("el-page-header"),w=o("router-view"),b=o("el-card"),x=o("ex-admin-user-form");return r(),l("div",z,[a("div",D,[t(p,{mode:"horizontal",ellipsis:!1,"background-color":"#1D242E","text-color":"#fff",onSelect:d.menuSelect},{default:n(()=>[E,T,t(f,{index:"/admin"},{title:n(()=>[U,i.user.info.account?(r(),l("span",j,"\uFF0C"+u(i.user.info.account),1)):B("",!0)]),default:n(()=>[t(_,{index:"password"},{default:n(()=>[q]),_:1}),t(_,{index:"logout"},{default:n(()=>[A]),_:1})]),_:1})]),_:1},8,["onSelect"])]),a("div",F,[a("div",G,[t(h)]),a("div",H,[t(v),t(b,null,k({default:n(()=>[t(w)]),_:2},[e.$route.meta.title?{name:"header",fn:n(()=>[e.$route.meta.breadcrumb&&e.$route.meta.breadcrumb.length>0?(r(),C(g,{key:0,onBack:s[0]||(s[0]=$=>e.$router.go(-1))},{content:n(()=>[a("span",null,u(e.$route.meta.title),1)]),_:1})):(r(),l("div",J,[a("span",null,u(e.$route.meta.title),1)]))])}:void 0]),1024)])]),t(x,{title:"\u4FEE\u6539\u5BC6\u7801",ref:"ex-admin-user-form",onSuccess:s[1]||(s[1]=$=>d.relogin())},null,512)])}const Q=y(V,[["render",K],["__scopeId","data-v-a550c6be"]]);export{Q as default};
