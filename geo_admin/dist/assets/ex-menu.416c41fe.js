import{_ as h,f as t,w as e,r as s,o as a,b as o,e as u,p as w,h as x,a as d}from"./index.ef8171f5.js";const $={data(){return{userInfo:null}},mounted(){this.userInfo=JSON.parse(localStorage.userInfo)},methods:{}},r=n=>(w("data-v-5ad79b85"),n=n(),x(),n),k=r(()=>d("span",null,"\u9996\u9875",-1)),g=r(()=>d("span",null,"\u673A\u6784\u7BA1\u7406",-1)),y=r(()=>d("span",null,"\u5B66\u5458\u7BA1\u7406",-1)),v=r(()=>d("span",null,"\u9898\u5E93\u7BA1\u7406",-1)),B=r(()=>d("span",null,"\u4E13\u4E1A\u7BA1\u7406",-1)),I=r(()=>d("span",null,"\u7BA1\u7406\u5458\u7BA1\u7406",-1));function b(n,S,M,N,C,O){const c=s("OfficeBuilding"),l=s("el-icon"),_=s("el-menu-item"),p=s("User"),f=s("MessageBox"),i=s("Avatar"),m=s("el-menu");return a(),t(m,{router:"",class:"window-scrollbar","background-color":"#283342","text-color":"#fff"},{default:e(()=>[n.$ispower("1")?(a(),t(_,{key:0,index:"/"},{default:e(()=>[o(l,null,{default:e(()=>[o(c)]),_:1}),k]),_:1})):u("",!0),n.$ispower("10000")?(a(),t(_,{key:1,index:"/organ/list"},{default:e(()=>[o(l,null,{default:e(()=>[o(c)]),_:1}),g]),_:1})):u("",!0),n.$ispower("20000")?(a(),t(_,{key:2,index:"/user/list"},{default:e(()=>[o(l,null,{default:e(()=>[o(p)]),_:1}),y]),_:1})):u("",!0),n.$ispower("30000")?(a(),t(_,{key:3,index:"/exam/list"},{default:e(()=>[o(l,null,{default:e(()=>[o(f)]),_:1}),v]),_:1})):u("",!0),n.$ispower("50000")?(a(),t(_,{key:4,index:"/major/list"},{default:e(()=>[o(l,null,{default:e(()=>[o(i)]),_:1}),B]),_:1})):u("",!0),n.$ispower("40000")?(a(),t(_,{key:5,index:"/admin/user"},{default:e(()=>[o(l,null,{default:e(()=>[o(i)]),_:1}),I]),_:1})):u("",!0)]),_:1})}const A=h($,[["render",b],["__scopeId","data-v-5ad79b85"]]);export{A as default};
