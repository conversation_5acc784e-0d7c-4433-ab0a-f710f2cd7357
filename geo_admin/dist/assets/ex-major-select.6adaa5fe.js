import{_ as u,f as n,w as m,r as s,o,c,n as p,F as h}from"./index.ef8171f5.js";const _={props:{value:[String,Number]},data(){return{model:"",list:[]}},watch:{value:{handler(e){this.model=e},immediate:!0}},mounted(){this.update()},methods:{async update(){this.loading=!0;try{const e=await this.$http.get("/major/list?size=9999");this.list=e.data.list,this.total=e.data.total}catch(e){console.error(e)}this.loading=!1},updateInput(){this.$emit("input",this.model),this.$emit("change",this.model)}}};function f(e,l,g,k,a,r){const i=s("el-option"),d=s("el-select");return o(),n(d,{"default-expand-all":"",clearable:"","node-key":"id",filterable:"","check-strictly":"",modelValue:a.model,"onUpdate:modelValue":l[0]||(l[0]=t=>a.model=t),data:a.list,onChange:l[1]||(l[1]=t=>r.updateInput())},{default:m(()=>[(o(!0),c(h,null,p(a.list,t=>(o(),n(i,{key:t.name,label:t.name,value:t.name},null,8,["label","value"]))),128))]),_:1},8,["modelValue","data"])}const x=u(_,[["render",f]]);export{x as default};
