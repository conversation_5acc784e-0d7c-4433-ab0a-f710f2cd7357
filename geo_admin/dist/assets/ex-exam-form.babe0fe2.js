import{_ as g,c as w,b as l,w as r,r as i,o as u,a as b,k as V,f as k,j as x,g as f,l as v}from"./index.ef8171f5.js";const y={props:{title:String},data(){return{isShow:!1,loading:!1,id:"",form:{name:"",remarks:"",major:""},rules:{name:[{required:!0,message:"\u8BF7\u8F93\u5165\u6807\u9898",trigger:"blur"}],major:[{required:!0,message:"\u8BF7\u9009\u62E9\u4E13\u4E1A",trigger:"blur"}]}}},computed:{isAdd(){return!this.id},dialogTitle(){return this.title?this.title:this.isAdd?"\u65B0\u5EFA":"\u7F16\u8F91"}},methods:{async show(o){this.isShow=!0,o?(this.id=o,await this.update()):(this.id="",this.form={name:"",remarks:""})},async hide(){this.isShow=!1},async update(){this.loading=!0;try{const o=await this.$http.get(`/exam/${this.id}`);this.form=o.data}catch(o){console.error(o)}this.loading=!1},async submit(){try{await this.$refs.form.validate()}catch{return}try{const o=await this.$http.post(`/exam/${this.id}`,this.form);o.code>=0?(this.$message.success("\u64CD\u4F5C\u6210\u529F\uFF01"),this.$emit("success"),this.hide()):this.$message.warning(o.msg)}catch(o){console.error(o)}}}},j={class:"dialog-footer"},S=f("\u5173\u95ED"),B=f("\u4FDD\u5B58");function C(o,e,U,N,t,a){const m=i("el-input"),n=i("el-form-item"),c=i("ex-major-select"),p=i("el-form"),d=i("el-button"),h=i("el-dialog"),_=v("loading");return u(),w("div",null,[l(h,{modelValue:t.isShow,"onUpdate:modelValue":e[6]||(e[6]=s=>t.isShow=s),title:a.dialogTitle,width:"30%","show-close":!1,"append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:r(()=>[b("span",j,[l(d,{onClick:e[4]||(e[4]=s=>a.hide())},{default:r(()=>[S]),_:1}),l(d,{type:"primary",loading:t.loading,onClick:e[5]||(e[5]=s=>a.submit())},{default:r(()=>[B]),_:1},8,["loading"])])]),default:r(()=>[V((u(),k(p,{onSubmit:e[3]||(e[3]=x(()=>{},["prevent"])),"label-width":"100px","label-position":"left",ref:"form",model:t.form,rules:t.rules},{default:r(()=>[l(n,{label:"\u6807\u9898",prop:"name"},{default:r(()=>[l(m,{clearable:"",modelValue:t.form.name,"onUpdate:modelValue":e[0]||(e[0]=s=>t.form.name=s),placeholder:"\u8BF7\u8F93\u5165\u6807\u9898"},null,8,["modelValue"])]),_:1}),l(n,{label:"\u5907\u6CE8",prop:"remarks"},{default:r(()=>[l(m,{clearable:"",modelValue:t.form.remarks,"onUpdate:modelValue":e[1]||(e[1]=s=>t.form.remarks=s),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1}),l(n,{label:"\u4E13\u4E1A",prop:"major"},{default:r(()=>[l(c,{modelValue:t.form.major,"onUpdate:modelValue":e[2]||(e[2]=s=>t.form.major=s)},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[_,t.loading]])]),_:1},8,["modelValue","title"])])}const q=g(y,[["render",C]]);export{q as default};
