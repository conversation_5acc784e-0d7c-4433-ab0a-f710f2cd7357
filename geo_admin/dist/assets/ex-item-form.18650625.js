import{_ as F,c as V,b as i,w as a,r as s,o as h,a as b,f as w,e as A,k as B,F as T,n as N,j,g as c,l as q}from"./index.ef8171f5.js";var y,L=new Uint8Array(16);function P(){if(!y&&(y=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||typeof msCrypto<"u"&&typeof msCrypto.getRandomValues=="function"&&msCrypto.getRandomValues.bind(msCrypto),!y))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return y(L)}const Y=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;function G(t){return typeof t=="string"&&Y.test(t)}var n=[];for(var v=0;v<256;++v)n.push((v+256).toString(16).substr(1));function H(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=(n[t[e+0]]+n[t[e+1]]+n[t[e+2]]+n[t[e+3]]+"-"+n[t[e+4]]+n[t[e+5]]+"-"+n[t[e+6]]+n[t[e+7]]+"-"+n[t[e+8]]+n[t[e+9]]+"-"+n[t[e+10]]+n[t[e+11]]+n[t[e+12]]+n[t[e+13]]+n[t[e+14]]+n[t[e+15]]).toLowerCase();if(!G(r))throw TypeError("Stringified UUID is invalid");return r}function M(t,e,r){t=t||{};var u=t.random||(t.rng||P)();if(u[6]=u[6]&15|64,u[8]=u[8]&63|128,e){r=r||0;for(var o=0;o<16;++o)e[r+o]=u[o];return e}return H(u)}const X={props:{title:String},data(){return{isShow:!1,loading:!1,types:[{value:0,label:"\u5355\u9009\u9898"},{value:1,label:"\u591A\u9009\u9898"},{value:2,label:"\u5224\u65AD\u9898"},{value:3,label:"\u5B9E\u4F8B\u9898"}],id:"",form:{title:"",parent_id:"",info:"",type:0,options:{data:[]},analysis:"\u65E0",exam_image:[],is_show:1},rules:{title:[{required:!0,message:"\u8BF7\u8F93\u5165\u6807\u9898",trigger:"blur"}]}}},computed:{isAdd(){return!this.id},dialogTitle(){return this.title?this.title:this.isAdd?"\u65B0\u5EFA":"\u7F16\u8F91"}},mounted(){},methods:{async handleUploadSuccess(t,e,r){this.form.exam_image?this.form.exam_image.push({name:e.name,url:t.data.url}):this.form.exam_image=[{name:e.name,url:t.data.url}]},async show(t){this.isShow=!0,t?(this.id=t,await this.update()):(this.id="",this.form={exam_id:this.$route.query.exam_id,parent_id:"",title:"",info:"",type:0,exam_image:[],options:{data:[]},analysis:"\u65E0",is_show:1})},async hide(){this.isShow=!1},async refresh(){try{await this.$confirm("\u6240\u6709\u672A\u4FDD\u5B58\u7684\u6570\u636E\u5C06\u4E22\u5931\uFF0C\u786E\u5B9A\u91CD\u7F6E\u5417?","\u63D0\u793A",{type:"warning","show-close":!1})}catch{return}this.update()},async update(){this.loading=!0;try{const t=await this.$http.get(`/item/${this.id}`);this.form=t.data,this.form.exam_image||(this.form.exam_image=[])}catch(t){console.error(t)}this.loading=!1},async submit(){try{await this.$refs.form.validate()}catch{return}try{const t=await this.$http.post(`/item/${this.id}`,this.form);t.code>=0?(this.$message.success("\u64CD\u4F5C\u6210\u529F\uFF01"),this.$emit("success"),this.hide()):this.$message.warning(t.msg)}catch(t){console.error(t)}},setOptionIsYes(t){this.form.type!=1&&this.form.options.data.forEach(e=>{e.id!=t.id&&(e.is_yes=0)})},addOption(){let t=this.getOptionItem();if(this.form.type==2)if(this.form.options.data.length>=2){this.$message.warning("\u5224\u65AD\u9898\u53EA\u80FD\u6DFB\u52A02\u4E2A\u9009\u9879\uFF01");return}else{let e=["\u6B63\u786E","\u9519\u8BEF"];t.label=e[this.form.options.data.length]}this.form.options.data.push(t)},getOptionItem(t=`\u9009\u9879${this.form.options.data.length+1}`,e=0){return{id:M(),label:t,is_yes:e,is_user_select:0}},getOption(){}}},z=b("div",null,"\u70B9\u51FB\u4E0A\u4F20",-1),J=c("\u9009\u9879"),K={class:"text-right"},Q=c(" \u6DFB\u52A0\u9009\u9879"),W=c("\u5220\u9664 "),Z={class:"dialog-footer"},$=c("\u5173\u95ED"),ee=c("\u91CD\u7F6E"),te=c("\u4FDD\u5B58");function le(t,e,r,u,o,d){const p=s("el-input"),m=s("el-form-item"),x=s("el-option"),k=s("el-select"),C=s("el-switch"),U=s("el-upload"),S=s("el-divider"),I=s("el-form"),_=s("el-button"),g=s("el-table-column"),O=s("el-checkbox"),R=s("el-table"),D=s("el-dialog"),E=q("loading");return h(),V("div",null,[i(D,{modelValue:o.isShow,"onUpdate:modelValue":e[12]||(e[12]=l=>o.isShow=l),title:d.dialogTitle,width:"80%","show-close":!1,"append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:a(()=>[b("span",Z,[i(_,{onClick:e[9]||(e[9]=l=>d.hide())},{default:a(()=>[$]),_:1}),d.isAdd?A("",!0):(h(),w(_,{key:0,type:"primary",loading:o.loading,onClick:e[10]||(e[10]=l=>d.refresh())},{default:a(()=>[ee]),_:1},8,["loading"])),i(_,{type:"primary",loading:o.loading,onClick:e[11]||(e[11]=l=>d.submit())},{default:a(()=>[te]),_:1},8,["loading"])])]),default:a(()=>[B((h(),w(I,{onSubmit:e[7]||(e[7]=j(()=>{},["prevent"])),"label-width":"100px","label-position":"left",ref:"form",model:o.form,rules:o.rules},{default:a(()=>[i(m,{label:"\u9898\u76EEID",prop:"id"},{default:a(()=>[i(p,{clearable:"",readonly:"",modelValue:o.form.id,"onUpdate:modelValue":e[0]||(e[0]=l=>o.form.id=l)},null,8,["modelValue"])]),_:1}),i(m,{label:"\u9898\u76EE\u6807\u9898",prop:"title"},{default:a(()=>[i(p,{clearable:"",modelValue:o.form.title,"onUpdate:modelValue":e[1]||(e[1]=l=>o.form.title=l),maxlength:"255","show-word-limit":"",placeholder:"\u8BF7\u8F93\u5165\u9898\u76EE\u6807\u9898"},null,8,["modelValue"])]),_:1}),i(m,{label:"\u9898\u76EE\u63CF\u8FF0",prop:"info"},{default:a(()=>[i(p,{clearable:"",modelValue:o.form.info,"onUpdate:modelValue":e[2]||(e[2]=l=>o.form.info=l),maxlength:"255","show-word-limit":"",placeholder:"\u8BF7\u8F93\u5165\u9898\u76EE\u63CF\u8FF0"},null,8,["modelValue"])]),_:1}),i(m,{label:"\u9898\u76EE\u7C7B\u578B",prop:"type"},{default:a(()=>[i(k,{clearable:"",filterable:"",modelValue:o.form.type,"onUpdate:modelValue":e[3]||(e[3]=l=>o.form.type=l),placeholder:"\u8BF7\u9009\u62E9\u9898\u76EE\u7C7B\u578B"},{default:a(()=>[(h(!0),V(T,null,N(o.types,(l,f)=>(h(),w(x,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),i(m,{label:"\u662F\u5426\u663E\u793A",prop:"type"},{default:a(()=>[i(C,{modelValue:o.form.is_show,"onUpdate:modelValue":e[4]||(e[4]=l=>o.form.is_show=l),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1}),i(m,{label:"\u9898\u76EE\u56FE\u7247",prop:"exam_image"},{default:a(()=>[i(U,{action:"/api/app/upload","list-type":"picture-card","file-list":o.form.exam_image,"on-success":d.handleUploadSuccess},{default:a(()=>[z]),_:1},8,["file-list","on-success"])]),_:1}),i(m,{label:"\u9898\u76EE\u89E3\u6790",prop:"analysis"},{default:a(()=>[i(p,{clearable:"",type:"textarea",maxlength:"1000",rows:5,"show-word-limit":"",modelValue:o.form.analysis,"onUpdate:modelValue":e[5]||(e[5]=l=>o.form.analysis=l),placeholder:"\u8BF7\u8F93\u5165\u9898\u76EE\u89E3\u6790"},null,8,["modelValue"])]),_:1}),i(m,{label:"\u9898\u76EEParentId",prop:"parent_id"},{default:a(()=>[i(p,{clearable:"",placeholder:"\u8BF7\u8F93\u5165parent_id",modelValue:o.form.parent_id,"onUpdate:modelValue":e[6]||(e[6]=l=>o.form.parent_id=l)},null,8,["modelValue"])]),_:1}),i(S,null,{default:a(()=>[J]),_:1})]),_:1},8,["model","rules"])),[[E,o.loading]]),b("div",null,[b("p",K,[i(_,{link:"",type:"primary",icon:"Plus",onClick:e[8]||(e[8]=l=>d.addOption())},{default:a(()=>[Q]),_:1})]),i(R,{data:o.form.options.data,border:"","row-key":"id","header-cell-style":{backgroundColor:"#FAFBFD"}},{default:a(()=>[i(g,{type:"index",label:"\u5E8F\u53F7",width:"55",align:"center"}),i(g,{prop:"label",label:"\u9009\u9879\u6807\u9898"},{default:a(l=>[i(p,{clearable:"",maxlength:"255","show-word-limit":"",modelValue:l.row.label,"onUpdate:modelValue":f=>l.row.label=f},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),i(g,{prop:"label",label:"\u662F\u5426\u6B63\u786E\u9009\u9879",align:"center",width:"80"},{default:a(l=>[i(O,{modelValue:l.row.is_yes,"onUpdate:modelValue":f=>l.row.is_yes=f,onChange:f=>d.setOptionIsYes(l.row),"true-label":1,"false-label":"0"},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),i(g,{fixed:"right",label:"\u64CD\u4F5C",align:"center",width:"80"},{default:a(l=>[i(_,{link:"",type:"danger",icon:"Delete",onClick:f=>o.form.options.data.splice(l.$index,1)},{default:a(()=>[W]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])])]),_:1},8,["modelValue","title"])])}const ie=F(X,[["render",le]]);export{ie as default};
