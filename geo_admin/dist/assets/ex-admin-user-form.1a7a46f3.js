import{_ as v,c,b as t,w as r,r as i,o as n,a as x,k,f,e as w,F as E,j as S,g,l as U}from"./index.ef8171f5.js";const A={props:{title:String},data(){return{isShow:!1,loading:!1,id:"",isEditPassword:!1,form:{account:"",password:"",password2:"",organ_id:""},rules:{account:[{required:!0,message:"\u8BF7\u8F93\u5165\u8D26\u6237",trigger:"blur"}],password:[{required:!0,message:"\u8BF7\u8F93\u5165\u5BC6\u7801",trigger:"blur"}],organ_id:[{required:!0,message:"\u8BF7\u9009\u62E9\u6240\u5C5E\u673A\u6784",trigger:"blur"}],password2:[{required:!0,message:"\u8BF7\u518D\u6B21\u8F93\u5165\u5BC6\u7801",trigger:"blur"},{validator:(s,e,u)=>{this.form.password!==e?u(new Error("\u4E24\u6B21\u8F93\u5165\u7684\u5BC6\u7801\u4E0D\u4E00\u81F4\uFF01")):u()},trigger:"blur"}]}}},computed:{isAdd(){return!this.id},dialogTitle(){return this.title?this.title:this.isAdd?"\u65B0\u5EFA":"\u7F16\u8F91"}},mounted(){},methods:{async show(s){this.isShow=!0,this.isEditPassword=!1,console.warn(s),s?(this.id=s,await this.update()):(this.id="",this.form={account:"",password:"",password2:"",organ_id:""})},async hide(){this.isEditPassword=!1,this.isShow=!1},async update(){this.loading=!0;try{const s=await this.$http.get(`/admin/${this.id}`);console.warn(s),this.form=s.data}catch(s){console.error(s)}this.loading=!1},async submit(){try{await this.$refs.form.validate()}catch{return}try{const s=await this.$http.post(`/admin/${this.id}`,this.form);if(s.code>=0)this.$message.success("\u64CD\u4F5C\u6210\u529F\uFF01"),this.$emit("success"),this.hide();else throw s.msg}catch(s){console.error(s)}}}},P={class:"dialog-footer"},C=g("\u5173\u95ED"),q=g("\u4FDD\u5B58");function B(s,e,u,N,o,d){const m=i("el-input"),a=i("el-form-item"),h=i("ex-organ-select"),_=i("el-checkbox"),b=i("el-form"),p=i("el-button"),V=i("el-dialog"),y=U("loading");return n(),c("div",null,[t(V,{modelValue:o.isShow,"onUpdate:modelValue":e[8]||(e[8]=l=>o.isShow=l),title:d.dialogTitle,width:"30%","show-close":!1,"append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:r(()=>[x("span",P,[t(p,{onClick:e[6]||(e[6]=l=>d.hide())},{default:r(()=>[C]),_:1}),t(p,{type:"primary",loading:o.loading,onClick:e[7]||(e[7]=l=>d.submit())},{default:r(()=>[q]),_:1},8,["loading"])])]),default:r(()=>[k((n(),f(b,{onSubmit:e[5]||(e[5]=S(()=>{},["prevent"])),"label-width":"100px","label-position":"left",ref:"form",model:o.form,rules:o.rules},{default:r(()=>[t(a,{label:"\u8D26\u6237",prop:"account"},{default:r(()=>[t(m,{clearable:"",disabled:!d.isAdd,modelValue:o.form.account,"onUpdate:modelValue":e[0]||(e[0]=l=>o.form.account=l),placeholder:"\u8BF7\u8F93\u5165\u8D26\u6237"},null,8,["disabled","modelValue"])]),_:1}),t(a,{label:"\u6240\u5C5E\u673A\u6784",prop:"organ_id"},{default:r(()=>[t(h,{modelValue:o.form.organ_id,"onUpdate:modelValue":e[1]||(e[1]=l=>o.form.organ_id=l)},null,8,["modelValue"])]),_:1}),d.isAdd?w("",!0):(n(),f(_,{key:0,modelValue:o.isEditPassword,"onUpdate:modelValue":e[2]||(e[2]=l=>o.isEditPassword=l),label:"\u4FEE\u6539\u5BC6\u7801"},null,8,["modelValue"])),d.isAdd||o.isEditPassword?(n(),c(E,{key:1},[t(a,{label:"\u5BC6\u7801",prop:"password"},{default:r(()=>[t(m,{clearable:"",modelValue:o.form.password,"onUpdate:modelValue":e[3]||(e[3]=l=>o.form.password=l),type:"password",placeholder:"\u8BF7\u8F93\u5165\u5BC6\u7801","show-password":""},null,8,["modelValue"])]),_:1}),t(a,{label:"\u786E\u8BA4\u5BC6\u7801",prop:"password2"},{default:r(()=>[t(m,{clearable:"",modelValue:o.form.password2,"onUpdate:modelValue":e[4]||(e[4]=l=>o.form.password2=l),type:"password",placeholder:"\u8BF7\u8F93\u5165\u5BC6\u7801","show-password":""},null,8,["modelValue"])]),_:1})],64)):w("",!0)]),_:1},8,["model","rules"])),[[y,o.loading]])]),_:1},8,["modelValue","title"])])}const T=v(A,[["render",B]]);export{T as default};
