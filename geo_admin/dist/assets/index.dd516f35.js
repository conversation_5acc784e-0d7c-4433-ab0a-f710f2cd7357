import{_ as a,c as r,a as i,e as o,o as n}from"./index.ef8171f5.js";const u={data(){return{userInfo:null}},created(){this.userInfo=JSON.parse(localStorage.userInfo)}},l={class:"nav-box"};function d(e,s,p,v,$,m){return n(),r("div",null,[i("div",l,[e.$ispower("20000")?(n(),r("div",{key:0,class:"nav-item",onClick:s[0]||(s[0]=t=>e.$router.push("/user/list"))},"\u5B66\u5458\u7BA1\u7406 ")):o("",!0),e.$ispower("30000")?(n(),r("div",{key:1,class:"nav-item",onClick:s[1]||(s[1]=t=>e.$router.push("/exam/list"))},"\u9898\u5E93\u7BA1\u7406 ")):o("",!0),e.$ispower("40000")?(n(),r("div",{key:2,class:"nav-item",onClick:s[2]||(s[2]=t=>e.$router.push("/admin/user"))},"\u7BA1\u7406\u5458\u7BA1\u7406 ")):o("",!0)])])}const c=a(u,[["render",d]]);export{c as default};
