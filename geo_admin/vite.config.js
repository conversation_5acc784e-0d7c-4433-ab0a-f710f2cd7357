import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

const host = 'http://127.0.0.1:8000';

export default defineConfig({
  plugins: [vue()],
  server: {
    proxy: {
      '/api': {
        target: host,
        changeOrigin: true,
        rewrite: path => path.replace(/^\/api/, '/api'),
      },
      '/storage': {
        target: host,
        changeOrigin: true,
      }
    }
  }
})
