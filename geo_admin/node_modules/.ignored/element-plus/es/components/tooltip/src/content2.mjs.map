{"version": 3, "file": "content2.mjs", "sources": ["../../../../../../packages/components/tooltip/src/content.vue"], "sourcesContent": ["<template>\n  <teleport :disabled=\"!teleported\" :to=\"appendTo\">\n    <transition\n      :name=\"transitionClass\"\n      @after-leave=\"onTransitionLeave\"\n      @before-enter=\"onBeforeEnter\"\n      @after-enter=\"onAfterShow\"\n      @before-leave=\"onBeforeLeave\"\n    >\n      <el-popper-content\n        v-if=\"shouldRender\"\n        v-show=\"shouldShow\"\n        :id=\"id\"\n        ref=\"contentRef\"\n        v-bind=\"$attrs\"\n        :aria-label=\"ariaLabel\"\n        :aria-hidden=\"ariaHidden\"\n        :boundaries-padding=\"boundariesPadding\"\n        :fallback-placements=\"fallbackPlacements\"\n        :gpu-acceleration=\"gpuAcceleration\"\n        :offset=\"offset\"\n        :placement=\"placement\"\n        :popper-options=\"popperOptions\"\n        :strategy=\"strategy\"\n        :effect=\"effect\"\n        :enterable=\"enterable\"\n        :pure=\"pure\"\n        :popper-class=\"popperClass\"\n        :popper-style=\"[popperStyle, contentStyle]\"\n        :reference-el=\"referenceEl\"\n        :trigger-target-el=\"triggerTargetEl\"\n        :visible=\"shouldShow\"\n        :z-index=\"zIndex\"\n        @mouseenter=\"onContentEnter\"\n        @mouseleave=\"onContentLeave\"\n        @blur=\"onBlur\"\n        @close=\"onClose\"\n      >\n        <template v-if=\"!destroyed\">\n          <slot />\n        </template>\n      </el-popper-content>\n    </transition>\n  </teleport>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, onBeforeUnmount, ref, unref, watch } from 'vue'\nimport { onClickOutside } from '@vueuse/core'\nimport { useNamespace, usePopperContainerId } from '@element-plus/hooks'\nimport { composeEventHandlers } from '@element-plus/utils'\nimport { ElPopperContent } from '@element-plus/components/popper'\nimport { TOOLTIP_INJECTION_KEY } from './constants'\nimport { useTooltipContentProps } from './content'\n\ndefineOptions({\n  name: 'ElTooltipContent',\n  inheritAttrs: false,\n})\n\nconst props = defineProps(useTooltipContentProps)\n\nconst { selector } = usePopperContainerId()\nconst ns = useNamespace('tooltip')\n// TODO any is temporary, replace with `InstanceType<typeof ElPopperContent> | null` later\nconst contentRef = ref<any>(null)\nconst destroyed = ref(false)\nconst {\n  controlled,\n  id,\n  open,\n  trigger,\n  onClose,\n  onOpen,\n  onShow,\n  onHide,\n  onBeforeShow,\n  onBeforeHide,\n} = inject(TOOLTIP_INJECTION_KEY, undefined)!\nconst transitionClass = computed(() => {\n  return props.transition || `${ns.namespace.value}-fade-in-linear`\n})\nconst persistentRef = computed(() => {\n  // For testing, we would always want the content to be rendered\n  // to the DOM, so we need to return true here.\n  if (process.env.NODE_ENV === 'test') {\n    return true\n  }\n  return props.persistent\n})\n\nonBeforeUnmount(() => {\n  destroyed.value = true\n})\n\nconst shouldRender = computed(() => {\n  return unref(persistentRef) ? true : unref(open)\n})\n\nconst shouldShow = computed(() => {\n  return props.disabled ? false : unref(open)\n})\n\nconst appendTo = computed(() => {\n  return props.appendTo || selector.value\n})\n\nconst contentStyle = computed(() => (props.style ?? {}) as any)\n\nconst ariaHidden = computed(() => !unref(open))\n\nconst onTransitionLeave = () => {\n  onHide()\n}\n\nconst stopWhenControlled = () => {\n  if (unref(controlled)) return true\n}\n\nconst onContentEnter = composeEventHandlers(stopWhenControlled, () => {\n  if (props.enterable && unref(trigger) === 'hover') {\n    onOpen()\n  }\n})\n\nconst onContentLeave = composeEventHandlers(stopWhenControlled, () => {\n  if (unref(trigger) === 'hover') {\n    onClose()\n  }\n})\n\nconst onBeforeEnter = () => {\n  contentRef.value?.updatePopper?.()\n  onBeforeShow?.()\n}\n\nconst onBeforeLeave = () => {\n  onBeforeHide?.()\n}\n\nconst onAfterShow = () => {\n  onShow()\n  stopHandle = onClickOutside(\n    computed(() => {\n      return contentRef.value?.popperContentRef\n    }),\n    () => {\n      if (unref(controlled)) return\n      const $trigger = unref(trigger)\n      if ($trigger !== 'hover') {\n        onClose()\n      }\n    }\n  )\n}\n\nconst onBlur = () => {\n  if (!props.virtualTriggering) {\n    onClose()\n  }\n}\n\nlet stopHandle: ReturnType<typeof onClickOutside>\n\nwatch(\n  () => unref(open),\n  (val) => {\n    if (!val) {\n      stopHandle?.()\n    }\n  },\n  {\n    flush: 'post',\n  }\n)\n\nwatch(\n  () => props.content,\n  () => {\n    contentRef.value?.updatePopper?.()\n  }\n)\n\ndefineExpose({\n  /**\n   * @description el-popper-content component instance\n   */\n  contentRef,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;mCAuDc,CAAA;AAAA,EACZ,IAAM,EAAA,kBAAA;AAAA,EACN,YAAc,EAAA,KAAA;AAChB,CAAA,CAAA,CAAA;;;;;;AAIA,IAAM,MAAA,EAAE,aAAa,oBAAqB,EAAA,CAAA;AAC1C,IAAM,MAAA,EAAA,GAAK,aAAa,SAAS,CAAA,CAAA;AAEjC,IAAM,MAAA,UAAA,GAAa,IAAS,IAAI,CAAA,CAAA;AAChC,IAAM,MAAA,SAAA,GAAY,IAAI,KAAK,CAAA,CAAA;AAC3B,IAAM,MAAA;AAAA,MACJ,UAAA;AAAA,MACA,EAAA;AAAA,MACA,IAAA;AAAA,MACA,OAAA;AAAA,MACA,OAAA;AAAA,MACA,MAAA;AAAA,MACA,MAAA;AAAA,MACA,MAAA;AAAA,MACA,YAAA;AAAA,MACA,YAAA;AAAA,KACE,GAAA,MAAA,CAAO,uBAAuB,KAAS,CAAA,CAAA,CAAA;AAC3C,IAAM,MAAA,eAAA,GAAkB,SAAS,MAAM;AACrC,MAAA,OAAO,KAAM,CAAA,UAAA,IAAc,CAAG,EAAA,EAAA,CAAG,SAAU,CAAA,KAAA,CAAA,eAAA,CAAA,CAAA;AAAA,KAC5C,CAAA,CAAA;AACD,IAAM,MAAA,aAAA,GAAgB,SAAS,MAAM;AAGnC,MAAI,IAAA,OAAA,CAAQ,GAAI,CAAA,QAAA,KAAa,MAAQ,EAAA;AACnC,QAAO,OAAA,IAAA,CAAA;AAAA,OACT;AACA,MAAA,OAAO,KAAM,CAAA,UAAA,CAAA;AAAA,KACd,CAAA,CAAA;AAED,IAAA,eAAA,CAAgB,MAAM;AACpB,MAAA,SAAA,CAAU,KAAQ,GAAA,IAAA,CAAA;AAAA,KACnB,CAAA,CAAA;AAED,IAAM,MAAA,YAAA,GAAe,SAAS,MAAM;AAClC,MAAA,OAAO,KAAM,CAAA,aAAa,CAAI,GAAA,IAAA,GAAO,MAAM,IAAI,CAAA,CAAA;AAAA,KAChD,CAAA,CAAA;AAED,IAAM,MAAA,UAAA,GAAa,SAAS,MAAM;AAChC,MAAA,OAAO,KAAM,CAAA,QAAA,GAAW,KAAQ,GAAA,KAAA,CAAM,IAAI,CAAA,CAAA;AAAA,KAC3C,CAAA,CAAA;AAED,IAAM,MAAA,QAAA,GAAW,SAAS,MAAM;AAC9B,MAAO,OAAA,KAAA,CAAM,YAAY,QAAS,CAAA,KAAA,CAAA;AAAA,KACnC,CAAA,CAAA;AAED,IAAA,MAAM,eAAe,QAAS,CAAA,MAAO;AAErC,MAAA,IAAM;AAEN,MAAA,wBAA0B,KAAM,IAAA,GAAA,EAAA,GAAA,EAAA,CAAA;AAC9B,KAAO,CAAA,CAAA;AAAA,IACT,MAAA,UAAA,GAAA,QAAA,CAAA,MAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AAEA,IAAA,MAAM,0BAA2B;AAC/B,MAAA;AAAuB,KAAO,CAAA;AAAA,IAChC,MAAA,kBAAA,GAAA,MAAA;AAEA,MAAM,IAAA,KAAA,CAAA,UAAA,CAAiB;AACrB,QAAA,OAAU,IAAA,CAAA;AACR,KAAO,CAAA;AAAA,IACT,MAAA,cAAA,GAAA,oBAAA,CAAA,kBAAA,EAAA,MAAA;AAAA,MACD,IAAA,KAAA,CAAA,SAAA,IAAA,KAAA,CAAA,OAAA,CAAA,KAAA,OAAA,EAAA;AAED,QAAM,MAAA,EAAA,CAAA;AACJ,OAAI;AACF,KAAQ,CAAA,CAAA;AAAA,IACV,MAAA,cAAA,GAAA,oBAAA,CAAA,kBAAA,EAAA,MAAA;AAAA,MACD,IAAA,KAAA,CAAA,OAAA,CAAA,KAAA,OAAA,EAAA;AAED,QAAA;AACE,OAAA;AACA,KAAe,CAAA,CAAA;AAAA,IACjB,MAAA,aAAA,GAAA,MAAA;AAEA,MAAA,IAAM;AACJ,MAAe,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,UAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;AAAA,MACjB,YAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,YAAA,EAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAO,MAAA,aAAA,GAAA,MAAA;AACP,MAAa,YAAA,IAAA,IAAA,GAAA,KACX,gBAAe,EAAA,CAAA;AACb,KAAA,CAAA;AAAyB,IAC3B,MACA,WAAM,GAAA,MAAA;AACJ,MAAA,MAAI;AAAmB,MAAA,UAAA,GAAA,cAAA,CAAA,QAAA,CAAA,MAAA;AACvB,QAAM,IAAA,EAAA,CAAA;AACN,QAAA,uBAA0B,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,gBAAA,CAAA;AACxB,OAAQ,CAAA,EAAA,MAAA;AAAA,QACV,IAAA,KAAA,CAAA,UAAA,CAAA;AAAA,UAEJ,OAAA;AAAA,QACF,MAAA,QAAA,GAAA,KAAA,CAAA,OAAA,CAAA,CAAA;AAEA,QAAA,YAAqB,KAAA,OAAA,EAAA;AACnB,UAAI,OAAO,EAAmB,CAAA;AAC5B,SAAQ;AAAA,OACV,CAAA,CAAA;AAAA,KACF,CAAA;AAEA,IAAI,MAAA,MAAA,GAAA,MAAA;AAEJ,MAAA,IACE,CAAM,KAAA,CAAA,iBACG,EAAA;AACP,QAAA,OAAU,EAAA,CAAA;AACR,OAAa;AAAA,KACf,CAAA;AAAA,IACF,IACA,UAAA,CAAA;AAAA,IAAA,KACS,CAAA,MAAA,KAAA,CAAA,IAAA,CAAA,EAAA,CAAA,GAAA,KAAA;AAAA,MAEX,IAAA,CAAA,GAAA,EAAA;AAEA,QACE,UAAY,IAAA,IAAA,GAAA,KACN,CAAA,GAAA,UAAA,EAAA,CAAA;AACJ,OAAA;AAAiC,KAErC,EAAA;AAEA,MAAa,KAAA,EAAA,MAAA;AAAA,KAIX,CAAA,CAAA;AAAA,IACF,KAAC,CAAA,MAAA,KAAA,CAAA,OAAA,EAAA,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}