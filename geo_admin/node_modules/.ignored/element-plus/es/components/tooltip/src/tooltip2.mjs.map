{"version": 3, "file": "tooltip2.mjs", "sources": ["../../../../../../packages/components/tooltip/src/tooltip.vue"], "sourcesContent": ["<template>\n  <el-popper ref=\"popperRef\" :role=\"role\">\n    <el-tooltip-trigger\n      :disabled=\"disabled\"\n      :trigger=\"trigger\"\n      :trigger-keys=\"triggerKeys\"\n      :virtual-ref=\"virtualRef\"\n      :virtual-triggering=\"virtualTriggering\"\n    >\n      <slot v-if=\"$slots.default\" />\n    </el-tooltip-trigger>\n    <el-tooltip-content\n      ref=\"contentRef\"\n      :aria-label=\"ariaLabel\"\n      :boundaries-padding=\"boundariesPadding\"\n      :content=\"content\"\n      :disabled=\"disabled\"\n      :effect=\"effect\"\n      :enterable=\"enterable\"\n      :fallback-placements=\"fallbackPlacements\"\n      :hide-after=\"hideAfter\"\n      :gpu-acceleration=\"gpuAcceleration\"\n      :offset=\"offset\"\n      :persistent=\"persistent\"\n      :popper-class=\"popperClass\"\n      :popper-style=\"popperStyle\"\n      :placement=\"placement\"\n      :popper-options=\"popperOptions\"\n      :pure=\"pure\"\n      :raw-content=\"rawContent\"\n      :reference-el=\"referenceEl\"\n      :trigger-target-el=\"triggerTargetEl\"\n      :show-after=\"showAfter\"\n      :strategy=\"strategy\"\n      :teleported=\"teleported\"\n      :transition=\"transition\"\n      :virtual-triggering=\"virtualTriggering\"\n      :z-index=\"zIndex\"\n      :append-to=\"appendTo\"\n    >\n      <slot name=\"content\">\n        <span v-if=\"rawContent\" v-html=\"content\" />\n        <span v-else>{{ content }}</span>\n      </slot>\n      <el-popper-arrow v-if=\"showArrow\" :arrow-offset=\"arrowOffset\" />\n    </el-tooltip-content>\n  </el-popper>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  onDeactivated,\n  provide,\n  readonly,\n  ref,\n  toRef,\n  unref,\n  watch,\n} from 'vue'\nimport { ElPopper, ElPopperArrow } from '@element-plus/components/popper'\n\nimport { isBoolean } from '@element-plus/utils'\nimport {\n  useDelayedToggle,\n  useId,\n  usePopperContainer,\n} from '@element-plus/hooks'\nimport { TOOLTIP_INJECTION_KEY } from './constants'\nimport { tooltipEmits, useTooltipModelToggle, useTooltipProps } from './tooltip'\nimport ElTooltipTrigger from './trigger.vue'\nimport ElTooltipContent from './content.vue'\nimport type { PopperInstance } from '@element-plus/components/popper'\n\ndefineOptions({\n  name: 'ElTooltip',\n})\n\nconst props = defineProps(useTooltipProps)\nconst emit = defineEmits(tooltipEmits)\n\nusePopperContainer()\n\nconst id = useId()\nconst popperRef = ref<PopperInstance>()\n// TODO any is temporary, replace with `TooltipContentInstance` later\nconst contentRef = ref<any>()\n\nconst updatePopper = () => {\n  const popperComponent = unref(popperRef)\n  if (popperComponent) {\n    popperComponent.popperInstanceRef?.update()\n  }\n}\nconst open = ref(false)\nconst toggleReason = ref<Event>()\n\nconst { show, hide, hasUpdateHandler } = useTooltipModelToggle({\n  indicator: open,\n  toggleReason,\n})\n\nconst { onOpen, onClose } = useDelayedToggle({\n  showAfter: toRef(props, 'showAfter'),\n  hideAfter: toRef(props, 'hideAfter'),\n  autoClose: toRef(props, 'autoClose'),\n  open: show,\n  close: hide,\n})\n\nconst controlled = computed(\n  () => isBoolean(props.visible) && !hasUpdateHandler.value\n)\n\nprovide(TOOLTIP_INJECTION_KEY, {\n  controlled,\n  id,\n  open: readonly(open),\n  trigger: toRef(props, 'trigger'),\n  onOpen: (event?: Event) => {\n    onOpen(event)\n  },\n  onClose: (event?: Event) => {\n    onClose(event)\n  },\n  onToggle: (event?: Event) => {\n    if (unref(open)) {\n      onClose(event)\n    } else {\n      onOpen(event)\n    }\n  },\n  onShow: () => {\n    emit('show', toggleReason.value)\n  },\n  onHide: () => {\n    emit('hide', toggleReason.value)\n  },\n  onBeforeShow: () => {\n    emit('before-show', toggleReason.value)\n  },\n  onBeforeHide: () => {\n    emit('before-hide', toggleReason.value)\n  },\n  updatePopper,\n})\n\nwatch(\n  () => props.disabled,\n  (disabled) => {\n    if (disabled && open.value) {\n      open.value = false\n    }\n  }\n)\n\nconst isFocusInsideContent = (event?: FocusEvent) => {\n  const popperContent: HTMLElement | undefined =\n    contentRef.value?.contentRef?.popperContentRef\n  const activeElement = (event?.relatedTarget as Node) || document.activeElement\n\n  return popperContent && popperContent.contains(activeElement)\n}\n\nonDeactivated(() => open.value && hide())\n\ndefineExpose({\n  /**\n   * @description el-popper component instance\n   */\n  popperRef,\n  /**\n   * @description el-tooltip-content component instance\n   */\n  contentRef,\n  /**\n   * @description validate current focus event is trigger inside el-tooltip-content\n   */\n  isFocusInsideContent,\n  /**\n   * @description update el-popper component instance\n   */\n  updatePopper,\n  /**\n   * @description expose onOpen function to mange el-tooltip open state\n   */\n  onOpen,\n  /**\n   * @description expose onOpen function to mange el-tooltip open state\n   */\n  onClose,\n  /**\n   * @description expose hide function\n   */\n  hide,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;mCA0Ec,CAAA;AAAA,EACZ,IAAM,EAAA,WAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAmB,kBAAA,EAAA,CAAA;AAEnB,IAAA,MAAM,KAAK,KAAM,EAAA,CAAA;AACjB,IAAA,MAAM,YAAY,GAAoB,EAAA,CAAA;AAEtC,IAAA,MAAM,aAAa,GAAS,EAAA,CAAA;AAE5B,IAAA,MAAM,eAAe,MAAM;AACzB,MAAM,IAAA,EAAA,CAAA;AACN,MAAA,MAAqB,eAAA,GAAA,KAAA,CAAA,SAAA,CAAA,CAAA;AACnB,MAAA,IAAA;AAA0C,QAC5C,CAAA,EAAA,GAAA,eAAA,CAAA,iBAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAA,EAAA,CAAA;AAAA,OACF;AACA,KAAM,CAAA;AACN,IAAA,MAAM,gBAA0B,CAAA,CAAA;AAEhC,IAAA,MAAM,YAAc,GAAA,GAAA,EAAA,CAAA;AAA2C,IAAA,MAClD,EAAA,IAAA,EAAA,IAAA,EAAA,gBAAA,EAAA,GAAA,qBAAA,CAAA;AAAA,MACX,SAAA,EAAA,IAAA;AAAA,MACD,YAAA;AAED,KAAM,CAAA,CAAA;AAAuC,IAC3C,MAAA,EAAA,MAAiB,EAAA,OAAA,EAAA,GAAkB,gBAAA,CAAA;AAAA,MACnC,SAAA,EAAW,KAAM,CAAA,KAAA,EAAO,WAAW,CAAA;AAAA,MACnC,SAAA,EAAW,KAAM,CAAA,KAAA,EAAO,WAAW,CAAA;AAAA,MACnC,SAAM,EAAA,KAAA,CAAA,KAAA,EAAA,WAAA,CAAA;AAAA,MACN,IAAO,EAAA,IAAA;AAAA,MACR,KAAA,EAAA,IAAA;AAED,KAAM,CAAA,CAAA;AAIN,IAAA,MAAA,UAA+B,GAAA,QAAA,CAAA,MAAA,SAAA,CAAA,KAAA,CAAA,OAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,CAAA;AAAA,IAC7B,OAAA,CAAA,qBAAA,EAAA;AAAA,MACA,UAAA;AAAA,MACA,EAAA;AAAmB,MACnB,IAAA,EAAA,QAAe,CAAA,IAAA,CAAA;AAAgB,MAC/B,OAAA,EAAS,KAAkB,CAAA,KAAA,EAAA,SAAA,CAAA;AACzB,MAAA,MAAA,EAAA,CAAO,KAAK,KAAA;AAAA,QACd,MAAA,CAAA,KAAA,CAAA,CAAA;AAAA,OACA;AACE,MAAA,OAAA,EAAA,CAAQ,KAAK,KAAA;AAAA,QACf,OAAA,CAAA,KAAA,CAAA,CAAA;AAAA,OACA;AACE,MAAI,QAAA,EAAA,CAAA,KAAU,KAAG;AACf,QAAA,IAAA,KAAA,CAAQ,IAAK,CAAA,EAAA;AAAA,UACR,OAAA,CAAA,KAAA,CAAA,CAAA;AACL,SAAA,MAAA;AAAY,UACd,MAAA,CAAA,KAAA,CAAA,CAAA;AAAA,SACF;AAAA;AAEE,MAAK,MAAA,EAAA,MAAA;AAA0B,QACjC,IAAA,CAAA,MAAA,EAAA,YAAA,CAAA,KAAA,CAAA,CAAA;AAAA;AAEE,MAAK,MAAA,EAAA,MAAA;AAA0B,QACjC,IAAA,CAAA,MAAA,EAAA,YAAA,CAAA,KAAA,CAAA,CAAA;AAAA;AAEE,MAAK,YAAA,EAAA,MAAA;AAAiC,QACxC,IAAA,CAAA,aAAA,EAAA,YAAA,CAAA,KAAA,CAAA,CAAA;AAAA;AAEE,MAAK,YAAA,EAAA,MAAA;AAAiC,QACxC,IAAA,CAAA,aAAA,EAAA,YAAA,CAAA,KAAA,CAAA,CAAA;AAAA,OACA;AAAA,MACD,YAAA;AAED,KAAA,CAAA,CAAA;AAGI,IAAI,KAAA,CAAA,MAAA,KAAA,CAAY,QAAY,EAAA,CAAA,QAAA,KAAA;AAC1B,MAAA,IAAA,QAAa,IAAA,IAAA,CAAA,KAAA,EAAA;AAAA,QACf,IAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAAA,OAEJ;AAEA,KAAM,CAAA,CAAA;AACJ,IAAM,MAAA,oBACO,GAAA,CAAA,KAAA,KAAA;AACb,MAAM,IAAA,EAAA,EAAA,EAAA,CAAA;AAEN,MAAO,MAAA,aAAA,GAAA,CAAA,EAA+B,GAAA,CAAA,EAAA,GAAA,UAAA,CAAA,KAAsB,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,gBAAA,CAAA;AAAA,MAC9D,MAAA,aAAA,GAAA,CAAA,KAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,KAAA,CAAA,aAAA,KAAA,QAAA,CAAA,aAAA,CAAA;AAEA,MAAA,OAAA,aAAoB,IAAc,aAAK,CAAC,QAAA,CAAA,aAAA,CAAA,CAAA;AAExC,KAAa,CAAA;AAAA,IAIX,aAAA,CAAA,MAAA,IAAA,CAAA,KAAA,IAAA,IAAA,EAAA,CAAA,CAAA;AAAA,IAIA,MAAA,CAAA;AAAA,MAIA,SAAA;AAAA,MAIA,UAAA;AAAA,MAIA,oBAAA;AAAA,MAIA,YAAA;AAAA,MAIA,MAAA;AAAA,MACD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}