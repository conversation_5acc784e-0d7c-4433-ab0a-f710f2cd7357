import type { VNode } from 'vue';
declare const _default: import("vue").DefineComponent<{
    store: {
        required: boolean;
        type: import("vue").PropType<any>;
    };
    stripe: BooleanConstructor;
    tooltipEffect: StringConstructor;
    tooltipOptions: {
        type: import("vue").PropType<Partial<Pick<import("../../..").ElTooltipProps, "offset" | "effect" | "placement" | "popperClass" | "showAfter" | "hideAfter" | "popperOptions" | "enterable" | "showArrow">> | undefined>;
    };
    context: {
        default: () => {};
        type: import("vue").PropType<import("../table/defaults").Table<any>>;
    };
    rowClassName: import("vue").PropType<import("../table/defaults").ColumnCls<any>>;
    rowStyle: import("vue").PropType<import("../table/defaults").ColumnStyle<any>>;
    fixed: {
        type: StringConstructor;
        default: string;
    };
    highlight: BooleanConstructor;
}, {
    ns: {
        namespace: import("vue").ComputedRef<string>;
        b: (blockSuffix?: string) => string;
        e: (element?: string | undefined) => string;
        m: (modifier?: string | undefined) => string;
        be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
        em: (element?: string | undefined, modifier?: string | undefined) => string;
        bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
        bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
        is: {
            (name: string, state: boolean | undefined): string;
            (name: string): string;
        };
        cssVar: (object: Record<string, string>) => Record<string, string>;
        cssVarName: (name: string) => string;
        cssVarBlock: (object: Record<string, string>) => Record<string, string>;
        cssVarBlockName: (name: string) => string;
    };
    onColumnsChange: (layout: import("../table-layout").default<any>) => void;
    onScrollableChange: (layout: import("../table-layout").default<any>) => void;
    wrappedRowRender: (row: any, $index: number) => VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }> | VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>[] | VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>[][];
    tooltipContent: import("vue").Ref<string>;
    tooltipTrigger: import("vue").Ref<VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>>;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    store: {
        required: boolean;
        type: import("vue").PropType<any>;
    };
    stripe: BooleanConstructor;
    tooltipEffect: StringConstructor;
    tooltipOptions: {
        type: import("vue").PropType<Partial<Pick<import("../../..").ElTooltipProps, "offset" | "effect" | "placement" | "popperClass" | "showAfter" | "hideAfter" | "popperOptions" | "enterable" | "showArrow">> | undefined>;
    };
    context: {
        default: () => {};
        type: import("vue").PropType<import("../table/defaults").Table<any>>;
    };
    rowClassName: import("vue").PropType<import("../table/defaults").ColumnCls<any>>;
    rowStyle: import("vue").PropType<import("../table/defaults").ColumnStyle<any>>;
    fixed: {
        type: StringConstructor;
        default: string;
    };
    highlight: BooleanConstructor;
}>>, {
    fixed: string;
    context: import("../table/defaults").Table<any>;
    stripe: boolean;
    highlight: boolean;
}>;
export default _default;
