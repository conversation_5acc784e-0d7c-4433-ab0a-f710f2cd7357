{"version": 3, "file": "styles-helper.mjs", "sources": ["../../../../../../../packages/components/table/src/table-body/styles-helper.ts"], "sourcesContent": ["// @ts-nocheck\nimport { inject } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport {\n  ensurePosition,\n  getFixedColumnOffset,\n  getFixedColumnsClass,\n} from '../util'\nimport { TABLE_INJECTION_KEY } from '../tokens'\nimport type { TableColumnCtx } from '../table-column/defaults'\nimport type { TableBodyProps } from './defaults'\n\nfunction useStyles<T>(props: Partial<TableBodyProps<T>>) {\n  const parent = inject(TABLE_INJECTION_KEY)\n  const ns = useNamespace('table')\n\n  const getRowStyle = (row: T, rowIndex: number) => {\n    const rowStyle = parent?.props.rowStyle\n    if (typeof rowStyle === 'function') {\n      return rowStyle.call(null, {\n        row,\n        rowIndex,\n      })\n    }\n    return rowStyle || null\n  }\n\n  const getRowClass = (row: T, rowIndex: number) => {\n    const classes = [ns.e('row')]\n    if (\n      parent?.props.highlightCurrentRow &&\n      row === props.store.states.currentRow.value\n    ) {\n      classes.push('current-row')\n    }\n\n    if (props.stripe && rowIndex % 2 === 1) {\n      classes.push(ns.em('row', 'striped'))\n    }\n    const rowClassName = parent?.props.rowClassName\n    if (typeof rowClassName === 'string') {\n      classes.push(rowClassName)\n    } else if (typeof rowClassName === 'function') {\n      classes.push(\n        rowClassName.call(null, {\n          row,\n          rowIndex,\n        })\n      )\n    }\n    return classes\n  }\n\n  const getCellStyle = (\n    rowIndex: number,\n    columnIndex: number,\n    row: T,\n    column: TableColumnCtx<T>\n  ) => {\n    const cellStyle = parent?.props.cellStyle\n    let cellStyles = cellStyle ?? {}\n    if (typeof cellStyle === 'function') {\n      cellStyles = cellStyle.call(null, {\n        rowIndex,\n        columnIndex,\n        row,\n        column,\n      })\n    }\n    const fixedStyle = getFixedColumnOffset(\n      columnIndex,\n      props?.fixed,\n      props.store\n    )\n    ensurePosition(fixedStyle, 'left')\n    ensurePosition(fixedStyle, 'right')\n    return Object.assign({}, cellStyles, fixedStyle)\n  }\n\n  const getCellClass = (\n    rowIndex: number,\n    columnIndex: number,\n    row: T,\n    column: TableColumnCtx<T>,\n    offset: number\n  ) => {\n    const fixedClasses = getFixedColumnsClass(\n      ns.b(),\n      columnIndex,\n      props?.fixed,\n      props.store,\n      undefined,\n      offset\n    )\n    const classes = [column.id, column.align, column.className, ...fixedClasses]\n    const cellClassName = parent?.props.cellClassName\n    if (typeof cellClassName === 'string') {\n      classes.push(cellClassName)\n    } else if (typeof cellClassName === 'function') {\n      classes.push(\n        cellClassName.call(null, {\n          rowIndex,\n          columnIndex,\n          row,\n          column,\n        })\n      )\n    }\n    classes.push(ns.e('cell'))\n    return classes.filter((className) => Boolean(className)).join(' ')\n  }\n  const getSpan = (\n    row: T,\n    column: TableColumnCtx<T>,\n    rowIndex: number,\n    columnIndex: number\n  ) => {\n    let rowspan = 1\n    let colspan = 1\n    const fn = parent?.props.spanMethod\n    if (typeof fn === 'function') {\n      const result = fn({\n        row,\n        column,\n        rowIndex,\n        columnIndex,\n      })\n      if (Array.isArray(result)) {\n        rowspan = result[0]\n        colspan = result[1]\n      } else if (typeof result === 'object') {\n        rowspan = result.rowspan\n        colspan = result.colspan\n      }\n    }\n    return { rowspan, colspan }\n  }\n  const getColspanRealWidth = (\n    columns: TableColumnCtx<T>[],\n    colspan: number,\n    index: number\n  ): number => {\n    if (colspan < 1) {\n      return columns[index].realWidth\n    }\n    const widthArr = columns\n      .map(({ realWidth, width }) => realWidth || width)\n      .slice(index, index + colspan)\n    return Number(\n      widthArr.reduce((acc, width) => Number(acc) + Number(width), -1)\n    )\n  }\n\n  return {\n    getRowStyle,\n    getRowClass,\n    getCellStyle,\n    getCellClass,\n    getSpan,\n    getColspanRealWidth,\n  }\n}\n\nexport default useStyles\n"], "names": [], "mappings": ";;;;;;AAQA,SAAS,SAAS,CAAC,KAAK,EAAE;AAC1B,EAAE,MAAM,MAAM,GAAG,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAC7C,EAAE,MAAM,EAAE,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;AACnC,EAAE,MAAM,WAAW,GAAG,CAAC,GAAG,EAAE,QAAQ,KAAK;AACzC,IAAI,MAAM,QAAQ,GAAG,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;AACrE,IAAI,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;AACxC,MAAM,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE;AACjC,QAAQ,GAAG;AACX,QAAQ,QAAQ;AAChB,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,OAAO,QAAQ,IAAI,IAAI,CAAC;AAC5B,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,CAAC,GAAG,EAAE,QAAQ,KAAK;AACzC,IAAI,MAAM,OAAO,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AAClC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,mBAAmB,KAAK,GAAG,KAAK,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE;AACrH,MAAM,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAClC,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,MAAM,IAAI,QAAQ,GAAG,CAAC,KAAK,CAAC,EAAE;AAC5C,MAAM,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC;AAC5C,KAAK;AACL,IAAI,MAAM,YAAY,GAAG,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC;AAC7E,IAAI,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;AAC1C,MAAM,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACjC,KAAK,MAAM,IAAI,OAAO,YAAY,KAAK,UAAU,EAAE;AACnD,MAAM,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE;AAC3C,QAAQ,GAAG;AACX,QAAQ,QAAQ;AAChB,OAAO,CAAC,CAAC,CAAC;AACV,KAAK;AACL,IAAI,OAAO,OAAO,CAAC;AACnB,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,GAAG,EAAE,MAAM,KAAK;AAC/D,IAAI,MAAM,SAAS,GAAG,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC;AACvE,IAAI,IAAI,UAAU,GAAG,SAAS,IAAI,IAAI,GAAG,SAAS,GAAG,EAAE,CAAC;AACxD,IAAI,IAAI,OAAO,SAAS,KAAK,UAAU,EAAE;AACzC,MAAM,UAAU,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE;AACxC,QAAQ,QAAQ;AAChB,QAAQ,WAAW;AACnB,QAAQ,GAAG;AACX,QAAQ,MAAM;AACd,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,MAAM,UAAU,GAAG,oBAAoB,CAAC,WAAW,EAAE,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5G,IAAI,cAAc,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;AACvC,IAAI,cAAc,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;AACxC,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;AACrD,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,KAAK;AACvE,IAAI,MAAM,YAAY,GAAG,oBAAoB,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,WAAW,EAAE,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC,CAAC;AACtI,IAAI,MAAM,OAAO,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,SAAS,EAAE,GAAG,YAAY,CAAC,CAAC;AACjF,IAAI,MAAM,aAAa,GAAG,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC;AAC/E,IAAI,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;AAC3C,MAAM,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAClC,KAAK,MAAM,IAAI,OAAO,aAAa,KAAK,UAAU,EAAE;AACpD,MAAM,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE;AAC5C,QAAQ,QAAQ;AAChB,QAAQ,WAAW;AACnB,QAAQ,GAAG;AACX,QAAQ,MAAM;AACd,OAAO,CAAC,CAAC,CAAC;AACV,KAAK;AACL,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;AAC/B,IAAI,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,SAAS,KAAK,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACvE,GAAG,CAAC;AACJ,EAAE,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,KAAK;AAC1D,IAAI,IAAI,OAAO,GAAG,CAAC,CAAC;AACpB,IAAI,IAAI,OAAO,GAAG,CAAC,CAAC;AACpB,IAAI,MAAM,EAAE,GAAG,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC;AACjE,IAAI,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE;AAClC,MAAM,MAAM,MAAM,GAAG,EAAE,CAAC;AACxB,QAAQ,GAAG;AACX,QAAQ,MAAM;AACd,QAAQ,QAAQ;AAChB,QAAQ,WAAW;AACnB,OAAO,CAAC,CAAC;AACT,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AACjC,QAAQ,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5B,QAAQ,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5B,OAAO,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AAC7C,QAAQ,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;AACjC,QAAQ,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;AACjC,OAAO;AACP,KAAK;AACL,IAAI,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;AAChC,GAAG,CAAC;AACJ,EAAE,MAAM,mBAAmB,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,KAAK;AAC3D,IAAI,IAAI,OAAO,GAAG,CAAC,EAAE;AACrB,MAAM,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC;AACtC,KAAK;AACL,IAAI,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,SAAS,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,GAAG,OAAO,CAAC,CAAC;AAC7G,IAAI,OAAO,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,KAAK,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACpF,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI,YAAY;AAChB,IAAI,OAAO;AACX,IAAI,mBAAmB;AACvB,GAAG,CAAC;AACJ;;;;"}