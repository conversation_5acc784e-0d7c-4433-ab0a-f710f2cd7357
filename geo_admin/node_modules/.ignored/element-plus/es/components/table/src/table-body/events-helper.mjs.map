{"version": 3, "file": "events-helper.mjs", "sources": ["../../../../../../../packages/components/table/src/table-body/events-helper.ts"], "sourcesContent": ["// @ts-nocheck\nimport { h, inject, ref } from 'vue'\nimport { debounce } from 'lodash-unified'\nimport { hasClass } from '@element-plus/utils'\nimport { useZIndex } from '@element-plus/hooks'\nimport { createTablePopper, getCell, getColumnByCell } from '../util'\nimport { TABLE_INJECTION_KEY } from '../tokens'\nimport type { TableColumnCtx } from '../table-column/defaults'\nimport type { TableBodyProps } from './defaults'\nimport type { TableOverflowTooltipOptions } from '../util'\n\nfunction useEvents<T>(props: Partial<TableBodyProps<T>>) {\n  const parent = inject(TABLE_INJECTION_KEY)\n  const tooltipContent = ref('')\n  const tooltipTrigger = ref(h('div'))\n  const { nextZIndex } = useZIndex()\n  const handleEvent = (event: Event, row: T, name: string) => {\n    const table = parent\n    const cell = getCell(event)\n    let column: TableColumnCtx<T>\n    const namespace = table?.vnode.el?.dataset.prefix\n    if (cell) {\n      column = getColumnByCell(\n        {\n          columns: props.store.states.columns.value,\n        },\n        cell,\n        namespace\n      )\n      if (column) {\n        table?.emit(`cell-${name}`, row, column, cell, event)\n      }\n    }\n    table?.emit(`row-${name}`, row, column, event)\n  }\n  const handleDoubleClick = (event: Event, row: T) => {\n    handleEvent(event, row, 'dblclick')\n  }\n  const handleClick = (event: Event, row: T) => {\n    props.store.commit('setCurrentRow', row)\n    handleEvent(event, row, 'click')\n  }\n  const handleContextMenu = (event: Event, row: T) => {\n    handleEvent(event, row, 'contextmenu')\n  }\n  const handleMouseEnter = debounce((index: number) => {\n    props.store.commit('setHoverRow', index)\n  }, 30)\n  const handleMouseLeave = debounce(() => {\n    props.store.commit('setHoverRow', null)\n  }, 30)\n  const getPadding = (el: HTMLElement) => {\n    const style = window.getComputedStyle(el, null)\n    const paddingLeft = Number.parseInt(style.paddingLeft, 10) || 0\n    const paddingRight = Number.parseInt(style.paddingRight, 10) || 0\n    const paddingTop = Number.parseInt(style.paddingTop, 10) || 0\n    const paddingBottom = Number.parseInt(style.paddingBottom, 10) || 0\n    return {\n      left: paddingLeft,\n      right: paddingRight,\n      top: paddingTop,\n      bottom: paddingBottom,\n    }\n  }\n  const handleCellMouseEnter = (\n    event: MouseEvent,\n    row: T,\n    tooltipOptions: TableOverflowTooltipOptions\n  ) => {\n    const table = parent\n    const cell = getCell(event)\n    const namespace = table?.vnode.el?.dataset.prefix\n    if (cell) {\n      const column = getColumnByCell(\n        {\n          columns: props.store.states.columns.value,\n        },\n        cell,\n        namespace\n      )\n      const hoverState = (table.hoverState = { cell, column, row })\n      table?.emit(\n        'cell-mouse-enter',\n        hoverState.row,\n        hoverState.column,\n        hoverState.cell,\n        event\n      )\n    }\n\n    if (!tooltipOptions) {\n      return\n    }\n\n    // 判断是否text-overflow, 如果是就显示tooltip\n    const cellChild = (event.target as HTMLElement).querySelector(\n      '.cell'\n    ) as HTMLElement\n    if (\n      !(\n        hasClass(cellChild, `${namespace}-tooltip`) &&\n        cellChild.childNodes.length\n      )\n    ) {\n      return\n    }\n    // use range width instead of scrollWidth to determine whether the text is overflowing\n    // to address a potential FireFox bug: https://bugzilla.mozilla.org/show_bug.cgi?id=1074543#c3\n    const range = document.createRange()\n    range.setStart(cellChild, 0)\n    range.setEnd(cellChild, cellChild.childNodes.length)\n    /** detail: https://github.com/element-plus/element-plus/issues/10790\n     *  What went wrong?\n     *  UI > Browser > Zoom, In Blink/WebKit, getBoundingClientRect() sometimes returns inexact values, probably due to lost precision during internal calculations. In the example above:\n     *    - Expected: 188\n     *    - Actual: 188.00000762939453\n     */\n    let rangeWidth = range.getBoundingClientRect().width\n    let rangeHeight = range.getBoundingClientRect().height\n    const offsetWidth = rangeWidth - Math.floor(rangeWidth)\n    if (offsetWidth < 0.001) {\n      rangeWidth = Math.floor(rangeWidth)\n    }\n    const offsetHeight = rangeHeight - Math.floor(rangeHeight)\n    if (offsetHeight < 0.001) {\n      rangeHeight = Math.floor(rangeHeight)\n    }\n\n    const { top, left, right, bottom } = getPadding(cellChild)\n    const horizontalPadding = left + right\n    const verticalPadding = top + bottom\n    if (\n      rangeWidth + horizontalPadding > cellChild.offsetWidth ||\n      rangeHeight + verticalPadding > cellChild.offsetHeight ||\n      cellChild.scrollWidth > cellChild.offsetWidth\n    ) {\n      createTablePopper(\n        parent?.refs.tableWrapper,\n        cell,\n        cell.innerText || cell.textContent,\n        nextZIndex,\n        tooltipOptions\n      )\n    }\n  }\n  const handleCellMouseLeave = (event) => {\n    const cell = getCell(event)\n    if (!cell) return\n\n    const oldHoverState = parent?.hoverState\n    parent?.emit(\n      'cell-mouse-leave',\n      oldHoverState?.row,\n      oldHoverState?.column,\n      oldHoverState?.cell,\n      event\n    )\n  }\n\n  return {\n    handleDoubleClick,\n    handleClick,\n    handleContextMenu,\n    handleMouseEnter,\n    handleMouseLeave,\n    handleCellMouseEnter,\n    handleCellMouseLeave,\n    tooltipContent,\n    tooltipTrigger,\n  }\n}\n\nexport default useEvents\n"], "names": [], "mappings": ";;;;;;;;;AAMA,SAAS,SAAS,CAAC,KAAK,EAAE;AAC1B,EAAE,MAAM,MAAM,GAAG,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAC7C,EAAE,MAAM,cAAc,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;AACjC,EAAE,MAAM,cAAc,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AACvC,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,SAAS,EAAE,CAAC;AACrC,EAAE,MAAM,WAAW,GAAG,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,KAAK;AAC5C,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC;AACzB,IAAI,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;AAChC,IAAI,IAAI,MAAM,CAAC;AACf,IAAI,MAAM,SAAS,GAAG,CAAC,EAAE,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC;AAC1G,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,MAAM,GAAG,eAAe,CAAC;AAC/B,QAAQ,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK;AACjD,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;AAC1B,MAAM,IAAI,MAAM,EAAE;AAClB,QAAQ,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AACtF,OAAO;AACP,KAAK;AACL,IAAI,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AAC3E,GAAG,CAAC;AACJ,EAAE,MAAM,iBAAiB,GAAG,CAAC,KAAK,EAAE,GAAG,KAAK;AAC5C,IAAI,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;AACxC,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,CAAC,KAAK,EAAE,GAAG,KAAK;AACtC,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC;AAC7C,IAAI,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;AACrC,GAAG,CAAC;AACJ,EAAE,MAAM,iBAAiB,GAAG,CAAC,KAAK,EAAE,GAAG,KAAK;AAC5C,IAAI,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE,aAAa,CAAC,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE,MAAM,gBAAgB,GAAG,QAAQ,CAAC,CAAC,KAAK,KAAK;AAC/C,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;AAC7C,GAAG,EAAE,EAAE,CAAC,CAAC;AACT,EAAE,MAAM,gBAAgB,GAAG,QAAQ,CAAC,MAAM;AAC1C,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;AAC5C,GAAG,EAAE,EAAE,CAAC,CAAC;AACT,EAAE,MAAM,UAAU,GAAG,CAAC,EAAE,KAAK;AAC7B,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,gBAAgB,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;AACpD,IAAI,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;AACpE,IAAI,MAAM,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;AACtE,IAAI,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;AAClE,IAAI,MAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,aAAa,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;AACxE,IAAI,OAAO;AACX,MAAM,IAAI,EAAE,WAAW;AACvB,MAAM,KAAK,EAAE,YAAY;AACzB,MAAM,GAAG,EAAE,UAAU;AACrB,MAAM,MAAM,EAAE,aAAa;AAC3B,KAAK,CAAC;AACN,GAAG,CAAC;AACJ,EAAE,MAAM,oBAAoB,GAAG,CAAC,KAAK,EAAE,GAAG,EAAE,cAAc,KAAK;AAC/D,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC;AACzB,IAAI,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;AAChC,IAAI,MAAM,SAAS,GAAG,CAAC,EAAE,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC;AAC1G,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,MAAM,MAAM,GAAG,eAAe,CAAC;AACrC,QAAQ,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK;AACjD,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;AAC1B,MAAM,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,MAAM,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,kBAAkB,EAAE,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,MAAM,EAAE,UAAU,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACzH,KAAK;AACL,IAAI,IAAI,CAAC,cAAc,EAAE;AACzB,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AAC1D,IAAI,IAAI,EAAE,QAAQ,CAAC,SAAS,EAAE,CAAC,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;AACvF,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;AACzC,IAAI,KAAK,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;AACjC,IAAI,KAAK,CAAC,MAAM,CAAC,SAAS,EAAE,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AACzD,IAAI,IAAI,UAAU,GAAG,KAAK,CAAC,qBAAqB,EAAE,CAAC,KAAK,CAAC;AACzD,IAAI,IAAI,WAAW,GAAG,KAAK,CAAC,qBAAqB,EAAE,CAAC,MAAM,CAAC;AAC3D,IAAI,MAAM,WAAW,GAAG,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AAC5D,IAAI,IAAI,WAAW,GAAG,IAAI,EAAE;AAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AAC1C,KAAK;AACL,IAAI,MAAM,YAAY,GAAG,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;AAC/D,IAAI,IAAI,YAAY,GAAG,IAAI,EAAE;AAC7B,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;AAC5C,KAAK;AACL,IAAI,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;AAC/D,IAAI,MAAM,iBAAiB,GAAG,IAAI,GAAG,KAAK,CAAC;AAC3C,IAAI,MAAM,eAAe,GAAG,GAAG,GAAG,MAAM,CAAC;AACzC,IAAI,IAAI,UAAU,GAAG,iBAAiB,GAAG,SAAS,CAAC,WAAW,IAAI,WAAW,GAAG,eAAe,GAAG,SAAS,CAAC,YAAY,IAAI,SAAS,CAAC,WAAW,GAAG,SAAS,CAAC,WAAW,EAAE;AAC3K,MAAM,iBAAiB,CAAC,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;AAClJ,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,oBAAoB,GAAG,CAAC,KAAK,KAAK;AAC1C,IAAI,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;AAChC,IAAI,IAAI,CAAC,IAAI;AACb,MAAM,OAAO;AACb,IAAI,MAAM,aAAa,GAAG,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC;AACtE,IAAI,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,GAAG,EAAE,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACrO,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,iBAAiB;AACrB,IAAI,WAAW;AACf,IAAI,iBAAiB;AACrB,IAAI,gBAAgB;AACpB,IAAI,gBAAgB;AACpB,IAAI,oBAAoB;AACxB,IAAI,oBAAoB;AACxB,IAAI,cAAc;AAClB,IAAI,cAAc;AAClB,GAAG,CAAC;AACJ;;;;"}