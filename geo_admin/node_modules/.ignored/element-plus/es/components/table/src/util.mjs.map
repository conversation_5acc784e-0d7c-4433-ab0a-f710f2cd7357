{"version": 3, "file": "util.mjs", "sources": ["../../../../../../packages/components/table/src/util.ts"], "sourcesContent": ["// @ts-nocheck\nimport { createPopper } from '@popperjs/core'\nimport { flatMap, get, merge } from 'lodash-unified'\nimport escapeHtml from 'escape-html'\nimport {\n  hasOwn,\n  isArray,\n  isBoolean,\n  isObject,\n  throwError,\n} from '@element-plus/utils'\nimport { useDelayedToggle } from '@element-plus/hooks'\nimport type { PopperInstance } from '@element-plus/components/popper'\nimport type { Nullable } from '@element-plus/utils'\nimport type { TableColumnCtx } from './table-column/defaults'\nimport type { ElTooltipProps } from '@element-plus/components/tooltip'\n\nexport type TableOverflowTooltipOptions = Partial<\n  Pick<\n    ElTooltipProps,\n    | 'effect'\n    | 'enterable'\n    | 'hideAfter'\n    | 'offset'\n    | 'placement'\n    | 'popperClass'\n    | 'popperOptions'\n    | 'showAfter'\n    | 'showArrow'\n    // | 'transition'\n  >\n>\n\nexport const getCell = function (event: Event) {\n  return (event.target as HTMLElement)?.closest('td')\n}\n\nexport const orderBy = function <T>(\n  array: T[],\n  sortKey: string,\n  reverse: string | number,\n  sortMethod,\n  sortBy: string | (string | ((a: T, b: T, array?: T[]) => number))[]\n) {\n  if (\n    !sortKey &&\n    !sortMethod &&\n    (!sortBy || (Array.isArray(sortBy) && !sortBy.length))\n  ) {\n    return array\n  }\n  if (typeof reverse === 'string') {\n    reverse = reverse === 'descending' ? -1 : 1\n  } else {\n    reverse = reverse && reverse < 0 ? -1 : 1\n  }\n  const getKey = sortMethod\n    ? null\n    : function (value, index) {\n        if (sortBy) {\n          if (!Array.isArray(sortBy)) {\n            sortBy = [sortBy]\n          }\n          return sortBy.map((by) => {\n            if (typeof by === 'string') {\n              return get(value, by)\n            } else {\n              return by(value, index, array)\n            }\n          })\n        }\n        if (sortKey !== '$key') {\n          if (isObject(value) && '$value' in value) value = value.$value\n        }\n        return [isObject(value) ? get(value, sortKey) : value]\n      }\n  const compare = function (a, b) {\n    if (sortMethod) {\n      return sortMethod(a.value, b.value)\n    }\n    for (let i = 0, len = a.key.length; i < len; i++) {\n      if (a.key[i] < b.key[i]) {\n        return -1\n      }\n      if (a.key[i] > b.key[i]) {\n        return 1\n      }\n    }\n    return 0\n  }\n  return array\n    .map((value, index) => {\n      return {\n        value,\n        index,\n        key: getKey ? getKey(value, index) : null,\n      }\n    })\n    .sort((a, b) => {\n      let order = compare(a, b)\n      if (!order) {\n        // make stable https://en.wikipedia.org/wiki/Sorting_algorithm#Stability\n        order = a.index - b.index\n      }\n      return order * +reverse\n    })\n    .map((item) => item.value)\n}\n\nexport const getColumnById = function <T>(\n  table: {\n    columns: TableColumnCtx<T>[]\n  },\n  columnId: string\n): null | TableColumnCtx<T> {\n  let column = null\n  table.columns.forEach((item) => {\n    if (item.id === columnId) {\n      column = item\n    }\n  })\n  return column\n}\n\nexport const getColumnByKey = function <T>(\n  table: {\n    columns: TableColumnCtx<T>[]\n  },\n  columnKey: string\n): TableColumnCtx<T> {\n  let column = null\n  for (let i = 0; i < table.columns.length; i++) {\n    const item = table.columns[i]\n    if (item.columnKey === columnKey) {\n      column = item\n      break\n    }\n  }\n  if (!column)\n    throwError('ElTable', `No column matching with column-key: ${columnKey}`)\n  return column\n}\n\nexport const getColumnByCell = function <T>(\n  table: {\n    columns: TableColumnCtx<T>[]\n  },\n  cell: HTMLElement,\n  namespace: string\n): null | TableColumnCtx<T> {\n  const matches = (cell.className || '').match(\n    new RegExp(`${namespace}-table_[^\\\\s]+`, 'gm')\n  )\n  if (matches) {\n    return getColumnById(table, matches[0])\n  }\n  return null\n}\n\nexport const getRowIdentity = <T>(\n  row: T,\n  rowKey: string | ((row: T) => any)\n): string => {\n  if (!row) throw new Error('Row is required when get row identity')\n  if (typeof rowKey === 'string') {\n    if (!rowKey.includes('.')) {\n      return `${row[rowKey]}`\n    }\n    const key = rowKey.split('.')\n    let current = row\n    for (const element of key) {\n      current = current[element]\n    }\n    return `${current}`\n  } else if (typeof rowKey === 'function') {\n    return rowKey.call(null, row)\n  }\n}\n\nexport const getKeysMap = function <T>(\n  array: T[],\n  rowKey: string\n): Record<string, { row: T; index: number }> {\n  const arrayMap = {}\n  ;(array || []).forEach((row, index) => {\n    arrayMap[getRowIdentity(row, rowKey)] = { row, index }\n  })\n  return arrayMap\n}\n\nexport function mergeOptions<T, K>(defaults: T, config: K): T & K {\n  const options = {} as T & K\n  let key\n  for (key in defaults) {\n    options[key] = defaults[key]\n  }\n  for (key in config) {\n    if (hasOwn(config as unknown as Record<string, any>, key)) {\n      const value = config[key]\n      if (typeof value !== 'undefined') {\n        options[key] = value\n      }\n    }\n  }\n  return options\n}\n\nexport function parseWidth(width: number | string): number | string {\n  if (width === '') return width\n  if (width !== undefined) {\n    width = Number.parseInt(width as string, 10)\n    if (Number.isNaN(width)) {\n      width = ''\n    }\n  }\n  return width\n}\n\nexport function parseMinWidth(minWidth: number | string): number | string {\n  if (minWidth === '') return minWidth\n  if (minWidth !== undefined) {\n    minWidth = parseWidth(minWidth)\n    if (Number.isNaN(minWidth)) {\n      minWidth = 80\n    }\n  }\n  return minWidth\n}\n\nexport function parseHeight(height: number | string) {\n  if (typeof height === 'number') {\n    return height\n  }\n  if (typeof height === 'string') {\n    if (/^\\d+(?:px)?$/.test(height)) {\n      return Number.parseInt(height, 10)\n    } else {\n      return height\n    }\n  }\n  return null\n}\n\n// https://github.com/reduxjs/redux/blob/master/src/compose.js\nexport function compose(...funcs) {\n  if (funcs.length === 0) {\n    return (arg) => arg\n  }\n  if (funcs.length === 1) {\n    return funcs[0]\n  }\n  return funcs.reduce(\n    (a, b) =>\n      (...args) =>\n        a(b(...args))\n  )\n}\n\nexport function toggleRowStatus<T>(\n  statusArr: T[],\n  row: T,\n  newVal: boolean\n): boolean {\n  let changed = false\n  const index = statusArr.indexOf(row)\n  const included = index !== -1\n\n  const toggleStatus = (type: 'add' | 'remove') => {\n    if (type === 'add') {\n      statusArr.push(row)\n    } else {\n      statusArr.splice(index, 1)\n    }\n    changed = true\n    if (isArray(row.children)) {\n      row.children.forEach((item) => {\n        toggleRowStatus(statusArr, item, newVal ?? !included)\n      })\n    }\n  }\n\n  if (isBoolean(newVal)) {\n    if (newVal && !included) {\n      toggleStatus('add')\n    } else if (!newVal && included) {\n      toggleStatus('remove')\n    }\n  } else {\n    included ? toggleStatus('remove') : toggleStatus('add')\n  }\n  return changed\n}\n\nexport function walkTreeNode(\n  root,\n  cb,\n  childrenKey = 'children',\n  lazyKey = 'hasChildren'\n) {\n  const isNil = (array) => !(Array.isArray(array) && array.length)\n\n  function _walker(parent, children, level) {\n    cb(parent, children, level)\n    children.forEach((item) => {\n      if (item[lazyKey]) {\n        cb(item, null, level + 1)\n        return\n      }\n      const children = item[childrenKey]\n      if (!isNil(children)) {\n        _walker(item, children, level + 1)\n      }\n    })\n  }\n\n  root.forEach((item) => {\n    if (item[lazyKey]) {\n      cb(item, null, 0)\n      return\n    }\n    const children = item[childrenKey]\n    if (!isNil(children)) {\n      _walker(item, children, 0)\n    }\n  })\n}\n\nexport let removePopper\n\nexport function createTablePopper(\n  parentNode: HTMLElement | undefined,\n  trigger: HTMLElement,\n  popperContent: string,\n  nextZIndex: () => number,\n  tooltipOptions?: TableOverflowTooltipOptions\n) {\n  // TODO transition\n  tooltipOptions = merge(\n    {\n      enterable: true,\n      showArrow: true,\n    } as TableOverflowTooltipOptions,\n    tooltipOptions\n  )\n  const ns = parentNode?.dataset.prefix\n  const scrollContainer = parentNode?.querySelector(`.${ns}-scrollbar__wrap`)\n  function renderContent(): HTMLDivElement {\n    const isLight = tooltipOptions.effect === 'light'\n    const content = document.createElement('div')\n    content.className = [\n      `${ns}-popper`,\n      isLight ? 'is-light' : 'is-dark',\n      tooltipOptions.popperClass || '',\n    ].join(' ')\n    popperContent = escapeHtml(popperContent)\n    content.innerHTML = popperContent\n    content.style.zIndex = String(nextZIndex())\n    // Avoid side effects caused by append to body\n    parentNode?.appendChild(content)\n    return content\n  }\n  function renderArrow(): HTMLDivElement {\n    const arrow = document.createElement('div')\n    arrow.className = `${ns}-popper__arrow`\n    return arrow\n  }\n  function togglePopperVisible(display: 'none' | 'block') {\n    return {\n      name: 'updateState',\n      enabled: true,\n      phase: 'beforeWrite',\n      fn: ({ state }) => {\n        state.styles.popper.display = display\n      },\n      requires: ['computeStyles'],\n    }\n  }\n  function showPopper() {\n    if (tooltipOptions.showAfter) {\n      popperInstance?.setOptions({\n        modifiers: [togglePopperVisible('block')],\n      })\n    }\n    popperInstance?.update()\n  }\n\n  const triggerChanged = removePopper?.trigger !== trigger\n  removePopper?.()\n  removePopper = () => {\n    try {\n      popperInstance && popperInstance.destroy()\n      content && parentNode?.removeChild(content)\n      trigger.removeEventListener('mouseenter', onOpen)\n      trigger.removeEventListener('mouseleave', onClose)\n      scrollContainer?.removeEventListener('scroll', removePopper)\n      removePopper = undefined\n    } catch {}\n  }\n  removePopper.trigger = trigger\n\n  let popperInstance: Nullable<PopperInstance> = null\n  const { onOpen, onClose } = useDelayedToggle({\n    showAfter: tooltipOptions.showAfter,\n    hideAfter: tooltipOptions.hideAfter,\n    open: showPopper,\n    close: removePopper,\n  })\n  const content = renderContent()\n  if (tooltipOptions.enterable) {\n    content.onmouseenter = onOpen\n    content.onmouseleave = onClose\n  }\n  const modifiers = []\n  if (tooltipOptions.offset) {\n    modifiers.push({\n      name: 'offset',\n      options: {\n        offset: [0, tooltipOptions.offset],\n      },\n    })\n  }\n  if (tooltipOptions.showArrow) {\n    const arrow = content.appendChild(renderArrow())\n    modifiers.push({\n      name: 'arrow',\n      options: {\n        element: arrow,\n        padding: 10,\n      },\n    })\n  }\n  if (tooltipOptions.showAfter && triggerChanged) {\n    modifiers.push(togglePopperVisible('none'))\n  }\n  const popperOptions = tooltipOptions.popperOptions || {}\n  popperInstance = createPopper(trigger, content, {\n    placement: tooltipOptions.placement || 'top',\n    strategy: 'fixed',\n    ...popperOptions,\n    modifiers: popperOptions.modifiers\n      ? modifiers.concat(popperOptions.modifiers)\n      : modifiers,\n  })\n  trigger.addEventListener('mouseenter', onOpen)\n  trigger.addEventListener('mouseleave', onClose)\n  scrollContainer?.addEventListener('scroll', removePopper)\n  onOpen()\n  return popperInstance\n}\n\nfunction getCurrentColumns<T>(column: TableColumnCtx<T>): TableColumnCtx<T>[] {\n  if (column.children) {\n    return flatMap(column.children, getCurrentColumns)\n  } else {\n    return [column]\n  }\n}\n\nfunction getColSpan<T>(colSpan: number, column: TableColumnCtx<T>) {\n  return colSpan + column.colSpan\n}\n\nexport const isFixedColumn = <T>(\n  index: number,\n  fixed: string | boolean,\n  store: any,\n  realColumns?: TableColumnCtx<T>[]\n) => {\n  let start = 0\n  let after = index\n  const columns = store.states.columns.value\n  if (realColumns) {\n    // fixed column supported in grouped header\n    const curColumns = getCurrentColumns(realColumns[index])\n    const preColumns = columns.slice(0, columns.indexOf(curColumns[0]))\n\n    start = preColumns.reduce(getColSpan, 0)\n    after = start + curColumns.reduce(getColSpan, 0) - 1\n  } else {\n    start = index\n  }\n  let fixedLayout\n  switch (fixed) {\n    case 'left':\n      if (after < store.states.fixedLeafColumnsLength.value) {\n        fixedLayout = 'left'\n      }\n      break\n    case 'right':\n      if (\n        start >=\n        columns.length - store.states.rightFixedLeafColumnsLength.value\n      ) {\n        fixedLayout = 'right'\n      }\n      break\n    default:\n      if (after < store.states.fixedLeafColumnsLength.value) {\n        fixedLayout = 'left'\n      } else if (\n        start >=\n        columns.length - store.states.rightFixedLeafColumnsLength.value\n      ) {\n        fixedLayout = 'right'\n      }\n  }\n  return fixedLayout\n    ? {\n        direction: fixedLayout,\n        start,\n        after,\n      }\n    : {}\n}\n\nexport const getFixedColumnsClass = <T>(\n  namespace: string,\n  index: number,\n  fixed: string | boolean,\n  store: any,\n  realColumns?: TableColumnCtx<T>[],\n  offset = 0\n) => {\n  const classes: string[] = []\n  const { direction, start, after } = isFixedColumn(\n    index,\n    fixed,\n    store,\n    realColumns\n  )\n  if (direction) {\n    const isLeft = direction === 'left'\n    classes.push(`${namespace}-fixed-column--${direction}`)\n    if (\n      isLeft &&\n      after + offset === store.states.fixedLeafColumnsLength.value - 1\n    ) {\n      classes.push('is-last-column')\n    } else if (\n      !isLeft &&\n      start - offset ===\n        store.states.columns.value.length -\n          store.states.rightFixedLeafColumnsLength.value\n    ) {\n      classes.push('is-first-column')\n    }\n  }\n  return classes\n}\n\nfunction getOffset<T>(offset: number, column: TableColumnCtx<T>) {\n  return (\n    offset +\n    (column.realWidth === null || Number.isNaN(column.realWidth)\n      ? Number(column.width)\n      : column.realWidth)\n  )\n}\n\nexport const getFixedColumnOffset = <T>(\n  index: number,\n  fixed: string | boolean,\n  store: any,\n  realColumns?: TableColumnCtx<T>[]\n) => {\n  const {\n    direction,\n    start = 0,\n    after = 0,\n  } = isFixedColumn(index, fixed, store, realColumns)\n  if (!direction) {\n    return\n  }\n  const styles: any = {}\n  const isLeft = direction === 'left'\n  const columns = store.states.columns.value\n  if (isLeft) {\n    styles.left = columns.slice(0, start).reduce(getOffset, 0)\n  } else {\n    styles.right = columns\n      .slice(after + 1)\n      .reverse()\n      .reduce(getOffset, 0)\n  }\n  return styles\n}\n\nexport const ensurePosition = (style, key: string) => {\n  if (!style) return\n  if (!Number.isNaN(style[key])) {\n    style[key] = `${style[key]}px`\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAWY,MAAC,OAAO,GAAG,SAAS,KAAK,EAAE;AACvC,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,OAAO,CAAC,EAAE,GAAG,KAAK,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACjE,EAAE;AACU,MAAC,OAAO,GAAG,SAAS,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE;AAC7E,EAAE,IAAI,CAAC,OAAO,IAAI,CAAC,UAAU,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;AACvF,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;AACnC,IAAI,OAAO,GAAG,OAAO,KAAK,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAChD,GAAG,MAAM;AACT,IAAI,OAAO,GAAG,OAAO,IAAI,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC9C,GAAG;AACH,EAAE,MAAM,MAAM,GAAG,UAAU,GAAG,IAAI,GAAG,SAAS,KAAK,EAAE,KAAK,EAAE;AAC5D,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AAClC,QAAQ,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC;AAC1B,OAAO;AACP,MAAM,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK;AAChC,QAAQ,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE;AACpC,UAAU,OAAO,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AAChC,SAAS,MAAM;AACf,UAAU,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AACzC,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,IAAI,OAAO,KAAK,MAAM,EAAE;AAC5B,MAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,IAAI,KAAK;AAC9C,QAAQ,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC;AAC7B,KAAK;AACL,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,KAAK,CAAC,CAAC;AAC3D,GAAG,CAAC;AACJ,EAAE,MAAM,OAAO,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;AACjC,IAAI,IAAI,UAAU,EAAE;AACpB,MAAM,OAAO,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;AAC1C,KAAK;AACL,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AACtD,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;AAC/B,QAAQ,OAAO,CAAC,CAAC,CAAC;AAClB,OAAO;AACP,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;AAC/B,QAAQ,OAAO,CAAC,CAAC;AACjB,OAAO;AACP,KAAK;AACL,IAAI,OAAO,CAAC,CAAC;AACb,GAAG,CAAC;AACJ,EAAE,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK;AACrC,IAAI,OAAO;AACX,MAAM,KAAK;AACX,MAAM,KAAK;AACX,MAAM,GAAG,EAAE,MAAM,GAAG,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,IAAI;AAC/C,KAAK,CAAC;AACN,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AACpB,IAAI,IAAI,KAAK,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9B,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;AAChC,KAAK;AACL,IAAI,OAAO,KAAK,GAAG,CAAC,OAAO,CAAC;AAC5B,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC;AAC/B,EAAE;AACU,MAAC,aAAa,GAAG,SAAS,KAAK,EAAE,QAAQ,EAAE;AACvD,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC;AACpB,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AAClC,IAAI,IAAI,IAAI,CAAC,EAAE,KAAK,QAAQ,EAAE;AAC9B,MAAM,MAAM,GAAG,IAAI,CAAC;AACpB,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,MAAM,CAAC;AAChB,EAAE;AACU,MAAC,cAAc,GAAG,SAAS,KAAK,EAAE,SAAS,EAAE;AACzD,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC;AACpB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjD,IAAI,MAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAClC,IAAI,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE;AACtC,MAAM,MAAM,GAAG,IAAI,CAAC;AACpB,MAAM,MAAM;AACZ,KAAK;AACL,GAAG;AACH,EAAE,IAAI,CAAC,MAAM;AACb,IAAI,UAAU,CAAC,SAAS,EAAE,CAAC,oCAAoC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;AAC9E,EAAE,OAAO,MAAM,CAAC;AAChB,EAAE;AACU,MAAC,eAAe,GAAG,SAAS,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE;AAChE,EAAE,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,SAAS,IAAI,EAAE,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,cAAc,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AAC/F,EAAE,IAAI,OAAO,EAAE;AACf,IAAI,OAAO,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5C,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,EAAE;AACU,MAAC,cAAc,GAAG,CAAC,GAAG,EAAE,MAAM,KAAK;AAC/C,EAAE,IAAI,CAAC,GAAG;AACV,IAAI,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;AAC7D,EAAE,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AAClC,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AAC/B,MAAM,OAAO,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC9B,KAAK;AACL,IAAI,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAClC,IAAI,IAAI,OAAO,GAAG,GAAG,CAAC;AACtB,IAAI,KAAK,MAAM,OAAO,IAAI,GAAG,EAAE;AAC/B,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AACjC,KAAK;AACL,IAAI,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;AACxB,GAAG,MAAM,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;AAC3C,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAClC,GAAG;AACH,EAAE;AACU,MAAC,UAAU,GAAG,SAAS,KAAK,EAAE,MAAM,EAAE;AAClD,EAAE,MAAM,QAAQ,GAAG,EAAE,CAAC;AACtB,EAAE,CAAC,KAAK,IAAI,EAAE,EAAE,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,KAAK;AACxC,IAAI,QAAQ,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;AAC3D,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,QAAQ,CAAC;AAClB,EAAE;AACK,SAAS,YAAY,CAAC,QAAQ,EAAE,MAAM,EAAE;AAC/C,EAAE,MAAM,OAAO,GAAG,EAAE,CAAC;AACrB,EAAE,IAAI,GAAG,CAAC;AACV,EAAE,KAAK,GAAG,IAAI,QAAQ,EAAE;AACxB,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;AACjC,GAAG;AACH,EAAE,KAAK,GAAG,IAAI,MAAM,EAAE;AACtB,IAAI,IAAI,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;AAC7B,MAAM,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;AAChC,MAAM,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AACxC,QAAQ,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAC7B,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,OAAO,OAAO,CAAC;AACjB,CAAC;AACM,SAAS,UAAU,CAAC,KAAK,EAAE;AAClC,EAAE,IAAI,KAAK,KAAK,EAAE;AAClB,IAAI,OAAO,KAAK,CAAC;AACjB,EAAE,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE;AACxB,IAAI,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AACvC,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;AAC7B,MAAM,KAAK,GAAG,EAAE,CAAC;AACjB,KAAK;AACL,GAAG;AACH,EAAE,OAAO,KAAK,CAAC;AACf,CAAC;AACM,SAAS,aAAa,CAAC,QAAQ,EAAE;AACxC,EAAE,IAAI,QAAQ,KAAK,EAAE;AACrB,IAAI,OAAO,QAAQ,CAAC;AACpB,EAAE,IAAI,QAAQ,KAAK,KAAK,CAAC,EAAE;AAC3B,IAAI,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC;AACpC,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;AAChC,MAAM,QAAQ,GAAG,EAAE,CAAC;AACpB,KAAK;AACL,GAAG;AACH,EAAE,OAAO,QAAQ,CAAC;AAClB,CAAC;AACM,SAAS,WAAW,CAAC,MAAM,EAAE;AACpC,EAAE,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AAClC,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH,EAAE,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AAClC,IAAI,IAAI,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;AACrC,MAAM,OAAO,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;AACzC,KAAK,MAAM;AACX,MAAM,OAAO,MAAM,CAAC;AACpB,KAAK;AACL,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,CAAC;AACM,SAAS,OAAO,CAAC,GAAG,KAAK,EAAE;AAClC,EAAE,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AAC1B,IAAI,OAAO,CAAC,GAAG,KAAK,GAAG,CAAC;AACxB,GAAG;AACH,EAAE,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AAC1B,IAAI,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;AACpB,GAAG;AACH,EAAE,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC;AACM,SAAS,eAAe,CAAC,SAAS,EAAE,GAAG,EAAE,MAAM,EAAE;AACxD,EAAE,IAAI,OAAO,GAAG,KAAK,CAAC;AACtB,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACvC,EAAE,MAAM,QAAQ,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC;AAChC,EAAE,MAAM,YAAY,GAAG,CAAC,IAAI,KAAK;AACjC,IAAI,IAAI,IAAI,KAAK,KAAK,EAAE;AACxB,MAAM,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC1B,KAAK,MAAM;AACX,MAAM,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACjC,KAAK;AACL,IAAI,OAAO,GAAG,IAAI,CAAC;AACnB,IAAI,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;AAC/B,MAAM,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AACrC,QAAQ,eAAe,CAAC,SAAS,EAAE,IAAI,EAAE,MAAM,IAAI,IAAI,GAAG,MAAM,GAAG,CAAC,QAAQ,CAAC,CAAC;AAC9E,OAAO,CAAC,CAAC;AACT,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,IAAI,SAAS,CAAC,MAAM,CAAC,EAAE;AACzB,IAAI,IAAI,MAAM,IAAI,CAAC,QAAQ,EAAE;AAC7B,MAAM,YAAY,CAAC,KAAK,CAAC,CAAC;AAC1B,KAAK,MAAM,IAAI,CAAC,MAAM,IAAI,QAAQ,EAAE;AACpC,MAAM,YAAY,CAAC,QAAQ,CAAC,CAAC;AAC7B,KAAK;AACL,GAAG,MAAM;AACT,IAAI,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;AAC5D,GAAG;AACH,EAAE,OAAO,OAAO,CAAC;AACjB,CAAC;AACM,SAAS,YAAY,CAAC,IAAI,EAAE,EAAE,EAAE,WAAW,GAAG,UAAU,EAAE,OAAO,GAAG,aAAa,EAAE;AAC1F,EAAE,MAAM,KAAK,GAAG,CAAC,KAAK,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;AACnE,EAAE,SAAS,OAAO,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE;AAC5C,IAAI,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;AAChC,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AAC/B,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE;AACzB,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;AAClC,QAAQ,OAAO;AACf,OAAO;AACP,MAAM,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;AAC1C,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;AAC7B,QAAQ,OAAO,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;AAC5C,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AACzB,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE;AACvB,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACxB,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;AACvC,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;AAC1B,MAAM,OAAO,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;AACjC,KAAK;AACL,GAAG,CAAC,CAAC;AACL,CAAC;AACS,IAAC,aAAa;AACjB,SAAS,iBAAiB,CAAC,UAAU,EAAE,OAAO,EAAE,aAAa,EAAE,UAAU,EAAE,cAAc,EAAE;AAClG,EAAE,cAAc,GAAG,KAAK,CAAC;AACzB,IAAI,SAAS,EAAE,IAAI;AACnB,IAAI,SAAS,EAAE,IAAI;AACnB,GAAG,EAAE,cAAc,CAAC,CAAC;AACrB,EAAE,MAAM,EAAE,GAAG,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC;AACrE,EAAE,MAAM,eAAe,GAAG,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC3G,EAAE,SAAS,aAAa,GAAG;AAC3B,IAAI,MAAM,OAAO,GAAG,cAAc,CAAC,MAAM,KAAK,OAAO,CAAC;AACtD,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACnD,IAAI,QAAQ,CAAC,SAAS,GAAG;AACzB,MAAM,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC;AACpB,MAAM,OAAO,GAAG,UAAU,GAAG,SAAS;AACtC,MAAM,cAAc,CAAC,WAAW,IAAI,EAAE;AACtC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAChB,IAAI,aAAa,GAAG,UAAU,CAAC,aAAa,CAAC,CAAC;AAC9C,IAAI,QAAQ,CAAC,SAAS,GAAG,aAAa,CAAC;AACvC,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC;AACjD,IAAI,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACnE,IAAI,OAAO,QAAQ,CAAC;AACpB,GAAG;AACH,EAAE,SAAS,WAAW,GAAG;AACzB,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAChD,IAAI,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE,EAAE,CAAC,cAAc,CAAC,CAAC;AAC5C,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,SAAS,mBAAmB,CAAC,OAAO,EAAE;AACxC,IAAI,OAAO;AACX,MAAM,IAAI,EAAE,aAAa;AACzB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,KAAK,EAAE,aAAa;AAC1B,MAAM,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK;AACzB,QAAQ,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;AAC9C,OAAO;AACP,MAAM,QAAQ,EAAE,CAAC,eAAe,CAAC;AACjC,KAAK,CAAC;AACN,GAAG;AACH,EAAE,SAAS,UAAU,GAAG;AACxB,IAAI,IAAI,cAAc,CAAC,SAAS,EAAE;AAClC,MAAM,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,cAAc,CAAC,UAAU,CAAC;AAClE,QAAQ,SAAS,EAAE,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;AACjD,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC;AAC9D,GAAG;AACH,EAAE,MAAM,cAAc,GAAG,CAAC,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,YAAY,CAAC,OAAO,MAAM,OAAO,CAAC;AAC5F,EAAE,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,YAAY,EAAE,CAAC;AACjD,EAAE,YAAY,GAAG,MAAM;AACvB,IAAI,IAAI;AACR,MAAM,cAAc,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;AACjD,MAAM,OAAO,KAAK,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;AACjF,MAAM,OAAO,CAAC,mBAAmB,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;AACxD,MAAM,OAAO,CAAC,mBAAmB,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;AACzD,MAAM,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,eAAe,CAAC,mBAAmB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;AACrG,MAAM,YAAY,GAAG,KAAK,CAAC,CAAC;AAC5B,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,YAAY,CAAC,OAAO,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,cAAc,GAAG,IAAI,CAAC;AAC5B,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,gBAAgB,CAAC;AAC/C,IAAI,SAAS,EAAE,cAAc,CAAC,SAAS;AACvC,IAAI,SAAS,EAAE,cAAc,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,UAAU;AACpB,IAAI,KAAK,EAAE,YAAY;AACvB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,OAAO,GAAG,aAAa,EAAE,CAAC;AAClC,EAAE,IAAI,cAAc,CAAC,SAAS,EAAE;AAChC,IAAI,OAAO,CAAC,YAAY,GAAG,MAAM,CAAC;AAClC,IAAI,OAAO,CAAC,YAAY,GAAG,OAAO,CAAC;AACnC,GAAG;AACH,EAAE,MAAM,SAAS,GAAG,EAAE,CAAC;AACvB,EAAE,IAAI,cAAc,CAAC,MAAM,EAAE;AAC7B,IAAI,SAAS,CAAC,IAAI,CAAC;AACnB,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,OAAO,EAAE;AACf,QAAQ,MAAM,EAAE,CAAC,CAAC,EAAE,cAAc,CAAC,MAAM,CAAC;AAC1C,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,IAAI,cAAc,CAAC,SAAS,EAAE;AAChC,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,CAAC;AACrD,IAAI,SAAS,CAAC,IAAI,CAAC;AACnB,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,OAAO,EAAE;AACf,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,OAAO,EAAE,EAAE;AACnB,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,IAAI,cAAc,CAAC,SAAS,IAAI,cAAc,EAAE;AAClD,IAAI,SAAS,CAAC,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC;AAChD,GAAG;AACH,EAAE,MAAM,aAAa,GAAG,cAAc,CAAC,aAAa,IAAI,EAAE,CAAC;AAC3D,EAAE,cAAc,GAAG,YAAY,CAAC,OAAO,EAAE,OAAO,EAAE;AAClD,IAAI,SAAS,EAAE,cAAc,CAAC,SAAS,IAAI,KAAK;AAChD,IAAI,QAAQ,EAAE,OAAO;AACrB,IAAI,GAAG,aAAa;AACpB,IAAI,SAAS,EAAE,aAAa,CAAC,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,SAAS;AAC9F,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,CAAC,gBAAgB,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;AACjD,EAAE,OAAO,CAAC,gBAAgB,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;AAClD,EAAE,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,eAAe,CAAC,gBAAgB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;AAC9F,EAAE,MAAM,EAAE,CAAC;AACX,EAAE,OAAO,cAAc,CAAC;AACxB,CAAC;AACD,SAAS,iBAAiB,CAAC,MAAM,EAAE;AACnC,EAAE,IAAI,MAAM,CAAC,QAAQ,EAAE;AACvB,IAAI,OAAO,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;AACvD,GAAG,MAAM;AACT,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC;AACpB,GAAG;AACH,CAAC;AACD,SAAS,UAAU,CAAC,OAAO,EAAE,MAAM,EAAE;AACrC,EAAE,OAAO,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;AAClC,CAAC;AACW,MAAC,aAAa,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,KAAK;AACnE,EAAE,IAAI,KAAK,GAAG,CAAC,CAAC;AAChB,EAAE,IAAI,KAAK,GAAG,KAAK,CAAC;AACpB,EAAE,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;AAC7C,EAAE,IAAI,WAAW,EAAE;AACnB,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;AAC7D,IAAI,MAAM,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxE,IAAI,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;AAC7C,IAAI,KAAK,GAAG,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACzD,GAAG,MAAM;AACT,IAAI,KAAK,GAAG,KAAK,CAAC;AAClB,GAAG;AACH,EAAE,IAAI,WAAW,CAAC;AAClB,EAAE,QAAQ,KAAK;AACf,IAAI,KAAK,MAAM;AACf,MAAM,IAAI,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC,KAAK,EAAE;AAC7D,QAAQ,WAAW,GAAG,MAAM,CAAC;AAC7B,OAAO;AACP,MAAM,MAAM;AACZ,IAAI,KAAK,OAAO;AAChB,MAAM,IAAI,KAAK,IAAI,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,2BAA2B,CAAC,KAAK,EAAE;AACpF,QAAQ,WAAW,GAAG,OAAO,CAAC;AAC9B,OAAO;AACP,MAAM,MAAM;AACZ,IAAI;AACJ,MAAM,IAAI,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC,KAAK,EAAE;AAC7D,QAAQ,WAAW,GAAG,MAAM,CAAC;AAC7B,OAAO,MAAM,IAAI,KAAK,IAAI,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,2BAA2B,CAAC,KAAK,EAAE;AAC3F,QAAQ,WAAW,GAAG,OAAO,CAAC;AAC9B,OAAO;AACP,GAAG;AACH,EAAE,OAAO,WAAW,GAAG;AACvB,IAAI,SAAS,EAAE,WAAW;AAC1B,IAAI,KAAK;AACT,IAAI,KAAK;AACT,GAAG,GAAG,EAAE,CAAC;AACT,EAAE;AACU,MAAC,oBAAoB,GAAG,CAAC,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,GAAG,CAAC,KAAK;AACjG,EAAE,MAAM,OAAO,GAAG,EAAE,CAAC;AACrB,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;AACtF,EAAE,IAAI,SAAS,EAAE;AACjB,IAAI,MAAM,MAAM,GAAG,SAAS,KAAK,MAAM,CAAC;AACxC,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,SAAS,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;AAC5D,IAAI,IAAI,MAAM,IAAI,KAAK,GAAG,MAAM,KAAK,KAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC,KAAK,GAAG,CAAC,EAAE;AACpF,MAAM,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACrC,KAAK,MAAM,IAAI,CAAC,MAAM,IAAI,KAAK,GAAG,MAAM,KAAK,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,2BAA2B,CAAC,KAAK,EAAE;AACjI,MAAM,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AACtC,KAAK;AACL,GAAG;AACH,EAAE,OAAO,OAAO,CAAC;AACjB,EAAE;AACF,SAAS,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE;AACnC,EAAE,OAAO,MAAM,IAAI,MAAM,CAAC,SAAS,KAAK,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;AAC1H,CAAC;AACW,MAAC,oBAAoB,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,KAAK;AAC1E,EAAE,MAAM;AACR,IAAI,SAAS;AACb,IAAI,KAAK,GAAG,CAAC;AACb,IAAI,KAAK,GAAG,CAAC;AACb,GAAG,GAAG,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;AACtD,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,OAAO;AACX,GAAG;AACH,EAAE,MAAM,MAAM,GAAG,EAAE,CAAC;AACpB,EAAE,MAAM,MAAM,GAAG,SAAS,KAAK,MAAM,CAAC;AACtC,EAAE,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;AAC7C,EAAE,IAAI,MAAM,EAAE;AACd,IAAI,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;AAC/D,GAAG,MAAM;AACT,IAAI,MAAM,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;AAC3E,GAAG;AACH,EAAE,OAAO,MAAM,CAAC;AAChB,EAAE;AACU,MAAC,cAAc,GAAG,CAAC,KAAK,EAAE,GAAG,KAAK;AAC9C,EAAE,IAAI,CAAC,KAAK;AACZ,IAAI,OAAO;AACX,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE;AACjC,IAAI,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AACnC,GAAG;AACH;;;;"}