{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/table/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON>oopInstall } from '@element-plus/utils'\nimport Table from './src/table.vue'\nimport TableColumn from './src/tableColumn'\n\nexport const ElTable = withInstall(Table, {\n  TableColumn,\n})\nexport default ElTable\nexport const ElTableColumn = withNoopInstall(TableColumn)\n\nexport type TableInstance = InstanceType<typeof Table>\n\nexport type TableColumnInstance = InstanceType<typeof TableColumn>\n\nexport type {\n  SummaryMethod,\n  Table,\n  TableProps,\n  TableRefs,\n  ColumnCls,\n  ColumnStyle,\n  CellCls,\n  CellStyle,\n  TreeNode,\n  RenderRowData,\n  Sort,\n  Filter,\n  TableColumnCtx,\n} from './src/table/defaults'\n"], "names": ["TableColumn"], "mappings": ";;;;;;AAGY,MAAC,OAAO,GAAG,WAAW,CAAC,KAAK,EAAE;AAC1C,eAAEA,eAAW;AACb,CAAC,EAAE;AAES,MAAC,aAAa,GAAG,eAAe,CAACA,eAAW;;;;"}