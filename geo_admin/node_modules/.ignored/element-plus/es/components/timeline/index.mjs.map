{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/timeline/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON>oopInstall } from '@element-plus/utils'\nimport Timeline from './src/timeline'\nimport TimelineItem from './src/timeline-item.vue'\n\nexport const ElTimeline = withInstall(Timeline, {\n  TimelineItem,\n})\nexport default ElTimeline\nexport const ElTimelineItem = withNoopInstall(TimelineItem)\n\nexport * from './src/timeline'\nexport * from './src/timeline-item'\n"], "names": [], "mappings": ";;;;;;AAGY,MAAC,UAAU,GAAG,WAAW,CAAC,QAAQ,EAAE;AAChD,EAAE,YAAY;AACd,CAAC,EAAE;AAES,MAAC,cAAc,GAAG,eAAe,CAAC,YAAY;;;;"}