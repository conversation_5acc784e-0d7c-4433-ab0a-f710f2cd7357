import '../../utils/index.mjs';
import Select from './src/select.mjs';
import Option from './src/option.mjs';
import OptionGroup from './src/option-group.mjs';
export { selectGroupKey, selectKey } from './src/token.mjs';
import { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';

const ElSelect = withInstall(Select, {
  Option,
  OptionGroup
});
const ElOption = withNoopInstall(Option);
const ElOptionGroup = withNoopInstall(OptionGroup);

export { ElOption, ElOptionGroup, ElSelect, ElSelect as default };
//# sourceMappingURL=index.mjs.map
