{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/select/index.ts"], "sourcesContent": ["import { withInstall, withNoopInstall } from '@element-plus/utils'\n\nimport Select from './src/select.vue'\nimport Option from './src/option.vue'\nimport OptionGroup from './src/option-group.vue'\n\nexport const ElSelect = withInstall(Select, {\n  Option,\n  OptionGroup,\n})\nexport default ElSelect\nexport const ElOption = withNoopInstall(Option)\nexport const ElOptionGroup = withNoopInstall(OptionGroup)\n\nexport * from './src/token'\n"], "names": [], "mappings": ";;;;;;;AAIY,MAAC,QAAQ,GAAG,WAAW,CAAC,MAAM,EAAE;AAC5C,EAAE,MAAM;AACR,EAAE,WAAW;AACb,CAAC,EAAE;AAES,MAAC,QAAQ,GAAG,eAAe,CAAC,MAAM,EAAE;AACpC,MAAC,aAAa,GAAG,eAAe,CAAC,WAAW;;;;"}