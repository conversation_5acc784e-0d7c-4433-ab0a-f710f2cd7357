{"version": 3, "file": "option.mjs", "sources": ["../../../../../../packages/components/select/src/option.vue"], "sourcesContent": ["<template>\n  <li\n    v-show=\"visible\"\n    :id=\"id\"\n    :class=\"containerKls\"\n    role=\"option\"\n    :aria-disabled=\"isDisabled || undefined\"\n    :aria-selected=\"itemSelected\"\n    @mouseenter=\"hoverItem\"\n    @click.stop=\"selectOptionClick\"\n  >\n    <slot>\n      <span>{{ currentLabel }}</span>\n    </slot>\n  </li>\n</template>\n\n<script lang=\"ts\">\n// @ts-nocheck\nimport {\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  nextTick,\n  onBeforeUnmount,\n  reactive,\n  toRefs,\n  unref,\n} from 'vue'\nimport { useId, useNamespace } from '@element-plus/hooks'\nimport { useOption } from './useOption'\nimport type { SelectOptionProxy } from './token'\n\nexport default defineComponent({\n  name: 'ElOption',\n  componentName: 'ElOption',\n\n  props: {\n    /**\n     * @description value of option\n     */\n    value: {\n      required: true,\n      type: [String, Number, Boolean, Object],\n    },\n    /**\n     * @description label of option, same as `value` if omitted\n     */\n    label: [String, Number],\n    created: <PERSON><PERSON><PERSON>,\n    /**\n     * @description whether option is disabled\n     */\n    disabled: Boolean,\n  },\n\n  setup(props) {\n    const ns = useNamespace('select')\n    const id = useId()\n\n    const containerKls = computed(() => [\n      ns.be('dropdown', 'item'),\n      ns.is('disabled', unref(isDisabled)),\n      {\n        selected: unref(itemSelected),\n        hover: unref(hover),\n      },\n    ])\n\n    const states = reactive({\n      index: -1,\n      groupDisabled: false,\n      visible: true,\n      hitState: false,\n      hover: false,\n    })\n\n    const { currentLabel, itemSelected, isDisabled, select, hoverItem } =\n      useOption(props, states)\n\n    const { visible, hover } = toRefs(states)\n\n    const vm = getCurrentInstance().proxy\n\n    select.onOptionCreate(vm as unknown as SelectOptionProxy)\n\n    onBeforeUnmount(() => {\n      const key = (vm as unknown as SelectOptionProxy).value\n      const { selected } = select\n      const selectedOptions = select.props.multiple ? selected : [selected]\n      const doesSelected = selectedOptions.some((item) => {\n        return item.value === (vm as unknown as SelectOptionProxy).value\n      })\n      // if option is not selected, remove it from cache\n      nextTick(() => {\n        if (select.cachedOptions.get(key) === vm && !doesSelected) {\n          select.cachedOptions.delete(key)\n        }\n      })\n      select.onOptionDestroy(key, vm)\n    })\n\n    function selectOptionClick() {\n      if (props.disabled !== true && states.groupDisabled !== true) {\n        select.handleOptionSelect(vm)\n      }\n    }\n\n    return {\n      ns,\n      id,\n      containerKls,\n      currentLabel,\n      itemSelected,\n      isDisabled,\n      select,\n      hoverItem,\n      visible,\n      hover,\n      selectOptionClick,\n      states,\n    }\n  },\n})\n</script>\n"], "names": ["_createElementBlock", "_normalizeClass", "_renderSlot", "_createElementVNode"], "mappings": ";;;;;;;AAiCA,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,UAAA;AAAA,EACN,aAAe,EAAA,UAAA;AAAA,EAEf,KAAO,EAAA;AAAA,IAIL,KAAO,EAAA;AAAA,MACL,QAAU,EAAA,IAAA;AAAA,MACV,IAAM,EAAA,CAAC,MAAQ,EAAA,MAAA,EAAQ,SAAS,MAAM,CAAA;AAAA,KACxC;AAAA,IAIA,KAAA,EAAO,CAAC,MAAA,EAAQ,MAAM,CAAA;AAAA,IACtB,OAAS,EAAA,OAAA;AAAA,IAIT,QAAU,EAAA,OAAA;AAAA,GACZ;AAAA,EAEA,MAAM,KAAO,EAAA;AACX,IAAM,MAAA,EAAA,GAAK,aAAa,QAAQ,CAAA,CAAA;AAChC,IAAA,MAAM,KAAK,KAAM,EAAA,CAAA;AAEjB,IAAM,MAAA,YAAA,GAAe,SAAS,MAAM;AAAA,MAClC,EAAA,CAAG,EAAG,CAAA,UAAA,EAAY,MAAM,CAAA;AAAA,MACxB,EAAG,CAAA,EAAA,CAAG,UAAY,EAAA,KAAA,CAAM,UAAU,CAAC,CAAA;AAAA,MACnC;AAAA,QACE,QAAA,EAAU,MAAM,YAAY,CAAA;AAAA,QAC5B,KAAA,EAAO,MAAM,KAAK,CAAA;AAAA,OACpB;AAAA,KACD,CAAA,CAAA;AAED,IAAA,MAAM,SAAS,QAAS,CAAA;AAAA,MACtB,KAAO,EAAA,CAAA,CAAA;AAAA,MACP,aAAe,EAAA,KAAA;AAAA,MACf,OAAS,EAAA,IAAA;AAAA,MACT,QAAU,EAAA,KAAA;AAAA,MACV,KAAO,EAAA,KAAA;AAAA,KACR,CAAA,CAAA;AAED,IAAM,MAAA,EAAE,cAAc,YAAc,EAAA,UAAA,EAAY,QAAQ,SACtD,EAAA,GAAA,SAAA,CAAU,OAAO,MAAM,CAAA,CAAA;AAEzB,IAAA,MAAM,EAAE,OAAA,EAAS,KAAU,EAAA,GAAA,MAAA,CAAO,MAAM,CAAA,CAAA;AAExC,IAAM,MAAA,EAAA,GAAK,oBAAqB,CAAA,KAAA,CAAA;AAEhC,IAAA,MAAA,CAAO,eAAe,EAAkC,CAAA,CAAA;AAExD,IAAA,eAAA,CAAgB,MAAM;AACpB,MAAA,MAAM,MAAO,EAAoC,CAAA,KAAA,CAAA;AACjD,MAAA,MAAM,EAAE,QAAa,EAAA,GAAA,MAAA,CAAA;AACrB,MAAA,MAAM,kBAAkB,MAAO,CAAA,KAAA,CAAM,QAAW,GAAA,QAAA,GAAW,CAAC,QAAQ,CAAA,CAAA;AACpE,MAAA,MAAM,YAAe,GAAA,eAAA,CAAgB,IAAK,CAAA,CAAC,IAAS,KAAA;AAClD,QAAO,OAAA,IAAA,CAAK,UAAW,EAAoC,CAAA,KAAA,CAAA;AAAA,OAC5D,CAAA,CAAA;AAED,MAAA,QAAA,CAAS,MAAM;AACb,QAAA,IAAI,OAAO,aAAc,CAAA,GAAA,CAAI,GAAG,CAAM,KAAA,EAAA,IAAM,CAAC,YAAc,EAAA;AACzD,UAAO,MAAA,CAAA,aAAA,CAAc,OAAO,GAAG,CAAA,CAAA;AAAA,SACjC;AAAA,OACD,CAAA,CAAA;AACD,MAAO,MAAA,CAAA,eAAA,CAAgB,KAAK,EAAE,CAAA,CAAA;AAAA,KAC/B,CAAA,CAAA;AAED,IAA6B,SAAA,iBAAA,GAAA;AAC3B,MAAA,IAAI,KAAM,CAAA,QAAA,KAAa,IAAQ,IAAA,MAAA,CAAO,kBAAkB,IAAM,EAAA;AAC5D,QAAA,MAAA,CAAO,mBAAmB,EAAE,CAAA,CAAA;AAAA,OAC9B;AAAA,KACF;AAEA,IAAO,OAAA;AAAA,MACL,EAAA;AAAA,MACA,EAAA;AAAA,MACA,YAAA;AAAA,MACA,YAAA;AAAA,MACA,YAAA;AAAA,MACA,UAAA;AAAA,MACA,MAAA;AAAA,MACA,SAAA;AAAA,MACA,OAAA;AAAA,MACA,KAAA;AAAA,MACA,iBAAA;AAAA,MACA,MAAA;AAAA,KACF,CAAA;AAAA,GACF;AACF,CAAC,CAAA,CAAA;;;sCA1HCA,kBAaK,CAAA,IAAA,EAAA;AAAA,IAXF,EAAI,EAAA,IAAA,CAAA,EAAA;AAAA,IACJ,KAAA,EAAKC,eAAE,IAAY,CAAA,YAAA,CAAA;AAAA,IACpB,IAAK,EAAA,QAAA;AAAA,IACJ,iBAAe,IAAc,CAAA,UAAA,IAAA,KAAA,CAAA;AAAA,IAC7B,eAAe,EAAA,IAAA,CAAA,YAAA;AAAA,IACf,cAAU,MAAE,CAAA,CAAA,CAAA,KAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,GAAA,IAAA,KAAA,IAAA,CAAA,SAAA,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AAAA,IACZ,OAAA,EAAK,qDAAO,IAAiB,CAAA,iBAAA,IAAA,IAAA,CAAA,iBAAA,CAAA,GAAA,IAAA,CAAA,EAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,GAAA,EAAA;AAE9B,IAAAC,UAAA,CAEO,4BAFP,MAEO;AAAA,MADLC,kBAAA,CAA+B,8BAAtB,IAAY,CAAA,YAAA,CAAA,EAAA,CAAA,CAAA;AAAA,KAAA,CAAA;;YAVf,IAAO,CAAA,OAAA,CAAA;AAAA,GAAA,CAAA,CAAA;;;;;;"}