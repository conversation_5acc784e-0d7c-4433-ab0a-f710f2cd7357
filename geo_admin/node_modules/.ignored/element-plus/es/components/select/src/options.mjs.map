{"version": 3, "file": "options.mjs", "sources": ["../../../../../../packages/components/select/src/options.ts"], "sourcesContent": ["import { defineComponent } from 'vue'\nimport { isFunction, isString } from '@element-plus/utils'\nimport type { Component, VNode, VNodeNormalizedChildren } from 'vue'\n\nexport default defineComponent({\n  name: 'ElOptions',\n  emits: ['update-options'],\n  setup(_, { slots, emit }) {\n    let cachedOptions: any[] = []\n\n    function isSameOptions(a: any[], b: any[]) {\n      if (a.length !== b.length) return false\n      for (const [index] of a.entries()) {\n        if (a[index] != b[index]) {\n          return false\n        }\n      }\n      return true\n    }\n\n    return () => {\n      const children = slots.default?.()!\n      const filteredOptions: any[] = []\n\n      function filterOptions(children?: VNodeNormalizedChildren) {\n        if (!Array.isArray(children)) return\n        ;(children as VNode[]).forEach((item) => {\n          const name = ((item?.type || {}) as Component)?.name\n\n          if (name === 'ElOptionGroup') {\n            filterOptions(\n              !isString(item.children) &&\n                !Array.isArray(item.children) &&\n                isFunction(item.children?.default)\n                ? item.children?.default()\n                : item.children\n            )\n          } else if (name === 'ElOption') {\n            filteredOptions.push(item.props?.label)\n          } else if (Array.isArray(item.children)) {\n            filterOptions(item.children)\n          }\n        })\n      }\n\n      if (children.length) {\n        filterOptions(children![0]?.children)\n      }\n\n      if (!isSameOptions(filteredOptions, cachedOptions)) {\n        cachedOptions = filteredOptions\n        emit('update-options', filteredOptions)\n      }\n\n      return children\n    }\n  },\n})\n"], "names": [], "mappings": ";;;;AAEA,gBAAe,eAAe,CAAC;AAC/B,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,KAAK,EAAE,CAAC,gBAAgB,CAAC;AAC3B,EAAE,KAAK,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;AAC5B,IAAI,IAAI,aAAa,GAAG,EAAE,CAAC;AAC3B,IAAI,SAAS,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE;AACjC,MAAM,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM;AAC/B,QAAQ,OAAO,KAAK,CAAC;AACrB,MAAM,KAAK,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;AACzC,QAAQ,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,EAAE;AAClC,UAAU,OAAO,KAAK,CAAC;AACvB,SAAS;AACT,OAAO;AACP,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,IAAI,OAAO,MAAM;AACjB,MAAM,IAAI,EAAE,EAAE,EAAE,CAAC;AACjB,MAAM,MAAM,QAAQ,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC9E,MAAM,MAAM,eAAe,GAAG,EAAE,CAAC;AACjC,MAAM,SAAS,aAAa,CAAC,SAAS,EAAE;AACxC,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC;AACrC,UAAU,OAAO;AACjB,QAAQ,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AACpC,UAAU,IAAI,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;AAC/B,UAAU,MAAM,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,KAAK,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC;AACrG,UAAU,IAAI,IAAI,KAAK,eAAe,EAAE;AACxC,YAAY,aAAa,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,UAAU,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;AAChO,WAAW,MAAM,IAAI,IAAI,KAAK,UAAU,EAAE;AAC1C,YAAY,eAAe,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;AAChF,WAAW,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;AACnD,YAAY,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACzC,WAAW;AACX,SAAS,CAAC,CAAC;AACX,OAAO;AACP,MAAM,IAAI,QAAQ,CAAC,MAAM,EAAE;AAC3B,QAAQ,aAAa,CAAC,CAAC,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC;AACzE,OAAO;AACP,MAAM,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,aAAa,CAAC,EAAE;AAC1D,QAAQ,aAAa,GAAG,eAAe,CAAC;AACxC,QAAQ,IAAI,CAAC,gBAAgB,EAAE,eAAe,CAAC,CAAC;AAChD,OAAO;AACP,MAAM,OAAO,QAAQ,CAAC;AACtB,KAAK,CAAC;AACN,GAAG;AACH,CAAC,CAAC;;;;"}