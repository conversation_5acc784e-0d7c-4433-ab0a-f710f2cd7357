declare const _default: import("vue").DefineComponent<{
    /**
     * @description value of option
     */
    value: {
        required: true;
        type: (ObjectConstructor | NumberConstructor | BooleanConstructor | StringConstructor)[];
    };
    /**
     * @description label of option, same as `value` if omitted
     */
    label: (NumberConstructor | StringConstructor)[];
    created: BooleanConstructor;
    /**
     * @description whether option is disabled
     */
    disabled: BooleanConstructor;
}, {
    ns: {
        namespace: import("vue").ComputedRef<string>;
        b: (blockSuffix?: string) => string;
        e: (element?: string | undefined) => string;
        m: (modifier?: string | undefined) => string;
        be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
        em: (element?: string | undefined, modifier?: string | undefined) => string;
        bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
        bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
        is: {
            (name: string, state: boolean | undefined): string;
            (name: string): string;
        };
        cssVar: (object: Record<string, string>) => Record<string, string>;
        cssVarName: (name: string) => string;
        cssVarBlock: (object: Record<string, string>) => Record<string, string>;
        cssVarBlockName: (name: string) => string;
    };
    id: import("vue").Ref<string>;
    containerKls: import("vue").ComputedRef<(string | {
        selected: boolean;
        hover: boolean;
    })[]>;
    currentLabel: import("vue").ComputedRef<any>;
    itemSelected: import("vue").ComputedRef<boolean>;
    isDisabled: import("vue").ComputedRef<any>;
    select: import("./token").SelectContext | undefined;
    hoverItem: () => void;
    visible: import("vue").Ref<boolean>;
    hover: import("vue").Ref<boolean>;
    selectOptionClick: () => void;
    states: {
        index: number;
        groupDisabled: boolean;
        visible: boolean;
        hitState: boolean;
        hover: boolean;
    };
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    /**
     * @description value of option
     */
    value: {
        required: true;
        type: (ObjectConstructor | NumberConstructor | BooleanConstructor | StringConstructor)[];
    };
    /**
     * @description label of option, same as `value` if omitted
     */
    label: (NumberConstructor | StringConstructor)[];
    created: BooleanConstructor;
    /**
     * @description whether option is disabled
     */
    disabled: BooleanConstructor;
}>>, {
    disabled: boolean;
    created: boolean;
}>;
export default _default;
