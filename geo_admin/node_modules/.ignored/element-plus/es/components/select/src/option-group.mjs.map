{"version": 3, "file": "option-group.mjs", "sources": ["../../../../../../packages/components/select/src/option-group.vue"], "sourcesContent": ["<template>\n  <ul v-show=\"visible\" :class=\"ns.be('group', 'wrap')\">\n    <li :class=\"ns.be('group', 'title')\">{{ label }}</li>\n    <li>\n      <ul :class=\"ns.b('group')\">\n        <slot />\n      </ul>\n    </li>\n  </ul>\n</template>\n\n<script lang=\"ts\">\n// @ts-nocheck\nimport {\n  defineComponent,\n  getCurrentInstance,\n  inject,\n  onMounted,\n  provide,\n  reactive,\n  ref,\n  toRaw,\n  toRefs,\n  watch,\n} from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { selectGroupKey, selectKey } from './token'\n\nexport default defineComponent({\n  name: 'ElOptionGroup',\n  componentName: 'ElOptionGroup',\n\n  props: {\n    /**\n     * @description name of the group\n     */\n    label: String,\n    /**\n     * @description whether to disable all options in this group\n     */\n    disabled: Boolean,\n  },\n  setup(props) {\n    const ns = useNamespace('select')\n    const visible = ref(true)\n    const instance = getCurrentInstance()\n    const children = ref([])\n\n    provide(\n      selectGroupKey,\n      reactive({\n        ...toRefs(props),\n      })\n    )\n\n    const select = inject(selectKey)\n\n    onMounted(() => {\n      children.value = flattedChildren(instance.subTree)\n    })\n\n    // get all instances of options\n    const flattedChildren = (node) => {\n      const children = []\n      if (Array.isArray(node.children)) {\n        node.children.forEach((child) => {\n          if (\n            child.type &&\n            child.type.name === 'ElOption' &&\n            child.component &&\n            child.component.proxy\n          ) {\n            children.push(child.component.proxy)\n          } else if (child.children?.length) {\n            children.push(...flattedChildren(child))\n          }\n        })\n      }\n      return children\n    }\n\n    const { groupQueryChange } = toRaw(select)\n    watch(\n      groupQueryChange,\n      () => {\n        visible.value = children.value.some((option) => option.visible === true)\n      },\n      { flush: 'post' }\n    )\n\n    return {\n      visible,\n      ns,\n    }\n  },\n})\n</script>\n"], "names": ["_withDirectives", "_openBlock", "_createElementBlock", "_normalizeClass", "_createElementVNode", "_toDisplayString", "_renderSlot", "_vShow"], "mappings": ";;;;;;AA4BA,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,eAAA;AAAA,EACN,aAAe,EAAA,eAAA;AAAA,EAEf,KAAO,EAAA;AAAA,IAIL,KAAO,EAAA,MAAA;AAAA,IAIP,QAAU,EAAA,OAAA;AAAA,GACZ;AAAA,EACA,MAAM,KAAO,EAAA;AACX,IAAM,MAAA,EAAA,GAAK,aAAa,QAAQ,CAAA,CAAA;AAChC,IAAM,MAAA,OAAA,GAAU,IAAI,IAAI,CAAA,CAAA;AACxB,IAAA,MAAM,WAAW,kBAAmB,EAAA,CAAA;AACpC,IAAM,MAAA,QAAA,GAAW,GAAI,CAAA,EAAE,CAAA,CAAA;AAEvB,IAAA,OAAA,CACE,gBACA,QAAS,CAAA;AAAA,MACP,GAAG,OAAO,KAAK,CAAA;AAAA,KAChB,CACH,CAAA,CAAA;AAEA,IAAM,MAAA,MAAA,GAAS,OAAO,SAAS,CAAA,CAAA;AAE/B,IAAA,SAAA,CAAU,MAAM;AACd,MAAS,QAAA,CAAA,KAAA,GAAQ,eAAgB,CAAA,QAAA,CAAS,OAAO,CAAA,CAAA;AAAA,KAClD,CAAA,CAAA;AAGD,IAAM,MAAA,eAAA,GAAkB,CAAC,IAAS,KAAA;AAChC,MAAA,MAAM,YAAW,EAAC,CAAA;AAClB,MAAA,IAAI,KAAM,CAAA,OAAA,CAAQ,IAAK,CAAA,QAAQ,CAAG,EAAA;AAChC,QAAK,IAAA,CAAA,QAAA,CAAS,OAAQ,CAAA,CAAC,KAAU,KAAA;AAC/B,UACE,IAAA,EAAA,CAAA;AAKA,UAAS,IAAA,KAAA,CAAA,IAAA,IAAW,KAAA,CAAA,IAAA,CAAA,IAAU,KAAK,UAAA,IAAA,KAAA,CAAA,SAAA,IAAA,KAAA,CAAA,SAAA,CAAA,KAAA,EAAA;AAAA,YACrC,SAAW,CAAM,IAAA,CAAA,KAAA,CAAA,SAAkB,CAAA,KAAA,CAAA,CAAA;AACjC,WAAA,MAAA,IAAA,CAAS,EAAK,GAAA,KAAmB,CAAA,QAAA,KAAA,IAAA,GAAM,KAAA,CAAA,GAAA,EAAA,CAAA,MAAA,EAAA;AAAA,YACzC,SAAA,CAAA,IAAA,CAAA,GAAA,eAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA,WACD;AAAA,SACH,CAAA,CAAA;AACA,OAAO;AAAA,MACT,OAAA,SAAA,CAAA;AAEA,KAAM,CAAA;AACN,IAAA,MACE,kBACA,EAAM,GAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AACJ,IAAQ,KAAA,CAAA,wBAAuB;AAAwC,MAEzE,OAAS,CAAA,KAAA,GAAA,QACX,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA,MAAA,KAAA,MAAA,CAAA,OAAA,KAAA,IAAA,CAAA,CAAA;AAEA,KAAO,EAAA,EAAA,KAAA,EAAA,MAAA,EAAA,CAAA,CAAA;AAAA,IACL,OAAA;AAAA,MACA,OAAA;AAAA,MACF,EAAA;AAAA,KACF,CAAA;AACF,GAAC;;yCAvFM,EAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AAAA,EAPiB,OAAAA,cAAO,EAAAC,SAAK,EAAA,EAAAC,kBAAA,CAAA,IAAA,EAAA;AAAA,IAAA,KAAA,EAAAC,cAAA,CAAA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,OAAA,EAAA,MAAA,CAAA,CAAA;KACqB;AAAA,IAAhDC,kBAAO,CAAA,IAAA,EAAA;AAAK,MAAA,KAAA,EAAAD,cAA4B,CAAA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,OAAA,EAAA,OAAA,CAAA,CAAA;AAAA,KAKxC,EAAAE,eAAA,CAAA,IAAA,CAAA,KAAA,CAAA,EAAA,CAAA,CAAA;AAAA,IAAAD,kBADE,CAAA,IAAA,EAAA,IAAA,EAAA;AAAA,MAFAA,kBAAO,CAAA,IAAA,EAAA;AAAI,QAAA,KAAA,EAAAD,cAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA;SACN;AAAA,QAAAG,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,CAAA;;;;AAJK,IAAA,CAAAC,KAAA,EAAA,IAAA,CAAA,OAAA,CAAA;;;;;;;"}