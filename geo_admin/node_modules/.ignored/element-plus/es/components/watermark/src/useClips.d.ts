import type { WatermarkProps } from './watermark';
export declare const FontGap = 3;
/**
 * Get the clips of text content.
 * This is a lazy hook function since SSR no need this
 */
export default function useClips(): (content: NonNullable<WatermarkProps['content']> | HTMLImageElement, rotate: number, ratio: number, width: number, height: number, font: Required<NonNullable<WatermarkProps['font']>>, gapX: number, gapY: number) => [dataURL: string, finalWidth: number, finalHeight: number];
