{"version": 3, "file": "watermark2.mjs", "sources": ["../../../../../../packages/components/watermark/src/watermark.vue"], "sourcesContent": ["<template>\n  <div ref=\"containerRef\" :style=\"[style]\">\n    <slot />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  onBeforeUnmount,\n  onMounted,\n  ref,\n  shallowRef,\n  watch,\n} from 'vue'\nimport { useMutationObserver } from '@vueuse/core'\nimport { watermarkProps } from './watermark'\nimport { getPixelRatio, getStyleStr, reRendering } from './utils'\nimport useClips, { FontGap } from './useClips'\nimport type { WatermarkProps } from './watermark'\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElWatermark',\n})\n\nconst style: CSSProperties = {\n  position: 'relative',\n}\n\nconst props = defineProps(watermarkProps)\nconst color = computed(() => props.font?.color ?? 'rgba(0,0,0,.15)')\nconst fontSize = computed(() => props.font?.fontSize ?? 16)\nconst fontWeight = computed(() => props.font?.fontWeight ?? 'normal')\nconst fontStyle = computed(() => props.font?.fontStyle ?? 'normal')\nconst fontFamily = computed(() => props.font?.fontFamily ?? 'sans-serif')\nconst textAlign = computed(() => props.font?.textAlign ?? 'center')\nconst textBaseline = computed(() => props.font?.textBaseline ?? 'top')\n\nconst gapX = computed(() => props.gap[0])\nconst gapY = computed(() => props.gap[1])\nconst gapXCenter = computed(() => gapX.value / 2)\nconst gapYCenter = computed(() => gapY.value / 2)\nconst offsetLeft = computed(() => props.offset?.[0] ?? gapXCenter.value)\nconst offsetTop = computed(() => props.offset?.[1] ?? gapYCenter.value)\n\nconst getMarkStyle = () => {\n  const markStyle: CSSProperties = {\n    zIndex: props.zIndex,\n    position: 'absolute',\n    left: 0,\n    top: 0,\n    width: '100%',\n    height: '100%',\n    pointerEvents: 'none',\n    backgroundRepeat: 'repeat',\n  }\n\n  /** Calculate the style of the offset */\n  let positionLeft = offsetLeft.value - gapXCenter.value\n  let positionTop = offsetTop.value - gapYCenter.value\n  if (positionLeft > 0) {\n    markStyle.left = `${positionLeft}px`\n    markStyle.width = `calc(100% - ${positionLeft}px)`\n    positionLeft = 0\n  }\n  if (positionTop > 0) {\n    markStyle.top = `${positionTop}px`\n    markStyle.height = `calc(100% - ${positionTop}px)`\n    positionTop = 0\n  }\n  markStyle.backgroundPosition = `${positionLeft}px ${positionTop}px`\n\n  return markStyle\n}\n\nconst containerRef = shallowRef<HTMLDivElement | null>(null)\nconst watermarkRef = shallowRef<HTMLDivElement>()\nconst stopObservation = ref(false)\n\nconst destroyWatermark = () => {\n  if (watermarkRef.value) {\n    watermarkRef.value.remove()\n    watermarkRef.value = undefined\n  }\n}\nconst appendWatermark = (base64Url: string, markWidth: number) => {\n  if (containerRef.value && watermarkRef.value) {\n    stopObservation.value = true\n    watermarkRef.value.setAttribute(\n      'style',\n      getStyleStr({\n        ...getMarkStyle(),\n        backgroundImage: `url('${base64Url}')`,\n        backgroundSize: `${Math.floor(markWidth)}px`,\n      })\n    )\n    containerRef.value?.append(watermarkRef.value)\n    // Delayed execution\n    setTimeout(() => {\n      stopObservation.value = false\n    })\n  }\n}\n\n/**\n * Get the width and height of the watermark. The default values are as follows\n * Image: [120, 64]; Content: It's calculated by content;\n */\nconst getMarkSize = (ctx: CanvasRenderingContext2D) => {\n  let defaultWidth = 120\n  let defaultHeight = 64\n  const image = props.image\n  const content = props.content\n  const width = props.width\n  const height = props.height\n  if (!image && ctx.measureText) {\n    ctx.font = `${Number(fontSize.value)}px ${fontFamily.value}`\n    const contents = Array.isArray(content) ? content : [content]\n    const sizes = contents.map((item) => {\n      const metrics = ctx.measureText(item!)\n\n      return [\n        metrics.width,\n        // Using `actualBoundingBoxAscent` to be compatible with lower version browsers (eg: Firefox < 116)\n        metrics.fontBoundingBoxAscent !== undefined\n          ? metrics.fontBoundingBoxAscent + metrics.fontBoundingBoxDescent\n          : metrics.actualBoundingBoxAscent + metrics.actualBoundingBoxDescent,\n      ]\n    })\n    defaultWidth = Math.ceil(Math.max(...sizes.map((size) => size[0])))\n    defaultHeight =\n      Math.ceil(Math.max(...sizes.map((size) => size[1]))) * contents.length +\n      (contents.length - 1) * FontGap\n  }\n  return [width ?? defaultWidth, height ?? defaultHeight] as const\n}\n\nconst getClips = useClips()\n\nconst renderWatermark = () => {\n  const canvas = document.createElement('canvas')\n  const ctx = canvas.getContext('2d')\n  const image = props.image\n  const content = props.content\n  const rotate = props.rotate\n\n  if (ctx) {\n    if (!watermarkRef.value) {\n      watermarkRef.value = document.createElement('div')\n    }\n\n    const ratio = getPixelRatio()\n    const [markWidth, markHeight] = getMarkSize(ctx)\n\n    const drawCanvas = (\n      drawContent?: NonNullable<WatermarkProps['content']> | HTMLImageElement\n    ) => {\n      const [textClips, clipWidth] = getClips(\n        drawContent || '',\n        rotate,\n        ratio,\n        markWidth,\n        markHeight,\n        {\n          color: color.value,\n          fontSize: fontSize.value,\n          fontStyle: fontStyle.value,\n          fontWeight: fontWeight.value,\n          fontFamily: fontFamily.value,\n          textAlign: textAlign.value,\n          textBaseline: textBaseline.value,\n        },\n        gapX.value,\n        gapY.value\n      )\n\n      appendWatermark(textClips, clipWidth)\n    }\n\n    if (image) {\n      const img = new Image()\n      img.onload = () => {\n        drawCanvas(img)\n      }\n      img.onerror = () => {\n        drawCanvas(content)\n      }\n      img.crossOrigin = 'anonymous'\n      img.referrerPolicy = 'no-referrer'\n      img.src = image\n    } else {\n      drawCanvas(content)\n    }\n  }\n}\n\nonMounted(() => {\n  renderWatermark()\n})\n\nwatch(\n  () => props,\n  () => {\n    renderWatermark()\n  },\n  {\n    deep: true,\n    flush: 'post',\n  }\n)\n\nonBeforeUnmount(() => {\n  destroyWatermark()\n})\n\nconst onMutate = (mutations: MutationRecord[]) => {\n  if (stopObservation.value) {\n    return\n  }\n  mutations.forEach((mutation) => {\n    if (reRendering(mutation, watermarkRef.value)) {\n      destroyWatermark()\n      renderWatermark()\n    }\n  })\n}\n\nuseMutationObserver(containerRef, onMutate, {\n  attributes: true,\n  subtree: true,\n  childList: true,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;mCAsBc,CAAA;AAAA,EACZ,IAAM,EAAA,aAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAEA,IAAA,MAAM,KAAuB,GAAA;AAAA,MAC3B,QAAU,EAAA,UAAA;AAAA,KACZ,CAAA;AAGA,IAAA,MAAM,QAAQ,QAAS,CAAA,MAAM;AAC7B,MAAA,IAAM;AACN,MAAA,mBAA4B,KAAA,CAAA,IAAA,KAAY,IAAA,GAAA,iBAAoB,KAAQ,IAAA,GAAA,EAAA,GAAA,iBAAA,CAAA;AACpE,KAAA,CAAA,CAAA;AACA,IAAA,MAAM,mBAAsB,CAAA,MAAA;AAC5B,MAAA,IAAM;AACN,MAAA,mBAAqB,KAAS,CAAA,IAAA,KAAM,IAAM,GAAA,KAAM,oBAAqB,IAAA,GAAA,EAAA,GAAA,EAAA,CAAA;AAErE,KAAA,CAAA,CAAA;AACA,IAAA,MAAM,UAAO,GAAA,QAAe,CAAA,MAAM;AAClC,MAAA,IAAM,EAAa,EAAA,EAAA,CAAA;AACnB,MAAA,OAAmB,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,IAAe,KAAA,YAAc,CAAA,GAAA,EAAA,CAAA,UAAA,KAAA,IAAA,GAAA,EAAA,GAAA,QAAA,CAAA;AAChD,KAAA,CAAA,CAAA;AACA,IAAA,MAAM,YAAY,QAAS,CAAA,MAAM;AAEjC,MAAA,IAAM;AACJ,MAAA,OAAiC,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,IAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,SAAA,KAAA,IAAA,GAAA,EAAA,GAAA,QAAA,CAAA;AAAA,KAAA,CAAA,CAAA;AACjB,IAAA,MACJ,UAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MAAA,IACJ,EAAA,EAAA,EAAA,CAAA;AAAA,MAAA,OACD,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,IAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAA,KAAA,IAAA,GAAA,EAAA,GAAA,YAAA,CAAA;AAAA,KAAA,CAAA,CAAA;AACE,IAAA,MACC,SAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MAAA,IACO,EAAA,EAAA,EAAA,CAAA;AAAA,MAAA,OACG,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,IAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,SAAA,KAAA,IAAA,GAAA,EAAA,GAAA,QAAA,CAAA;AAAA,KACpB,CAAA,CAAA;AAGA,IAAI,MAAA,YAAA,GAAe,QAAW,CAAA,MAAA;AAC9B,MAAI,IAAA,EAAA,EAAA,EAAA,CAAA;AACJ,MAAA,mBAAmB,KAAG,CAAA,IAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,KAAA,IAAA,GAAA,EAAA,GAAA,KAAA,CAAA;AACpB,KAAA,CAAA,CAAA;AACA,IAAA,MAAA,IAAA,GAAA,SAAkB,MAAe,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACjC,IAAe,MAAA,IAAA,GAAA,QAAA,CAAA,MAAA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,IACjB,MAAA,UAAA,GAAA,QAAA,CAAA,MAAA,IAAA,CAAA,KAAA,GAAA,CAAA,CAAA,CAAA;AACA,IAAA,MAAI,qBAAiB,CAAA,MAAA,IAAA,CAAA,KAAA,GAAA,CAAA,CAAA,CAAA;AACnB,IAAA,MAAA,qBAAmB,CAAA,MAAA;AACnB,MAAA,IAAA,EAAA,EAAA,EAAA,CAAA;AACA,MAAc,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,MAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,KAAA,IAAA,GAAA,EAAA,GAAA,UAAA,CAAA,KAAA,CAAA;AAAA,KAChB,CAAA,CAAA;AACA,IAAU,MAAA,SAAA,GAAA,QAAA,CAAA,MAAqB;AAE/B,MAAO,IAAA,EAAA,EAAA,EAAA,CAAA;AAAA,MACT,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,MAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,KAAA,IAAA,GAAA,EAAA,GAAA,UAAA,CAAA,KAAA,CAAA;AAEA,KAAM,CAAA,CAAA;AACN,IAAA,MAAM,eAAe,MAA2B;AAChD,MAAM,MAAA,SAAA,GAAA;AAEN,QAAA;AACE,QAAA,oBAAwB;AACtB,QAAA,IAAA,EAAA,CAAA;AACA,QAAA,GAAA,EAAA,CAAA;AAAqB,QACvB,KAAA,EAAA,MAAA;AAAA,QACF,MAAA,EAAA,MAAA;AACA,QAAM,aAAA,EAAA,MAAmB;AACvB,QAAI,gBAAsB,EAAA,QAAA;AACxB,OAAA,CAAA;AACA,MAAa,IAAA,YAAA,GAAA,UACX,CAAA,KAAA,GAAA,UACY,CAAA,KAAA,CAAA;AAAA,MAAA,IACV,WAAgB,GAAA,SAAA,CAAA,KAAA,GAAA,UAAA,CAAA,KAAA,CAAA;AAAA,MAAA,IAChB,kBAAyB;AAAA,QAAA,SACT,CAAA,IAAA,GAAA,CAAA,EAAA,YAAc,CAAS,EAAA,CAAA,CAAA;AAAA,QACzC,SACF,CAAA,KAAA,GAAA,CAAA,YAAA,EAAA,YAAA,CAAA,GAAA,CAAA,CAAA;AACA,QAAa,YAAA,GAAA,CAAA,CAAA;AAEb,OAAA;AACE,MAAA,IAAA,WAAA,GAAA,CAAA,EAAwB;AAAA,QAC1B,SAAC,CAAA,GAAA,GAAA,CAAA,EAAA,WAAA,CAAA,EAAA,CAAA,CAAA;AAAA,QACH,SAAA,CAAA,MAAA,GAAA,CAAA,YAAA,EAAA,WAAA,CAAA,GAAA,CAAA,CAAA;AAAA,QACF,WAAA,GAAA,CAAA,CAAA;AAMA,OAAM;AACJ,MAAA,SAAmB,CAAA,kBAAA,GAAA,CAAA,EAAA,YAAA,CAAA,GAAA,EAAA,WAAA,CAAA,EAAA,CAAA,CAAA;AACnB,MAAA,OAAoB,SAAA,CAAA;AACpB,KAAA,CAAA;AACA,IAAA,MAAA,YAAgB,GAAM,UAAA,CAAA,IAAA,CAAA,CAAA;AACtB,IAAA,MAAA,YAAoB,GAAA,UAAA,EAAA,CAAA;AACpB,IAAA,MAAA,eAAqB,GAAA,GAAA,CAAA,KAAA,CAAA,CAAA;AACrB,IAAI,MAAA,gBAA2B,GAAA,MAAA;AAC7B,MAAA,IAAA,YAAc,CAAA,KAAA,EAAgB;AAC9B,QAAA,kBAAuB,CAAA,MAAA,EAAA,CAAA;AACvB,QAAA,YAAc,CAAA,KAAA,GAAA,KAAa,CAAA,CAAC;AAC1B,OAAM;AAEN,KAAO,CAAA;AAAA,IAAA,MAAA,eACG,GAAA,CAAA,SAAA,EAAA,SAAA,KAAA;AAAA,MAER,IAAA,EAAA,CAAA;AAE8C,MAChD,IAAA,YAAA,CAAA,KAAA,IAAA,YAAA,CAAA,KAAA,EAAA;AAAA,QACF,eAAC,CAAA,KAAA,GAAA,IAAA,CAAA;AACD,QAAA,YAAA,CAAA,KAAoB,CAAA,YAAU,CAAA,OAAO,EAAA,WAAoB,CAAA;AACzD,UAAA,GAAA;AAE0B,UAC5B,eAAA,EAAA,CAAA,KAAA,EAAA,SAAA,CAAA,EAAA,CAAA;AACA,UAAA,cAA+B,EAAA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,SAAuB,CAAA,CAAA,EAAA,CAAA;AAAA,SACxD,CAAA,CAAA,CAAA;AAEA,QAAA,CAAA,iBAA0B,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAA,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA;AAE1B,QAAA;AACE,UAAM,eAAkB,CAAA,KAAA,GAAA,KAAA,CAAA;AACxB,SAAM,CAAA,CAAA;AACN,OAAA;AACA,KAAA,CAAA;AACA,IAAA,MAAA,WAAe,GAAM,CAAA,GAAA,KAAA;AAErB,MAAA,IAAI,YAAK,GAAA,GAAA,CAAA;AACP,MAAI,IAAA,gBAAc,EAAO,CAAA;AACvB,MAAa,MAAA,KAAA,GAAA,KAAA,CAAA,KAAQ,CAAS;AAAmB,MACnD,MAAA,OAAA,GAAA,KAAA,CAAA,OAAA,CAAA;AAEA,MAAA,MAAA,aAA4B,CAAA,KAAA,CAAA;AAC5B,MAAA,MAAA,MAAO,GAAA,KAAW,CAAc,MAAA,CAAA;AAEhC,MAAM,IAAA,CAAA,KAAA,IAAA,GAAA,CAAA,WAED,EAAA;AACH,QAAM,GAAA,CAAA,IAAA,YAAY,CAAa,QAAA,CAAA,KAAA,CAAA,CAAA,GAAA,EAC7B,gBACA,CAAA,CAAA,CAAA;AAIA,QAAA,cACe,GAAA,KAAA,CAAA,OAAA,CAAA,OAAA,CAAA,GAAA,OAAA,GAAA,CAAA,OAAA,CAAA,CAAA;AAAA,QAAA,cACH,QAAS,CAAA,GAAA,CAAA,CAAA,IAAA,KAAA;AAAA,UAAA,aACR,GAAU,GAAA,CAAA,WAAA,CAAA,IAAA,CAAA,CAAA;AAAA,UAAA;AACE,YACvB,aAAuB;AAAA,YACvB,6BAAqB,KAAA,KAAA,CAAA,GAAA,OAAA,CAAA,qBAAA,GAAA,OAAA,CAAA,sBAAA,GAAA,OAAA,CAAA,uBAAA,GAAA,OAAA,CAAA,wBAAA;AAAA,WAAA,CACrB;AAA2B,SAAA,CAC7B,CACA;AAIF,QAAA,YAAA,GAAA,cAA2B,CAAS,GAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,QACtC,aAAA,GAAA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,QAAA,CAAA,MAAA,GAAA,CAAA,QAAA,CAAA,MAAA,GAAA,CAAA,IAAA,OAAA,CAAA;AAEA,OAAA;AACE,MAAM,OAAA,CAAA,KAAA,QAAgB,GAAA,KAAA,GAAA,YAAA,EAAA,MAAA,IAAA,IAAA,GAAA,MAAA,GAAA,aAAA,CAAA,CAAA;AACtB,KAAA,CAAA;AACE,IAAA,MAAA,QAAA,GAAA,QAAc,EAAA,CAAA;AAAA,IAChB,MAAA,eAAA,GAAA,MAAA;AACA,MAAA,MAAA,iBAAoB,CAAA,aAAA,CAAA,QAAA,CAAA,CAAA;AAClB,MAAA,MAAA,GAAA,GAAA,MAAkB,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA;AAAA,MACpB,MAAA,KAAA,GAAA,KAAA,CAAA,KAAA,CAAA;AACA,MAAA,MAAA,OAAkB,GAAA,KAAA,CAAA,OAAA,CAAA;AAClB,MAAA,MAAA,MAAqB,GAAA,KAAA,CAAA,MAAA,CAAA;AACrB,MAAA,IAAA,GAAA,EAAU;AAAA,QACZ,IAAO,CAAA,YAAA,CAAA,KAAA,EAAA;AACL,UAAA,YAAkB,CAAA,KAAA,GAAA,QAAA,CAAA,aAAA,CAAA,KAAA,CAAA,CAAA;AAAA,SACpB;AAAA,QACF,MAAA,KAAA,GAAA,aAAA,EAAA,CAAA;AAAA,QACF,MAAA,CAAA,SAAA,EAAA,UAAA,CAAA,GAAA,WAAA,CAAA,GAAA,CAAA,CAAA;AAEA,QAAA,MAAU,UAAM,GAAA,CAAA,WAAA,KAAA;AACd,UAAgB,MAAA,CAAA,SAAA,EAAA,SAAA,CAAA,GAAA,QAAA,CAAA,WAAA,IAAA,EAAA,EAAA,MAAA,EAAA,KAAA,EAAA,SAAA,EAAA,UAAA,EAAA;AAAA,YACjB,KAAA,EAAA,KAAA,CAAA,KAAA;AAED,YACE,kBACM,CAAA,KAAA;AACJ,YAAgB,SAAA,EAAA,SAAA,CAAA,KAAA;AAAA,YAElB,UAAA,EAAA,UAAA,CAAA,KAAA;AAAA,YACQ,UAAA,EAAA,UAAA,CAAA,KAAA;AAAA,YACC,SAAA,EAAA,SAAA,CAAA,KAAA;AAAA,YAEX,YAAA,EAAA,YAAA,CAAA,KAAA;AAEA,WAAA,EAAA,IAAA,CAAA,KAAsB,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AACpB,UAAiB,eAAA,CAAA,SAAA,EAAA,SAAA,CAAA,CAAA;AAAA,SAClB,CAAA;AAED,QAAM,IAAA,KAAA,EAAA;AACJ,UAAI,gBAAgB,KAAO,EAAA,CAAA;AACzB,UAAA,GAAA,CAAA,MAAA,GAAA,MAAA;AAAA,YACF,UAAA,CAAA,GAAA,CAAA,CAAA;AACA,WAAU,CAAA;AACR,UAAA,GAAgB,CAAA,OAAA,GAAA,MAAA;AACd,YAAiB,UAAA,CAAA,OAAA,CAAA,CAAA;AACjB,WAAgB,CAAA;AAAA,UAClB,GAAA,CAAA,WAAA,GAAA,WAAA,CAAA;AAAA,UACD,GAAA,CAAA,cAAA,GAAA,aAAA,CAAA;AAAA,UACH,GAAA,CAAA,GAAA,GAAA,KAAA,CAAA;AAEA,SAAA,MAAA;AAA4C,UAC9B,UAAA,CAAA,OAAA,CAAA,CAAA;AAAA,SACH;AAAA,OACE;AAAA,KACZ,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}