{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/watermark/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Watermark from './src/watermark.vue'\n\nexport const ElWatermark = withInstall(Watermark)\nexport default ElWatermark\n\nexport * from './src/watermark'\n"], "names": [], "mappings": ";;;;;AAEY,MAAC,WAAW,GAAG,WAAW,CAAC,SAAS;;;;"}