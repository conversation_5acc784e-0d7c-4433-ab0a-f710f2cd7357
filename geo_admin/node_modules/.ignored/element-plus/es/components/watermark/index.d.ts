export declare const ElWatermark: import("element-plus/es/utils").SFCWithInstall<import("vue").DefineComponent<{
    readonly zIndex: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 9, boolean>;
    readonly rotate: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, -22, boolean>;
    readonly width: NumberConstructor;
    readonly height: NumberConstructor;
    readonly image: StringConstructor;
    readonly content: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (string | string[]) & {}) | (() => string | string[]) | ((new (...args: any[]) => (string | string[]) & {}) | (() => string | string[]))[], unknown, unknown, "Element Plus", boolean>;
    readonly font: {
        readonly type: import("vue").PropType<import("./src/watermark").WatermarkFontType>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly gap: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => [number, number]) | (() => [number, number]) | ((new (...args: any[]) => [number, number]) | (() => [number, number]))[], unknown, unknown, () => number[], boolean>;
    readonly offset: {
        readonly type: import("vue").PropType<[number, number]>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
}, {
    style: import("vue").CSSProperties;
    props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
        readonly zIndex: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 9, boolean>;
        readonly rotate: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, -22, boolean>;
        readonly width: NumberConstructor;
        readonly height: NumberConstructor;
        readonly image: StringConstructor;
        readonly content: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (string | string[]) & {}) | (() => string | string[]) | ((new (...args: any[]) => (string | string[]) & {}) | (() => string | string[]))[], unknown, unknown, "Element Plus", boolean>;
        readonly font: {
            readonly type: import("vue").PropType<import("./src/watermark").WatermarkFontType>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly gap: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => [number, number]) | (() => [number, number]) | ((new (...args: any[]) => [number, number]) | (() => [number, number]))[], unknown, unknown, () => number[], boolean>;
        readonly offset: {
            readonly type: import("vue").PropType<[number, number]>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
    }>> & {
        [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
    }>>;
    color: import("vue").ComputedRef<string>;
    fontSize: import("vue").ComputedRef<string | number>;
    fontWeight: import("vue").ComputedRef<number | "light" | "normal" | "weight">;
    fontStyle: import("vue").ComputedRef<"none" | "normal" | "italic" | "oblique">;
    fontFamily: import("vue").ComputedRef<string>;
    textAlign: import("vue").ComputedRef<"center" | "right" | "left" | "end" | "start">;
    textBaseline: import("vue").ComputedRef<"top" | "bottom" | "middle" | "alphabetic" | "hanging" | "ideographic">;
    gapX: import("vue").ComputedRef<number>;
    gapY: import("vue").ComputedRef<number>;
    gapXCenter: import("vue").ComputedRef<number>;
    gapYCenter: import("vue").ComputedRef<number>;
    offsetLeft: import("vue").ComputedRef<number>;
    offsetTop: import("vue").ComputedRef<number>;
    getMarkStyle: () => import("vue").CSSProperties;
    containerRef: import("vue").ShallowRef<HTMLDivElement | null>;
    watermarkRef: import("vue").ShallowRef<HTMLDivElement | undefined>;
    stopObservation: import("vue").Ref<boolean>;
    destroyWatermark: () => void;
    appendWatermark: (base64Url: string, markWidth: number) => void;
    getMarkSize: (ctx: CanvasRenderingContext2D) => readonly [number, number];
    getClips: (content: HTMLImageElement | import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => (string | string[]) & {}) | (() => string | string[]) | ((new (...args: any[]) => (string | string[]) & {}) | (() => string | string[]))[], unknown, unknown>, rotate: number, ratio: number, width: number, height: number, font: Required<import("./src/watermark").WatermarkFontType>, gapX: number, gapY: number) => [dataURL: string, finalWidth: number, finalHeight: number];
    renderWatermark: () => void;
    onMutate: (mutations: MutationRecord[]) => void;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    readonly zIndex: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 9, boolean>;
    readonly rotate: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, -22, boolean>;
    readonly width: NumberConstructor;
    readonly height: NumberConstructor;
    readonly image: StringConstructor;
    readonly content: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (string | string[]) & {}) | (() => string | string[]) | ((new (...args: any[]) => (string | string[]) & {}) | (() => string | string[]))[], unknown, unknown, "Element Plus", boolean>;
    readonly font: {
        readonly type: import("vue").PropType<import("./src/watermark").WatermarkFontType>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly gap: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => [number, number]) | (() => [number, number]) | ((new (...args: any[]) => [number, number]) | (() => [number, number]))[], unknown, unknown, () => number[], boolean>;
    readonly offset: {
        readonly type: import("vue").PropType<[number, number]>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
}>>, {
    readonly zIndex: number;
    readonly content: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => (string | string[]) & {}) | (() => string | string[]) | ((new (...args: any[]) => (string | string[]) & {}) | (() => string | string[]))[], unknown, unknown>;
    readonly rotate: number;
    readonly gap: [number, number];
}>> & Record<string, any>;
export default ElWatermark;
export * from './src/watermark';
