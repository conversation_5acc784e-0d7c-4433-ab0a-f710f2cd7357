{"version": 3, "file": "picker.mjs", "sources": ["../../../../../../../packages/components/time-picker/src/common/picker.vue"], "sourcesContent": ["<template>\n  <el-tooltip\n    ref=\"refPopper\"\n    :visible=\"pickerVisible\"\n    effect=\"light\"\n    pure\n    trigger=\"click\"\n    v-bind=\"$attrs\"\n    role=\"dialog\"\n    teleported\n    :transition=\"`${nsDate.namespace.value}-zoom-in-top`\"\n    :popper-class=\"[`${nsDate.namespace.value}-picker__popper`, popperClass]\"\n    :popper-options=\"elPopperOptions\"\n    :fallback-placements=\"['bottom', 'top', 'right', 'left']\"\n    :gpu-acceleration=\"false\"\n    :stop-popper-mouse-event=\"false\"\n    :hide-after=\"0\"\n    persistent\n    @before-show=\"onBeforeShow\"\n    @show=\"onShow\"\n    @hide=\"onHide\"\n  >\n    <template #default>\n      <el-input\n        v-if=\"!isRangeInput\"\n        :id=\"(id as string | undefined)\"\n        ref=\"inputRef\"\n        container-role=\"combobox\"\n        :model-value=\"(displayValue as string)\"\n        :name=\"name\"\n        :size=\"pickerSize\"\n        :disabled=\"pickerDisabled\"\n        :placeholder=\"placeholder\"\n        :class=\"[nsDate.b('editor'), nsDate.bm('editor', type), $attrs.class]\"\n        :style=\"$attrs.style\"\n        :readonly=\"!editable || readonly || isDatesPicker || type === 'week'\"\n        :label=\"label\"\n        :tabindex=\"tabindex\"\n        :validate-event=\"false\"\n        @input=\"onUserInput\"\n        @focus=\"handleFocusInput\"\n        @blur=\"handleBlurInput\"\n        @keydown=\"\n          //\n          handleKeydownInput as any\n        \"\n        @change=\"handleChange\"\n        @mousedown=\"onMouseDownInput\"\n        @mouseenter=\"onMouseEnter\"\n        @mouseleave=\"onMouseLeave\"\n        @touchstart=\"onTouchStartInput\"\n        @click.stop\n      >\n        <template #prefix>\n          <el-icon\n            v-if=\"triggerIcon\"\n            :class=\"nsInput.e('icon')\"\n            @mousedown.prevent=\"onMouseDownInput\"\n            @touchstart=\"onTouchStartInput\"\n          >\n            <component :is=\"triggerIcon\" />\n          </el-icon>\n        </template>\n        <template #suffix>\n          <el-icon\n            v-if=\"showClose && clearIcon\"\n            :class=\"`${nsInput.e('icon')} clear-icon`\"\n            @click.stop=\"onClearIconClick\"\n          >\n            <component :is=\"clearIcon\" />\n          </el-icon>\n        </template>\n      </el-input>\n      <div\n        v-else\n        ref=\"inputRef\"\n        :class=\"rangeInputKls\"\n        :style=\"($attrs.style as any)\"\n        @click=\"handleFocusInput\"\n        @mouseenter=\"onMouseEnter\"\n        @mouseleave=\"onMouseLeave\"\n        @touchstart=\"onTouchStartInput\"\n        @keydown=\"handleKeydownInput\"\n      >\n        <el-icon\n          v-if=\"triggerIcon\"\n          :class=\"[nsInput.e('icon'), nsRange.e('icon')]\"\n          @mousedown.prevent=\"onMouseDownInput\"\n          @touchstart=\"onTouchStartInput\"\n        >\n          <component :is=\"triggerIcon\" />\n        </el-icon>\n        <input\n          :id=\"id && id[0]\"\n          autocomplete=\"off\"\n          :name=\"name && name[0]\"\n          :placeholder=\"startPlaceholder\"\n          :value=\"displayValue && displayValue[0]\"\n          :disabled=\"pickerDisabled\"\n          :readonly=\"!editable || readonly\"\n          :class=\"nsRange.b('input')\"\n          @mousedown=\"onMouseDownInput\"\n          @input=\"handleStartInput\"\n          @change=\"handleStartChange\"\n          @focus=\"handleFocusInput\"\n          @blur=\"handleBlurInput\"\n        />\n        <slot name=\"range-separator\">\n          <span :class=\"nsRange.b('separator')\">{{ rangeSeparator }}</span>\n        </slot>\n        <input\n          :id=\"id && id[1]\"\n          autocomplete=\"off\"\n          :name=\"name && name[1]\"\n          :placeholder=\"endPlaceholder\"\n          :value=\"displayValue && displayValue[1]\"\n          :disabled=\"pickerDisabled\"\n          :readonly=\"!editable || readonly\"\n          :class=\"nsRange.b('input')\"\n          @mousedown=\"onMouseDownInput\"\n          @focus=\"handleFocusInput\"\n          @blur=\"handleBlurInput\"\n          @input=\"handleEndInput\"\n          @change=\"handleEndChange\"\n        />\n        <el-icon\n          v-if=\"clearIcon\"\n          :class=\"clearIconKls\"\n          @click=\"onClearIconClick\"\n        >\n          <component :is=\"clearIcon\" />\n        </el-icon>\n      </div>\n    </template>\n    <template #content>\n      <slot\n        :visible=\"pickerVisible\"\n        :actual-visible=\"pickerActualVisible\"\n        :parsed-value=\"parsedValue\"\n        :format=\"format\"\n        :date-format=\"dateFormat\"\n        :time-format=\"timeFormat\"\n        :unlink-panels=\"unlinkPanels\"\n        :type=\"type\"\n        :default-value=\"defaultValue\"\n        @pick=\"onPick\"\n        @select-range=\"setSelectionRange\"\n        @set-picker-option=\"onSetPickerOption\"\n        @calendar-change=\"onCalendarChange\"\n        @panel-change=\"onPanelChange\"\n        @keydown=\"onKeydownPopperContent\"\n        @mousedown.stop\n      />\n    </template>\n  </el-tooltip>\n</template>\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  inject,\n  nextTick,\n  provide,\n  ref,\n  unref,\n  useAttrs,\n  watch,\n} from 'vue'\nimport { isEqual } from 'lodash-unified'\nimport { onClickOutside } from '@vueuse/core'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { useFormItem, useFormSize } from '@element-plus/components/form'\nimport ElInput from '@element-plus/components/input'\nimport ElIcon from '@element-plus/components/icon'\nimport ElTooltip from '@element-plus/components/tooltip'\nimport { debugWarn, isArray } from '@element-plus/utils'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport { Calendar, Clock } from '@element-plus/icons-vue'\nimport { formatter, parseDate, valueEquals } from '../utils'\nimport { timePickerDefaultProps } from './props'\n\nimport type { Dayjs } from 'dayjs'\nimport type { ComponentPublicInstance } from 'vue'\nimport type { Options } from '@popperjs/core'\nimport type {\n  DateModelType,\n  DateOrDates,\n  DayOrDays,\n  PickerOptions,\n  SingleOrRange,\n  TimePickerDefaultProps,\n  UserInput,\n} from './props'\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\n\n// Date object and string\n\ndefineOptions({\n  name: 'Picker',\n})\n\nconst props = defineProps(timePickerDefaultProps)\nconst emit = defineEmits([\n  'update:modelValue',\n  'change',\n  'focus',\n  'blur',\n  'calendar-change',\n  'panel-change',\n  'visible-change',\n  'keydown',\n])\nconst attrs = useAttrs()\n\nconst { lang } = useLocale()\n\nconst nsDate = useNamespace('date')\nconst nsInput = useNamespace('input')\nconst nsRange = useNamespace('range')\n\nconst { form, formItem } = useFormItem()\nconst elPopperOptions = inject('ElPopperOptions', {} as Options)\n\nconst refPopper = ref<TooltipInstance>()\nconst inputRef = ref<HTMLElement | ComponentPublicInstance>()\nconst pickerVisible = ref(false)\nconst pickerActualVisible = ref(false)\nconst valueOnOpen = ref<TimePickerDefaultProps['modelValue'] | null>(null)\n\nlet hasJustTabExitedInput = false\nlet ignoreFocusEvent = false\n\nconst rangeInputKls = computed(() => [\n  nsDate.b('editor'),\n  nsDate.bm('editor', props.type),\n  nsInput.e('wrapper'),\n  nsDate.is('disabled', pickerDisabled.value),\n  nsDate.is('active', pickerVisible.value),\n  nsRange.b('editor'),\n  pickerSize ? nsRange.bm('editor', pickerSize.value) : '',\n  attrs.class,\n])\n\nconst clearIconKls = computed(() => [\n  nsInput.e('icon'),\n  nsRange.e('close-icon'),\n  !showClose.value ? nsRange.e('close-icon--hidden') : '',\n])\n\nwatch(pickerVisible, (val) => {\n  if (!val) {\n    userInput.value = null\n    nextTick(() => {\n      emitChange(props.modelValue)\n    })\n  } else {\n    nextTick(() => {\n      if (val) {\n        valueOnOpen.value = props.modelValue\n      }\n    })\n  }\n})\nconst emitChange = (\n  val: TimePickerDefaultProps['modelValue'] | null,\n  isClear?: boolean\n) => {\n  // determine user real change only\n  if (isClear || !valueEquals(val, valueOnOpen.value)) {\n    emit('change', val)\n    props.validateEvent &&\n      formItem?.validate('change').catch((err) => debugWarn(err))\n  }\n}\nconst emitInput = (input: SingleOrRange<DateModelType | Dayjs> | null) => {\n  if (!valueEquals(props.modelValue, input)) {\n    let formatted\n    if (isArray(input)) {\n      formatted = input.map((item) =>\n        formatter(item, props.valueFormat, lang.value)\n      )\n    } else if (input) {\n      formatted = formatter(input, props.valueFormat, lang.value)\n    }\n    emit('update:modelValue', input ? formatted : input, lang.value)\n  }\n}\nconst emitKeydown = (e: KeyboardEvent) => {\n  emit('keydown', e)\n}\n\nconst refInput = computed<HTMLInputElement[]>(() => {\n  if (inputRef.value) {\n    const _r = isRangeInput.value\n      ? inputRef.value\n      : (inputRef.value as any as ComponentPublicInstance).$el\n    return Array.from<HTMLInputElement>(_r.querySelectorAll('input'))\n  }\n  return []\n})\n\nconst setSelectionRange = (start: number, end: number, pos?: 'min' | 'max') => {\n  const _inputs = refInput.value\n  if (!_inputs.length) return\n  if (!pos || pos === 'min') {\n    _inputs[0].setSelectionRange(start, end)\n    _inputs[0].focus()\n  } else if (pos === 'max') {\n    _inputs[1].setSelectionRange(start, end)\n    _inputs[1].focus()\n  }\n}\nconst focusOnInputBox = () => {\n  focus(true, true)\n  nextTick(() => {\n    ignoreFocusEvent = false\n  })\n}\n\nconst onPick = (date: any = '', visible = false) => {\n  if (!visible) {\n    ignoreFocusEvent = true\n  }\n  pickerVisible.value = visible\n  let result\n  if (isArray(date)) {\n    result = date.map((_) => _.toDate())\n  } else {\n    // clear btn emit null\n    result = date ? date.toDate() : date\n  }\n  userInput.value = null\n  emitInput(result)\n}\n\nconst onBeforeShow = () => {\n  pickerActualVisible.value = true\n}\n\nconst onShow = () => {\n  emit('visible-change', true)\n}\n\nconst onKeydownPopperContent = (event: KeyboardEvent) => {\n  if ((event as KeyboardEvent)?.key === EVENT_CODE.esc) {\n    focus(true, true)\n  }\n}\n\nconst onHide = () => {\n  pickerActualVisible.value = false\n  pickerVisible.value = false\n  ignoreFocusEvent = false\n  emit('visible-change', false)\n}\n\nconst handleOpen = () => {\n  pickerVisible.value = true\n}\n\nconst handleClose = () => {\n  pickerVisible.value = false\n}\n\nconst focus = (focusStartInput = true, isIgnoreFocusEvent = false) => {\n  ignoreFocusEvent = isIgnoreFocusEvent\n  const [leftInput, rightInput] = unref(refInput)\n  let input = leftInput\n  if (!focusStartInput && isRangeInput.value) {\n    input = rightInput\n  }\n  if (input) {\n    input.focus()\n  }\n}\n\nconst handleFocusInput = (e?: FocusEvent) => {\n  if (\n    props.readonly ||\n    pickerDisabled.value ||\n    pickerVisible.value ||\n    ignoreFocusEvent\n  ) {\n    return\n  }\n  pickerVisible.value = true\n  emit('focus', e)\n}\n\nlet currentHandleBlurDeferCallback:\n  | (() => Promise<void> | undefined)\n  | undefined = undefined\n\n// Check if document.activeElement is inside popper or any input before popper close\nconst handleBlurInput = (e?: FocusEvent) => {\n  const handleBlurDefer = async () => {\n    setTimeout(() => {\n      if (currentHandleBlurDeferCallback === handleBlurDefer) {\n        if (\n          !(\n            refPopper.value?.isFocusInsideContent() && !hasJustTabExitedInput\n          ) &&\n          refInput.value.filter((input) => {\n            return input.contains(document.activeElement)\n          }).length === 0\n        ) {\n          handleChange()\n          pickerVisible.value = false\n          emit('blur', e)\n          props.validateEvent &&\n            formItem?.validate('blur').catch((err) => debugWarn(err))\n        }\n        hasJustTabExitedInput = false\n      }\n    }, 0)\n  }\n  currentHandleBlurDeferCallback = handleBlurDefer\n  handleBlurDefer()\n}\n\nconst pickerDisabled = computed(() => {\n  return props.disabled || form?.disabled\n})\n\nconst parsedValue = computed(() => {\n  let dayOrDays: DayOrDays\n  if (valueIsEmpty.value) {\n    if (pickerOptions.value.getDefaultValue) {\n      dayOrDays = pickerOptions.value.getDefaultValue()\n    }\n  } else {\n    if (isArray(props.modelValue)) {\n      dayOrDays = props.modelValue.map((d) =>\n        parseDate(d, props.valueFormat, lang.value)\n      ) as [Dayjs, Dayjs]\n    } else {\n      dayOrDays = parseDate(props.modelValue, props.valueFormat, lang.value)!\n    }\n  }\n\n  if (pickerOptions.value.getRangeAvailableTime) {\n    const availableResult = pickerOptions.value.getRangeAvailableTime(\n      dayOrDays!\n    )\n    if (!isEqual(availableResult, dayOrDays!)) {\n      dayOrDays = availableResult\n      emitInput(\n        (isArray(dayOrDays)\n          ? dayOrDays.map((_) => _.toDate())\n          : dayOrDays.toDate()) as SingleOrRange<Date>\n      )\n    }\n  }\n  if (isArray(dayOrDays!) && dayOrDays.some((day) => !day)) {\n    dayOrDays = [] as unknown as DayOrDays\n  }\n  return dayOrDays!\n})\n\nconst displayValue = computed<UserInput>(() => {\n  if (!pickerOptions.value.panelReady) return ''\n  const formattedValue = formatDayjsToString(parsedValue.value)\n  if (isArray(userInput.value)) {\n    return [\n      userInput.value[0] || (formattedValue && formattedValue[0]) || '',\n      userInput.value[1] || (formattedValue && formattedValue[1]) || '',\n    ]\n  } else if (userInput.value !== null) {\n    return userInput.value\n  }\n  if (!isTimePicker.value && valueIsEmpty.value) return ''\n  if (!pickerVisible.value && valueIsEmpty.value) return ''\n  if (formattedValue) {\n    return isDatesPicker.value\n      ? (formattedValue as Array<string>).join(', ')\n      : formattedValue\n  }\n  return ''\n})\n\nconst isTimeLikePicker = computed(() => props.type.includes('time'))\n\nconst isTimePicker = computed(() => props.type.startsWith('time'))\n\nconst isDatesPicker = computed(() => props.type === 'dates')\n\nconst triggerIcon = computed(\n  () => props.prefixIcon || (isTimeLikePicker.value ? Clock : Calendar)\n)\n\nconst showClose = ref(false)\n\nconst onClearIconClick = (event: MouseEvent) => {\n  if (props.readonly || pickerDisabled.value) return\n  if (showClose.value) {\n    event.stopPropagation()\n    focusOnInputBox()\n    emitInput(null)\n    emitChange(null, true)\n    showClose.value = false\n    pickerVisible.value = false\n    pickerOptions.value.handleClear && pickerOptions.value.handleClear()\n  }\n}\n\nconst valueIsEmpty = computed(() => {\n  const { modelValue } = props\n  return (\n    !modelValue || (isArray(modelValue) && !modelValue.filter(Boolean).length)\n  )\n})\n\nconst onMouseDownInput = async (event: MouseEvent) => {\n  if (props.readonly || pickerDisabled.value) return\n  if (\n    (event.target as HTMLElement)?.tagName !== 'INPUT' ||\n    refInput.value.includes(document.activeElement as HTMLInputElement)\n  ) {\n    pickerVisible.value = true\n  }\n}\nconst onMouseEnter = () => {\n  if (props.readonly || pickerDisabled.value) return\n  if (!valueIsEmpty.value && props.clearable) {\n    showClose.value = true\n  }\n}\nconst onMouseLeave = () => {\n  showClose.value = false\n}\nconst onTouchStartInput = (event: TouchEvent) => {\n  if (props.readonly || pickerDisabled.value) return\n  if (\n    (event.touches[0].target as HTMLElement)?.tagName !== 'INPUT' ||\n    refInput.value.includes(document.activeElement as HTMLInputElement)\n  ) {\n    pickerVisible.value = true\n  }\n}\nconst isRangeInput = computed(() => {\n  return props.type.includes('range')\n})\n\nconst pickerSize = useFormSize()\n\nconst popperEl = computed(() => unref(refPopper)?.popperRef?.contentRef)\nconst actualInputRef = computed(() => {\n  if (unref(isRangeInput)) {\n    return unref(inputRef)\n  }\n\n  return (unref(inputRef) as ComponentPublicInstance)?.$el\n})\n\nonClickOutside(actualInputRef, (e: PointerEvent) => {\n  const unrefedPopperEl = unref(popperEl)\n  const inputEl = unref(actualInputRef)\n  if (\n    (unrefedPopperEl &&\n      (e.target === unrefedPopperEl ||\n        e.composedPath().includes(unrefedPopperEl))) ||\n    e.target === inputEl ||\n    e.composedPath().includes(inputEl)\n  )\n    return\n  pickerVisible.value = false\n})\n\nconst userInput = ref<UserInput>(null)\n\nconst handleChange = () => {\n  if (userInput.value) {\n    const value = parseUserInputToDayjs(displayValue.value)\n    if (value) {\n      if (isValidValue(value)) {\n        emitInput(\n          (isArray(value)\n            ? value.map((_) => _.toDate())\n            : value.toDate()) as DateOrDates\n        )\n        userInput.value = null\n      }\n    }\n  }\n  if (userInput.value === '') {\n    emitInput(null)\n    emitChange(null)\n    userInput.value = null\n  }\n}\n\nconst parseUserInputToDayjs = (value: UserInput) => {\n  if (!value) return null\n  return pickerOptions.value.parseUserInput!(value)\n}\n\nconst formatDayjsToString = (value: DayOrDays) => {\n  if (!value) return null\n  return pickerOptions.value.formatToString!(value)\n}\n\nconst isValidValue = (value: DayOrDays) => {\n  return pickerOptions.value.isValidValue!(value)\n}\n\nconst handleKeydownInput = async (event: KeyboardEvent) => {\n  if (props.readonly || pickerDisabled.value) return\n\n  const { code } = event\n  emitKeydown(event)\n  if (code === EVENT_CODE.esc) {\n    if (pickerVisible.value === true) {\n      pickerVisible.value = false\n      event.preventDefault()\n      event.stopPropagation()\n    }\n    return\n  }\n\n  if (code === EVENT_CODE.down) {\n    if (pickerOptions.value.handleFocusPicker) {\n      event.preventDefault()\n      event.stopPropagation()\n    }\n    if (pickerVisible.value === false) {\n      pickerVisible.value = true\n      await nextTick()\n    }\n    if (pickerOptions.value.handleFocusPicker) {\n      pickerOptions.value.handleFocusPicker()\n      return\n    }\n  }\n\n  if (code === EVENT_CODE.tab) {\n    hasJustTabExitedInput = true\n    return\n  }\n\n  if (code === EVENT_CODE.enter || code === EVENT_CODE.numpadEnter) {\n    if (\n      userInput.value === null ||\n      userInput.value === '' ||\n      isValidValue(parseUserInputToDayjs(displayValue.value) as DayOrDays)\n    ) {\n      handleChange()\n      pickerVisible.value = false\n    }\n    event.stopPropagation()\n    return\n  }\n\n  // if user is typing, do not let picker handle key input\n  if (userInput.value) {\n    event.stopPropagation()\n    return\n  }\n  if (pickerOptions.value.handleKeydownInput) {\n    pickerOptions.value.handleKeydownInput(event)\n  }\n}\nconst onUserInput = (e: string) => {\n  userInput.value = e\n  // Temporary fix when the picker is dismissed and the input box\n  // is focused, just mimic the behavior of antdesign.\n  if (!pickerVisible.value) {\n    pickerVisible.value = true\n  }\n}\n\nconst handleStartInput = (event: Event) => {\n  const target = event.target as HTMLInputElement\n  if (userInput.value) {\n    userInput.value = [target.value, userInput.value[1]]\n  } else {\n    userInput.value = [target.value, null]\n  }\n}\n\nconst handleEndInput = (event: Event) => {\n  const target = event.target as HTMLInputElement\n  if (userInput.value) {\n    userInput.value = [userInput.value[0], target.value]\n  } else {\n    userInput.value = [null, target.value]\n  }\n}\n\nconst handleStartChange = () => {\n  const values = userInput.value as string[]\n  const value = parseUserInputToDayjs(values && values[0]) as Dayjs\n  const parsedVal = unref(parsedValue) as [Dayjs, Dayjs]\n  if (value && value.isValid()) {\n    userInput.value = [\n      formatDayjsToString(value) as string,\n      displayValue.value?.[1] || null,\n    ]\n    const newValue = [value, parsedVal && (parsedVal[1] || null)] as DayOrDays\n    if (isValidValue(newValue)) {\n      emitInput(newValue)\n      userInput.value = null\n    }\n  }\n}\n\nconst handleEndChange = () => {\n  const values = unref(userInput) as string[]\n  const value = parseUserInputToDayjs(values && values[1]) as Dayjs\n  const parsedVal = unref(parsedValue) as [Dayjs, Dayjs]\n  if (value && value.isValid()) {\n    userInput.value = [\n      unref(displayValue)?.[0] || null,\n      formatDayjsToString(value) as string,\n    ]\n    const newValue = [parsedVal && parsedVal[0], value] as DayOrDays\n    if (isValidValue(newValue)) {\n      emitInput(newValue)\n      userInput.value = null\n    }\n  }\n}\n\nconst pickerOptions = ref<Partial<PickerOptions>>({})\nconst onSetPickerOption = <T extends keyof PickerOptions>(\n  e: [T, PickerOptions[T]]\n) => {\n  pickerOptions.value[e[0]] = e[1]\n  pickerOptions.value.panelReady = true\n}\n\nconst onCalendarChange = (e: [Date, null | Date]) => {\n  emit('calendar-change', e)\n}\n\nconst onPanelChange = (\n  value: [Dayjs, Dayjs],\n  mode: 'month' | 'year',\n  view: unknown\n) => {\n  emit('panel-change', value, mode, view)\n}\n\nprovide('EP_PICKER_BASE', {\n  props,\n})\n\ndefineExpose({\n  /**\n   * @description focus input box.\n   */\n  focus,\n  /**\n   * @description emit focus event\n   */\n  handleFocusInput,\n  /**\n   * @description emit blur event\n   */\n  handleBlurInput,\n  /**\n   * @description opens picker\n   */\n  handleOpen,\n  /**\n   * @description closes picker\n   */\n  handleClose,\n  /**\n   * @description pick item manually\n   */\n  onPick,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;mCAoMc,CAAA;AAAA,EACZ,IAAM,EAAA,QAAA;AACR,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;AAaA,IAAA,MAAM,QAAQ,QAAS,EAAA,CAAA;AAEvB,IAAM,MAAA,EAAE,SAAS,SAAU,EAAA,CAAA;AAE3B,IAAM,MAAA,MAAA,GAAS,aAAa,MAAM,CAAA,CAAA;AAClC,IAAM,MAAA,OAAA,GAAU,aAAa,OAAO,CAAA,CAAA;AACpC,IAAM,MAAA,OAAA,GAAU,aAAa,OAAO,CAAA,CAAA;AAEpC,IAAM,MAAA,EAAE,IAAM,EAAA,QAAA,EAAA,GAAa,WAAY,EAAA,CAAA;AACvC,IAAA,MAAM,eAAkB,GAAA,MAAA,CAAO,iBAAmB,EAAA,EAAa,CAAA,CAAA;AAE/D,IAAA,MAAM,YAAY,GAAqB,EAAA,CAAA;AACvC,IAAA,MAAM,WAAW,GAA2C,EAAA,CAAA;AAC5D,IAAM,MAAA,aAAA,GAAgB,IAAI,KAAK,CAAA,CAAA;AAC/B,IAAM,MAAA,mBAAA,GAAsB,IAAI,KAAK,CAAA,CAAA;AACrC,IAAM,MAAA,WAAA,GAAc,IAAiD,IAAI,CAAA,CAAA;AAEzE,IAAA,IAAI,qBAAwB,GAAA,KAAA,CAAA;AAC5B,IAAA,IAAI,gBAAmB,GAAA,KAAA,CAAA;AAEvB,IAAM,MAAA,aAAA,GAAgB,SAAS,MAAM;AAAA,MACnC,MAAA,CAAO,EAAE,QAAQ,CAAA;AAAA,MACjB,MAAO,CAAA,EAAA,CAAG,QAAU,EAAA,KAAA,CAAM,IAAI,CAAA;AAAA,MAC9B,OAAA,CAAQ,EAAE,SAAS,CAAA;AAAA,MACnB,MAAO,CAAA,EAAA,CAAG,UAAY,EAAA,cAAA,CAAe,KAAK,CAAA;AAAA,MAC1C,MAAO,CAAA,EAAA,CAAG,QAAU,EAAA,aAAA,CAAc,KAAK,CAAA;AAAA,MACvC,OAAA,CAAQ,EAAE,QAAQ,CAAA;AAAA,MAClB,aAAa,OAAQ,CAAA,EAAA,CAAG,QAAU,EAAA,UAAA,CAAW,KAAK,CAAI,GAAA,EAAA;AAAA,MACtD,KAAM,CAAA,KAAA;AAAA,KACP,CAAA,CAAA;AAED,IAAM,MAAA,YAAA,GAAe,SAAS,MAAM;AAAA,MAClC,OAAA,CAAQ,EAAE,MAAM,CAAA;AAAA,MAChB,OAAA,CAAQ,EAAE,YAAY,CAAA;AAAA,MACtB,CAAC,SAAU,CAAA,KAAA,GAAQ,OAAQ,CAAA,CAAA,CAAE,oBAAoB,CAAI,GAAA,EAAA;AAAA,KACtD,CAAA,CAAA;AAED,IAAM,KAAA,CAAA,aAAA,EAAe,CAAC,GAAQ,KAAA;AAC5B,MAAA,IAAI,CAAC,GAAK,EAAA;AACR,QAAA,SAAA,CAAU,KAAQ,GAAA,IAAA,CAAA;AAClB,QAAA,QAAA,CAAS,MAAM;AACb,UAAA,UAAA,CAAW,MAAM,UAAU,CAAA,CAAA;AAAA,SAC5B,CAAA,CAAA;AAAA,OACI,MAAA;AACL,QAAA,QAAA,CAAS,MAAM;AACb,UAAA,IAAI,GAAK,EAAA;AACP,YAAA,WAAA,CAAY,QAAQ,KAAM,CAAA,UAAA,CAAA;AAAA,WAC5B;AAAA,SACD,CAAA,CAAA;AAAA,OACH;AAAA,KACD,CAAA,CAAA;AACD,IAAM,MAAA,UAAA,GAAa,CACjB,GAAA,EACA,OACG,KAAA;AAEH,MAAA,IAAI,WAAW,CAAC,WAAA,CAAY,GAAK,EAAA,WAAA,CAAY,KAAK,CAAG,EAAA;AACnD,QAAA,IAAA,CAAK,UAAU,GAAG,CAAA,CAAA;AAClB,QAAM,KAAA,CAAA,aAAA,KACM,QAAA,IAAA,IAAA,GAAiB,KAAA,CAAA,GAAE,QAAO,CAAA,QAAkB,CAAA,QAAA,CAAA,CAAG,KAAC,CAAA,CAAA,GAAA,KAAA,SAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA;AAAA,OAC9D;AAAA,KACF,CAAA;AACA,IAAM,MAAA,SAAA,GAAY,CAAC,KAAuD,KAAA;AACxE,MAAA,IAAI,CAAC,WAAA,CAAY,KAAM,CAAA,UAAA,EAAY,KAAK,CAAG,EAAA;AACzC,QAAI,IAAA,SAAA,CAAA;AACJ,QAAI,IAAA,OAAA,CAAQ,KAAK,CAAG,EAAA;AAClB,UAAY,SAAA,GAAA,KAAA,CAAM,GAAI,CAAA,CAAC,IACrB,KAAA,SAAA,CAAU,MAAM,KAAM,CAAA,WAAA,EAAa,IAAK,CAAA,KAAK,CAC/C,CAAA,CAAA;AAAA,mBACS,KAAO,EAAA;AAChB,UAAA,SAAA,GAAY,SAAU,CAAA,KAAA,EAAO,KAAM,CAAA,WAAA,EAAa,KAAK,KAAK,CAAA,CAAA;AAAA,SAC5D;AACA,QAAA,IAAA,CAAK,mBAAqB,EAAA,KAAA,GAAQ,SAAY,GAAA,KAAA,EAAO,KAAK,KAAK,CAAA,CAAA;AAAA,OACjE;AAAA,KACF,CAAA;AACA,IAAM,MAAA,WAAA,GAAc,CAAC,CAAqB,KAAA;AACxC,MAAA,IAAA,CAAK,WAAW,CAAC,CAAA,CAAA;AAAA,KACnB,CAAA;AAEA,IAAM,MAAA,QAAA,GAAW,SAA6B,MAAM;AAClD,MAAA,IAAI,SAAS,KAAO,EAAA;AAClB,QAAA,MAAM,KAAK,YAAa,CAAA,KAAA,GACpB,QAAS,CAAA,KAAA,GACR,SAAS,KAAyC,CAAA,GAAA,CAAA;AACvD,QAAA,OAAO,KAAM,CAAA,IAAA,CAAuB,EAAG,CAAA,gBAAA,CAAiB,OAAO,CAAC,CAAA,CAAA;AAAA,OAClE;AACA,MAAA,OAAO,EAAC,CAAA;AAAA,KACT,CAAA,CAAA;AAED,IAAA,MAAM,iBAAoB,GAAA,CAAC,KAAe,EAAA,GAAA,EAAa,GAAwB,KAAA;AAC7E,MAAA,MAAM,UAAU,QAAS,CAAA,KAAA,CAAA;AACzB,MAAA,IAAI,CAAC,OAAQ,CAAA,MAAA;AAAQ,QAAA,OAAA;AACrB,MAAI,IAAA,CAAC,GAAO,IAAA,GAAA,KAAQ,KAAO,EAAA;AACzB,QAAQ,OAAA,CAAA,CAAA,CAAA,CAAG,iBAAkB,CAAA,KAAA,EAAO,GAAG,CAAA,CAAA;AACvC,QAAA,OAAA,CAAQ,GAAG,KAAM,EAAA,CAAA;AAAA,OACnB,MAAA,IAAW,QAAQ,KAAO,EAAA;AACxB,QAAQ,OAAA,CAAA,CAAA,CAAA,CAAG,iBAAkB,CAAA,KAAA,EAAO,GAAG,CAAA,CAAA;AACvC,QAAA,OAAA,CAAQ,GAAG,KAAM,EAAA,CAAA;AAAA,OACnB;AAAA,KACF,CAAA;AACA,IAAA,MAAM,kBAAkB,MAAM;AAC5B,MAAA,KAAA,CAAM,MAAM,IAAI,CAAA,CAAA;AAChB,MAAA,QAAA,CAAS,MAAM;AACb,QAAmB,gBAAA,GAAA,KAAA,CAAA;AAAA,OACpB,CAAA,CAAA;AAAA,KACH,CAAA;AAEA,IAAA,MAAM,MAAS,GAAA,CAAC,IAAY,GAAA,EAAA,EAAI,UAAU,KAAU,KAAA;AAClD,MAAA,IAAI,CAAC,OAAS,EAAA;AACZ,QAAmB,gBAAA,GAAA,IAAA,CAAA;AAAA,OACrB;AACA,MAAA,aAAA,CAAc,KAAQ,GAAA,OAAA,CAAA;AACtB,MAAI,IAAA,MAAA,CAAA;AACJ,MAAI,IAAA,OAAA,CAAQ,IAAI,CAAG,EAAA;AACjB,QAAA,MAAA,GAAS,KAAK,GAAI,CAAA,CAAC,CAAM,KAAA,CAAA,CAAE,QAAQ,CAAA,CAAA;AAAA,OAC9B,MAAA;AAEL,QAAS,MAAA,GAAA,IAAA,GAAO,IAAK,CAAA,MAAA,EAAW,GAAA,IAAA,CAAA;AAAA,OAClC;AACA,MAAA,SAAA,CAAU,KAAQ,GAAA,IAAA,CAAA;AAClB,MAAA,SAAA,CAAU,MAAM,CAAA,CAAA;AAAA,KAClB,CAAA;AAEA,IAAA,MAAM,eAAe,MAAM;AACzB,MAAA,mBAAA,CAAoB,KAAQ,GAAA,IAAA,CAAA;AAAA,KAC9B,CAAA;AAEA,IAAA,MAAM,SAAS,MAAM;AACnB,MAAA,IAAA,CAAK,kBAAkB,IAAI,CAAA,CAAA;AAAA,KAC7B,CAAA;AAEA,IAAM,MAAA,sBAAA,GAAyB,CAAC,KAAyB,KAAA;AACvD,MAAK,IAAA,CAAA,KAAA,IAAiC,IAAA,GAAA,KAAA,CAAA,GAAW,KAAK,CAAA,GAAA,MAAA,UAAA,CAAA,GAAA,EAAA;AACpD,QAAA,KAAA,CAAM,MAAM,IAAI,CAAA,CAAA;AAAA,OAClB;AAAA,KACF,CAAA;AAEA,IAAA,MAAM,SAAS,MAAM;AACnB,MAAA,mBAAA,CAAoB,KAAQ,GAAA,KAAA,CAAA;AAC5B,MAAA,aAAA,CAAc,KAAQ,GAAA,KAAA,CAAA;AACtB,MAAmB,gBAAA,GAAA,KAAA,CAAA;AACnB,MAAA,IAAA,CAAK,kBAAkB,KAAK,CAAA,CAAA;AAAA,KAC9B,CAAA;AAEA,IAAA,MAAM,aAAa,MAAM;AACvB,MAAA,aAAA,CAAc,KAAQ,GAAA,IAAA,CAAA;AAAA,KACxB,CAAA;AAEA,IAAA,MAAM,cAAc,MAAM;AACxB,MAAA,aAAA,CAAc,KAAQ,GAAA,KAAA,CAAA;AAAA,KACxB,CAAA;AAEA,IAAA,MAAM,KAAQ,GAAA,CAAC,eAAkB,GAAA,IAAA,EAAM,qBAAqB,KAAU,KAAA;AACpE,MAAmB,gBAAA,GAAA,kBAAA,CAAA;AACnB,MAAA,MAAM,CAAC,SAAA,EAAW,UAAc,CAAA,GAAA,KAAA,CAAM,QAAQ,CAAA,CAAA;AAC9C,MAAA,IAAI,KAAQ,GAAA,SAAA,CAAA;AACZ,MAAI,IAAA,CAAC,eAAmB,IAAA,YAAA,CAAa,KAAO,EAAA;AAC1C,QAAQ,KAAA,GAAA,UAAA,CAAA;AAAA,OACV;AACA,MAAA,IAAI,KAAO,EAAA;AACT,QAAA,KAAA,CAAM,KAAM,EAAA,CAAA;AAAA,OACd;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,gBAAA,GAAmB,CAAC,CAAmB,KAAA;AAC3C,MAAA,IACE,MAAM,QACN,IAAA,cAAA,CAAe,KACf,IAAA,aAAA,CAAc,SACd,gBACA,EAAA;AACA,QAAA,OAAA;AAAA,OACF;AACA,MAAA,aAAA,CAAc,KAAQ,GAAA,IAAA,CAAA;AACtB,MAAA,IAAA,CAAK,SAAS,CAAC,CAAA,CAAA;AAAA,KACjB,CAAA;AAEA,IAAA,IAAI,8BAEY,GAAA,KAAA,CAAA,CAAA;AAGhB,IAAM,MAAA,eAAA,GAAkB,CAAC,CAAmB,KAAA;AAC1C,MAAA,MAAM,kBAAkB,YAAY;AAClC,QAAA,UAAA,CAAW,MAAM;AACf,UAAA,IAAI;AACF,UACE,IAAA,8BACwC,KAAA,eAAM,EAAA;AAG5C,YAAO,IAAA,EAAA,CAAA,CAAA,EAAA,GAAA,SAAe,CAAA,KAAA,KAAS,IAAa,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,oBAAA,EAAA,KAAA,CAAA,qBAAA,CAAA,IAAA,QAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA,KAAA,KAAA;AAAA,cAC3C,OAAA,KAAW,CACd,QAAA,CAAA,QAAA,CAAA,aAAA,CAAA,CAAA;AACA,aAAa,CAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACb,cAAA,YAAA,EAAc,CAAQ;AACtB,cAAA,aAAa,CAAC,KAAA,GAAA,KAAA,CAAA;AACd,cAAM,IAAA,CAAA,MAAA,EAAA,CAAA,CAAA,CAAA;AACoD,cAC5D,KAAA,CAAA,aAAA,KAAA,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA,SAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA;AACA,aAAwB;AAAA,YAC1B,qBAAA,GAAA,KAAA,CAAA;AAAA,WACC;AAAC,SACN,EAAA,CAAA,CAAA,CAAA;AACA,OAAiC,CAAA;AACjC,MAAgB,8BAAA,GAAA,eAAA,CAAA;AAAA,MAClB,eAAA,EAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAO,MAAA,yBAAwB,CAAA,MAAA;AAAA,MAChC,OAAA,KAAA,CAAA,QAAA,KAAA,IAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,IAAA,CAAA,QAAA,CAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAI,MAAA,WAAA,GAAA,QAAA,CAAA,MAAA;AACJ,MAAA,IAAI;AACF,MAAI,IAAA,YAAA,CAAA;AACF,QAAY,IAAA,aAAA,CAAA,KAAA,CAAA,eAAoC,EAAA;AAAA,UAClD,SAAA,GAAA,aAAA,CAAA,KAAA,CAAA,eAAA,EAAA,CAAA;AAAA,SACK;AACL,OAAI,MAAA;AACF,QAAY,IAAA,OAAA,CAAA,KAAA,CAAA,UAAiB,CAAA,EAAA;AAE7B,UACK,SAAA,GAAA,KAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,CAAA,KAAA,SAAA,CAAA,CAAA,EAAA,KAAA,CAAA,WAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACL,SAAA,MAAA;AAAqE,UACvE,SAAA,GAAA,SAAA,CAAA,KAAA,CAAA,UAAA,EAAA,KAAA,CAAA,WAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAAA,SACF;AAEA,OAAI;AACF,MAAA,IAAA,aAAwB,CAAA,KAAA,CAAA,qBAAoB,EAAA;AAG5C,QAAA,MAAK,eAAyB,GAAA,aAAA,CAAA,KAAa,CAAA,qBAAA,CAAA,SAAA,CAAA,CAAA;AACzC,QAAY,IAAA,CAAA,OAAA,CAAA,eAAA,EAAA,SAAA,CAAA,EAAA;AACZ,UAAA,SAAA,GACW,eAAS,CACd;AAEN,UACF,SAAA,CAAA,OAAA,CAAA,SAAA,CAAA,GAAA,SAAA,CAAA,GAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,MAAA,EAAA,CAAA,GAAA,SAAA,CAAA,MAAA,EAAA,CAAA,CAAA;AAAA,SACF;AACA,OAAI;AACF,MAAA,IAAA,OAAA,CAAA,SAAa,CAAA,IAAA,SAAA,CAAA,IAAA,CAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAA,EAAA;AAAA,QACf,SAAA,GAAA,EAAA,CAAA;AACA,OAAO;AAAA,MACR,OAAA,SAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAI,MAAA,eAAe,QAAM,CAAA,MAAA;AAAY,MAAO,IAAA,CAAA,aAAA,CAAA,KAAA,CAAA,UAAA;AAC5C,QAAM,OAAA,EAAA,CAAA;AACN,MAAI,MAAA,cAAkB,GAAA,mBAAQ,CAAA,WAAA,CAAA,KAAA,CAAA,CAAA;AAC5B,MAAO,IAAA,OAAA,CAAA,SAAA,CAAA,KAAA,CAAA,EAAA;AAAA,QAAA,OACK;AAAqD,UAC/D,SAAU,CAAA,KAAA,CAAM,CAAO,CAAA,IAAA,cAAA,IAAkB,eAAe,CAAO,CAAA,IAAA,EAAA;AAAA,UACjE,SAAA,CAAA,KAAA,CAAA,CAAA,CAAA,IAAA,cAAA,IAAA,cAAA,CAAA,CAAA,CAAA,IAAA,EAAA;AAAA,SACF,CAAA;AACE,OAAA,MAAA,IAAiB,SAAA,CAAA,KAAA,KAAA,IAAA,EAAA;AAAA,QACnB,OAAA,SAAA,CAAA,KAAA,CAAA;AACA,OAAI;AAA2C,MAAO,IAAA,CAAA,YAAA,CAAA,KAAA,IAAA,YAAA,CAAA,KAAA;AACtD,QAAI,OAAe,EAAA,CAAA;AAA6B,MAAO,IAAA,CAAA,aAAA,CAAA,KAAA,IAAA,YAAA,CAAA,KAAA;AACvD,QAAA,OAAoB,EAAA,CAAA;AAClB,MAAA,IAAA,cAAqB,EAAA;AAEjB,QACN,OAAA,aAAA,CAAA,KAAA,GAAA,cAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,cAAA,CAAA;AACA,OAAO;AAAA,MACR,OAAA,EAAA,CAAA;AAED,KAAA,CAAA,CAAA;AAEA,IAAA,MAAM,gBAAwB,GAAA,QAAA,CAAA,WAAiB,CAAA,IAAA,CAAA,eAAkB,CAAA,CAAA,CAAA;AAEjE,IAAA,MAAM,YAAgB,GAAA,QAAA,CAAA,MAAe,KAAA,CAAA,eAAsB,CAAA,MAAA,CAAA,CAAA,CAAA;AAE3D,IAAM,MAAA,aAAA,WACE,CAAA,MAAA,sBAAsC,CAAA,CAAA;AAG9C,IAAM,MAAA,WAAA,WAAqB,CAAA,MAAA,KAAA,CAAA,UAAA,KAAA,gBAAA,CAAA,KAAA,GAAA,KAAA,GAAA,QAAA,CAAA,CAAA,CAAA;AAE3B,IAAM,MAAA,SAAA,GAAA,GAAA,CAAA,KAA0C,CAAA,CAAA;AAC9C,IAAI,MAAA,mBAAiC,CAAA,KAAA,KAAA;AAAO,MAAA,IAAA,KAAA,CAAA,QAAA,IAAA,cAAA,CAAA,KAAA;AAC5C,QAAA;AACE,MAAA,IAAA,SAAsB,CAAA,KAAA,EAAA;AACtB,QAAgB,KAAA,CAAA,eAAA,EAAA,CAAA;AAChB,QAAA,eAAc,EAAA,CAAA;AACd,QAAA,SAAA,CAAA;AACA,QAAA,UAAU,CAAQ,IAAA,EAAA,IAAA,CAAA,CAAA;AAClB,QAAA,SAAA,CAAA,KAAsB,GAAA,KAAA,CAAA;AACtB,QAAA,aAAA,CAAc,KAAM,GAAA,KAAA,CAAA;AAA+C,QACrE,aAAA,CAAA,KAAA,CAAA,WAAA,IAAA,aAAA,CAAA,KAAA,CAAA,WAAA,EAAA,CAAA;AAAA,OACF;AAEA,KAAM,CAAA;AACJ,IAAA,MAAA,YAAuB,GAAA,QAAA,CAAA,MAAA;AACvB,MACE,MAAA,EAAC,eAAuB,KAAA,CAAA;AAA2C,MAEtE,OAAA,CAAA,UAAA,IAAA,OAAA,CAAA,UAAA,CAAA,IAAA,CAAA,UAAA,CAAA,MAAA,CAAA,OAAA,CAAA,CAAA,MAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAI,MAAA,mBAAiC,OAAA,KAAA,KAAA;AAAO,MAAA,IAAA,EAAA,CAAA;AAC5C,MACG,IAAA,KAAA,CAAM,QAAwB,IAAY,cAAA,CAAA;AAG3C,QAAA,OAAA;AAAsB,MACxB,IAAA,CAAA,CAAA,EAAA,GAAA,KAAA,CAAA,MAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,MAAA,OAAA,IAAA,QAAA,CAAA,KAAA,CAAA,QAAA,CAAA,QAAA,CAAA,aAAA,CAAA,EAAA;AAAA,QACF,aAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AACA,OAAA;AACE,KAAI,CAAA;AAAwC,IAAA,MAAA,YAAA,GAAA,MAAA;AAC5C,MAAA,IAAI,KAAC,CAAA,QAAa,IAAS,cAAiB,CAAA,KAAA;AAC1C,QAAA,OAAA;AAAkB,MACpB,IAAA,CAAA,YAAA,CAAA,KAAA,IAAA,KAAA,CAAA,SAAA,EAAA;AAAA,QACF,SAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AACA,OAAA;AACE,KAAA,CAAA;AAAkB,IACpB,MAAA,YAAA,GAAA,MAAA;AACA,MAAM,SAAA,CAAA,KAAA,GAAA,KAAA,CAAoB;AACxB,KAAI,CAAA;AAAwC,IAAA,MAAA,iBAAA,GAAA,CAAA,KAAA,KAAA;AAC5C,MACG,IAAA,EAAA,CAAA;AAGD,MAAA,IAAA,KAAA,CAAA,QAAsB,IAAA,cAAA,CAAA,KAAA;AAAA,QACxB,OAAA;AAAA,MACF,IAAA,CAAA,CAAA,EAAA,GAAA,KAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,MAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,MAAA,OAAA,IAAA,QAAA,CAAA,KAAA,CAAA,QAAA,CAAA,QAAA,CAAA,aAAA,CAAA,EAAA;AACA,QAAM,aAAA,CAAA,YAAwB,CAAM;AAClC,OAAO;AAA2B,KACnC,CAAA;AAED,IAAA,MAAM,eAAyB,QAAA,CAAA,MAAA;AAE/B,MAAA,iBAA0B,CAAA,QAAA,CAAA,SAAY;AACtC,KAAM,CAAA,CAAA;AACJ,IAAI,MAAA,wBAAqB,EAAA,CAAA;AACvB,IAAA,MAAA,WAAa,QAAQ,CAAA,MAAA;AAAA,MACvB,IAAA,EAAA,EAAA,EAAA,CAAA;AAEA,MAAQ,OAAA,CAAA,EAAA,GAAM,WAAuC,CAAA,SAAA,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,SAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAA,CAAA;AAAA,KACtD,CAAA,CAAA;AAED,IAAe,MAAA,cAAA,GAAA,QAAgB,CAAC,MAAoB;AAClD,MAAM,IAAA,EAAA,CAAA;AACN,MAAM,IAAA,KAAA,CAAA,YAAgB,CAAc,EAAA;AACpC,QAAA,qBAEK,CAAE,CAAA;AAKL,OAAA;AACF,MAAA,OAAA,CAAA,EAAA,GAAA,KAAsB,CAAA,QAAA,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,CAAA;AAAA,KACvB,CAAA,CAAA;AAED,IAAM,cAAA,CAAA,cAA+B,EAAA,CAAA,CAAA,KAAA;AAErC,MAAA,qBAA2B,GAAA,KAAA,CAAA,QAAA,CAAA,CAAA;AACzB,MAAA,gBAAqB,KAAA,CAAA,cAAA,CAAA,CAAA;AACnB,MAAM,IAAA,eAA8B,KAAA,CAAA,CAAA,MAAA,KAAA,eAAkB,IAAA,CAAA,CAAA,YAAA,EAAA,CAAA,QAAA,CAAA,eAAA,CAAA,CAAA,IAAA,CAAA,CAAA,MAAA,KAAA,OAAA,IAAA,CAAA,CAAA,YAAA,EAAA,CAAA,QAAA,CAAA,OAAA,CAAA;AACtD,QAAA,OAAW;AACT,MAAI,aAAA,CAAA,KAAA,QAAqB,CAAA;AACvB,KAAA,CAAA,CAAA;AAKA,IAAA,MAAA,SAAA,GAAU,GAAQ,CAAA,IAAA,CAAA,CAAA;AAAA,IACpB,MAAA,YAAA,GAAA,MAAA;AAAA,MACF,IAAA,SAAA,CAAA,KAAA,EAAA;AAAA,QACF,MAAA,KAAA,GAAA,qBAAA,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA;AACA,QAAI,IAAA,KAAA,EAAA;AACF,UAAA,IAAA,YAAc,CAAA,KAAA,CAAA,EAAA;AACd,YAAA,SAAe,CAAA,OAAA,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,MAAA,EAAA,CAAA,GAAA,KAAA,CAAA,MAAA,EAAA,CAAA,CAAA;AACf,YAAA,SAAkB,CAAA,KAAA,GAAA,IAAA,CAAA;AAAA,WACpB;AAAA,SACF;AAEA,OAAM;AACJ,MAAA,IAAI,SAAC,CAAA,KAAA,KAAA,EAAA,EAAA;AAAO,QAAO,SAAA,CAAA,IAAA,CAAA,CAAA;AACnB,QAAO,UAAA,CAAA,IAAA,CAAA,CAAA;AAAyC,QAClD,SAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAEA,OAAM;AACJ,KAAA,CAAA;AAAY,IAAO,MAAA,qBAAA,GAAA,CAAA,KAAA,KAAA;AACnB,MAAO,IAAA,CAAA,KAAA;AAAyC,QAClD,OAAA,IAAA,CAAA;AAEA,MAAM,OAAA,aAAgB,CAAqB,KAAA,CAAA,cAAA,CAAA,KAAA,CAAA,CAAA;AACzC,KAAO,CAAA;AAAuC,IAChD,MAAA,mBAAA,GAAA,CAAA,KAAA,KAAA;AAEA,MAAM,IAAA,CAAA,KAAA;AACJ,QAAI,OAAA;AAAwC,MAAA,OAAA,aAAA,CAAA,KAAA,CAAA,cAAA,CAAA,KAAA,CAAA,CAAA;AAE5C,KAAA,CAAA;AACA,IAAA,MAAA,YAAiB,GAAA,CAAA,KAAA,KAAA;AACjB,MAAI,OAAA,mBAAyB,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA;AAC3B,KAAI,CAAA;AACF,IAAA,MAAA,kBAAsB,GAAA,OAAA,KAAA,KAAA;AACtB,MAAA,IAAA,KAAA,CAAM,QAAe,IAAA,cAAA,CAAA,KAAA;AACrB,QAAA,OAAA;AAAsB,MACxB,MAAA,EAAA,IAAA,EAAA,GAAA,KAAA,CAAA;AACA,MAAA,WAAA,CAAA,KAAA,CAAA,CAAA;AAAA,MACF,IAAA,IAAA,KAAA,UAAA,CAAA,GAAA,EAAA;AAEA,QAAI,IAAA,mBAA0B,KAAA,IAAA,EAAA;AAC5B,UAAI,aAAA,CAAA,QAAoB,KAAmB,CAAA;AACzC,UAAA,KAAA,CAAM,cAAe,EAAA,CAAA;AACrB,UAAA,KAAA,CAAM,eAAgB,EAAA,CAAA;AAAA,SACxB;AACA,QAAI,OAAA;AACF,OAAA;AACA,MAAA,IAAA,IAAA,KAAe,UAAA,CAAA,IAAA,EAAA;AAAA,QACjB,IAAA,aAAA,CAAA,KAAA,CAAA,iBAAA,EAAA;AACA,UAAI,KAAA,CAAA,gBAAoB,CAAmB;AACzC,UAAA,KAAA,CAAA,eAAsC,EAAA,CAAA;AACtC,SAAA;AAAA,QACF,IAAA,aAAA,CAAA,KAAA,KAAA,KAAA,EAAA;AAAA,UACF,aAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAEA,UAAI,MAAA;AACF,SAAwB;AACxB,QAAA,IAAA,aAAA,CAAA,KAAA,CAAA,iBAAA,EAAA;AAAA,UACF,aAAA,CAAA,KAAA,CAAA,iBAAA,EAAA,CAAA;AAEA,UAAI,OAAS;AACX,SACE;AAIA,OAAa;AACb,MAAA,IAAA,IAAA,KAAA,UAAsB,CAAA,GAAA,EAAA;AAAA,QACxB,qBAAA,GAAA,IAAA,CAAA;AACA,QAAA,OAAsB;AACtB,OAAA;AAAA,MACF,IAAA,IAAA,KAAA,UAAA,CAAA,KAAA,IAAA,IAAA,KAAA,UAAA,CAAA,WAAA,EAAA;AAGA,QAAA,aAAqB,CAAA,KAAA,KAAA,IAAA,IAAA,SAAA,CAAA,KAAA,KAAA,EAAA,IAAA,YAAA,CAAA,qBAAA,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA,EAAA;AACnB,UAAA,YAAsB,EAAA,CAAA;AACtB,UAAA,aAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAAA,SACF;AACA,QAAI,KAAA,CAAA,iBAAwC,CAAA;AAC1C,QAAc,OAAA;AAA8B,OAC9C;AAAA,MACF,IAAA,SAAA,CAAA,KAAA,EAAA;AACA,QAAM,KAAA,CAAA,eAA6B,EAAA,CAAA;AACjC,QAAA,OAAA;AAGA,OAAI;AACF,MAAA,IAAA,aAAsB,CAAA,KAAA,CAAA,kBAAA,EAAA;AAAA,QACxB,aAAA,CAAA,KAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,CAAA;AAAA,OACF;AAEA,KAAM,CAAA;AACJ,IAAA,MAAA,WAAe,GAAM,CAAA,CAAA,KAAA;AACrB,MAAA,eAAqB,GAAA,CAAA,CAAA;AACnB,MAAA,IAAA,CAAA,mBAAmB,EAAA;AAAgC,QAC9C,aAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AACL,OAAA;AAAqC,KACvC,CAAA;AAAA,IACF,MAAA,gBAAA,GAAA,CAAA,KAAA,KAAA;AAEA,MAAM,MAAA,MAAA,GAAA,KAAA,CAAiB,MAAkB,CAAA;AACvC,MAAA,IAAA,eAAqB,EAAA;AACrB,QAAA,eAAqB,GAAA,CAAA,MAAA,CAAA,KAAA,EAAA,SAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACnB,OAAA,MAAA;AAAmD,QAC9C,SAAA,CAAA,KAAA,GAAA,CAAA,MAAA,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AACL,OAAA;AAAqC,KACvC,CAAA;AAAA,IACF,MAAA,cAAA,GAAA,CAAA,KAAA,KAAA;AAEA,MAAA,2BAAgC,CAAA;AAC9B,MAAA,IAAA,eAAyB,EAAA;AACzB,QAAA,SAAc,CAAA,KAAA,GAAA,CAAA,SAAA,CAAA,KAAsB,CAAU,CAAA,CAAA,EAAA,MAAA,CAAA,KAAO,CAAE,CAAA;AACvD,OAAM,MAAA;AACN,QAAI,SAAA,CAAA,KAAe,GAAA,CAAA,IAAA,EAAQ,MAAG,CAAA,KAAA,CAAA,CAAA;AAC5B,OAAA;AAAkB,KAAA,CAAA;AACS,IACzB,MAAA,0BAA2B;AAAA,MAC7B,IAAA,EAAA,CAAA;AACA,MAAA,MAAA,kBAAkB,CAAA,KAAqB,CAAA;AACvC,MAAI,MAAA,KAAA,GAAA,qBAAwB,CAAA,MAAA,IAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAC1B,MAAA,MAAA,SAAkB,GAAA,KAAA,CAAA,WAAA,CAAA,CAAA;AAClB,MAAA,IAAA,KAAA,IAAA,KAAkB,CAAA,OAAA,EAAA,EAAA;AAAA,QACpB,SAAA,CAAA,KAAA,GAAA;AAAA,UACF,mBAAA,CAAA,KAAA,CAAA;AAAA,UACF,CAAA,CAAA,EAAA,GAAA,YAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,KAAA,IAAA;AAEA,SAAA,CAAM;AACJ,QAAM,MAAA,iBAAwB,EAAA,SAAA,KAAA,SAAA,CAAA,CAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA;AAC9B,QAAA,IAAM,YAAQ,CAAA,QAAA,CAAA,EAAA;AACd,UAAM,SAAA,CAAA,UAAkB;AACxB,UAAI,SAAS,CAAM,KAAA,GAAA,IAAA,CAAQ;AACzB,SAAA;AAAkB,OAChB;AAA4B,KAAA,CAAA;AACH,IAC3B,MAAA,eAAA,GAAA,MAAA;AACA,MAAA,IAAA,EAAA,CAAA;AACA,MAAI,MAAA,MAAA,GAAA,eAAwB,CAAA,CAAA;AAC1B,MAAA,MAAA,KAAA,GAAU,qBAAQ,CAAA,MAAA,IAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAClB,MAAA,MAAA,SAAkB,GAAA,KAAA,CAAA,WAAA,CAAA,CAAA;AAAA,MACpB,IAAA,KAAA,IAAA,KAAA,CAAA,OAAA,EAAA,EAAA;AAAA,QACF,SAAA,CAAA,KAAA,GAAA;AAAA,UACF,CAAA,CAAA,EAAA,GAAA,KAAA,CAAA,YAAA,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,KAAA,IAAA;AAEA,UAAM,mBAA4C,CAAA,KAAE,CAAA;AACpD,SAAM,CAAA;AAGJ,QAAc,MAAA,QAAA,GAAA,CAAM,SAAU,IAAA,SAAA,CAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA;AAC9B,QAAA,IAAA,qBAAiC,CAAA,EAAA;AAAA,UACnC,SAAA,CAAA,QAAA,CAAA,CAAA;AAEA,UAAM,SAAA,CAAA,KAAA,GAAA,IAA+C,CAAA;AACnD,SAAA;AAAyB,OAC3B;AAEA,KAAA,CAAA;AAKE,IAAK,MAAA,aAAA,GAAA,GAAuB,CAAA,EAAA,CAAA,CAAA;AAAU,IACxC,MAAA,iBAAA,GAAA,CAAA,CAAA,KAAA;AAEA,MAAA,aAA0B,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,MACxB,aAAA,CAAA,KAAA,CAAA,UAAA,GAAA,IAAA,CAAA;AAAA,KACD,CAAA;AAED,IAAa,MAAA,gBAAA,GAAA,CAAA,CAAA,KAAA;AAAA,MAIX,IAAA,CAAA,iBAAA,EAAA,CAAA,CAAA,CAAA;AAAA,KAIA,CAAA;AAAA,IAIA,MAAA,aAAA,GAAA,CAAA,KAAA,EAAA,IAAA,EAAA,IAAA,KAAA;AAAA,MAIA,IAAA,CAAA,cAAA,EAAA,KAAA,EAAA,IAAA,EAAA,IAAA,CAAA,CAAA;AAAA,KAIA,CAAA;AAAA,IAIA,OAAA,CAAA,gBAAA,EAAA;AAAA,MACD,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}