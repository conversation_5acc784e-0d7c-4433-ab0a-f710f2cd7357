{"version": 3, "file": "panel-time-range.mjs", "sources": ["../../../../../../../packages/components/time-picker/src/time-picker-com/panel-time-range.vue"], "sourcesContent": ["<template>\n  <div\n    v-if=\"actualVisible\"\n    :class=\"[nsTime.b('range-picker'), nsPicker.b('panel')]\"\n  >\n    <div :class=\"nsTime.be('range-picker', 'content')\">\n      <div :class=\"nsTime.be('range-picker', 'cell')\">\n        <div :class=\"nsTime.be('range-picker', 'header')\">\n          {{ t('el.datepicker.startTime') }}\n        </div>\n        <div :class=\"startContainerKls\">\n          <time-spinner\n            ref=\"minSpinner\"\n            role=\"start\"\n            :show-seconds=\"showSeconds\"\n            :am-pm-mode=\"amPmMode\"\n            :arrow-control=\"arrowControl\"\n            :spinner-date=\"startTime\"\n            :disabled-hours=\"disabledHours_\"\n            :disabled-minutes=\"disabledMinutes_\"\n            :disabled-seconds=\"disabledSeconds_\"\n            @change=\"handleMinChange\"\n            @set-option=\"onSetOption\"\n            @select-range=\"setMinSelectionRange\"\n          />\n        </div>\n      </div>\n      <div :class=\"nsTime.be('range-picker', 'cell')\">\n        <div :class=\"nsTime.be('range-picker', 'header')\">\n          {{ t('el.datepicker.endTime') }}\n        </div>\n        <div :class=\"endContainerKls\">\n          <time-spinner\n            ref=\"maxSpinner\"\n            role=\"end\"\n            :show-seconds=\"showSeconds\"\n            :am-pm-mode=\"amPmMode\"\n            :arrow-control=\"arrowControl\"\n            :spinner-date=\"endTime\"\n            :disabled-hours=\"disabledHours_\"\n            :disabled-minutes=\"disabledMinutes_\"\n            :disabled-seconds=\"disabledSeconds_\"\n            @change=\"handleMaxChange\"\n            @set-option=\"onSetOption\"\n            @select-range=\"setMaxSelectionRange\"\n          />\n        </div>\n      </div>\n    </div>\n    <div :class=\"nsTime.be('panel', 'footer')\">\n      <button\n        type=\"button\"\n        :class=\"[nsTime.be('panel', 'btn'), 'cancel']\"\n        @click=\"handleCancel()\"\n      >\n        {{ t('el.datepicker.cancel') }}\n      </button>\n      <button\n        type=\"button\"\n        :class=\"[nsTime.be('panel', 'btn'), 'confirm']\"\n        :disabled=\"btnConfirmDisabled\"\n        @click=\"handleConfirm()\"\n      >\n        {{ t('el.datepicker.confirm') }}\n      </button>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, ref, unref } from 'vue'\nimport dayjs from 'dayjs'\nimport { union } from 'lodash-unified'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { isArray } from '@element-plus/utils'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport { panelTimeRangeProps } from '../props/panel-time-range'\nimport { useTimePanel } from '../composables/use-time-panel'\nimport {\n  buildAvailableTimeSlotGetter,\n  useOldValue,\n} from '../composables/use-time-picker'\nimport TimeSpinner from './basic-time-spinner.vue'\n\nimport type { Dayjs } from 'dayjs'\n\nconst props = defineProps(panelTimeRangeProps)\nconst emit = defineEmits(['pick', 'select-range', 'set-picker-option'])\n\nconst makeSelectRange = (start: number, end: number) => {\n  const result: number[] = []\n  for (let i = start; i <= end; i++) {\n    result.push(i)\n  }\n  return result\n}\n\nconst { t, lang } = useLocale()\nconst nsTime = useNamespace('time')\nconst nsPicker = useNamespace('picker')\nconst pickerBase = inject('EP_PICKER_BASE') as any\nconst {\n  arrowControl,\n  disabledHours,\n  disabledMinutes,\n  disabledSeconds,\n  defaultValue,\n} = pickerBase.props\n\nconst startContainerKls = computed(() => [\n  nsTime.be('range-picker', 'body'),\n  nsTime.be('panel', 'content'),\n  nsTime.is('arrow', arrowControl),\n  showSeconds.value ? 'has-seconds' : '',\n])\nconst endContainerKls = computed(() => [\n  nsTime.be('range-picker', 'body'),\n  nsTime.be('panel', 'content'),\n  nsTime.is('arrow', arrowControl),\n  showSeconds.value ? 'has-seconds' : '',\n])\n\nconst startTime = computed(() => props.parsedValue![0])\nconst endTime = computed(() => props.parsedValue![1])\nconst oldValue = useOldValue(props)\nconst handleCancel = () => {\n  emit('pick', oldValue.value, false)\n}\nconst showSeconds = computed(() => {\n  return props.format.includes('ss')\n})\nconst amPmMode = computed(() => {\n  if (props.format.includes('A')) return 'A'\n  if (props.format.includes('a')) return 'a'\n  return ''\n})\n\nconst handleConfirm = (visible = false) => {\n  emit('pick', [startTime.value, endTime.value], visible)\n}\n\nconst handleMinChange = (date: Dayjs) => {\n  handleChange(date.millisecond(0), endTime.value)\n}\nconst handleMaxChange = (date: Dayjs) => {\n  handleChange(startTime.value, date.millisecond(0))\n}\n\nconst isValidValue = (_date: Dayjs[]) => {\n  const parsedDate = _date.map((_) => dayjs(_).locale(lang.value))\n  const result = getRangeAvailableTime(parsedDate)\n  return parsedDate[0].isSame(result[0]) && parsedDate[1].isSame(result[1])\n}\n\nconst handleChange = (start: Dayjs, end: Dayjs) => {\n  // todo getRangeAvailableTime(_date).millisecond(0)\n  emit('pick', [start, end], true)\n}\nconst btnConfirmDisabled = computed(() => {\n  return startTime.value > endTime.value\n})\n\nconst selectionRange = ref([0, 2])\nconst setMinSelectionRange = (start: number, end: number) => {\n  emit('select-range', start, end, 'min')\n  selectionRange.value = [start, end]\n}\n\nconst offset = computed(() => (showSeconds.value ? 11 : 8))\nconst setMaxSelectionRange = (start: number, end: number) => {\n  emit('select-range', start, end, 'max')\n  const _offset = unref(offset)\n  selectionRange.value = [start + _offset, end + _offset]\n}\n\nconst changeSelectionRange = (step: number) => {\n  const list = showSeconds.value ? [0, 3, 6, 11, 14, 17] : [0, 3, 8, 11]\n  const mapping = ['hours', 'minutes'].concat(\n    showSeconds.value ? ['seconds'] : []\n  )\n  const index = list.indexOf(selectionRange.value[0])\n  const next = (index + step + list.length) % list.length\n  const half = list.length / 2\n  if (next < half) {\n    timePickerOptions['start_emitSelectRange'](mapping[next])\n  } else {\n    timePickerOptions['end_emitSelectRange'](mapping[next - half])\n  }\n}\n\nconst handleKeydown = (event: KeyboardEvent) => {\n  const code = event.code\n\n  const { left, right, up, down } = EVENT_CODE\n\n  if ([left, right].includes(code)) {\n    const step = code === left ? -1 : 1\n    changeSelectionRange(step)\n    event.preventDefault()\n    return\n  }\n\n  if ([up, down].includes(code)) {\n    const step = code === up ? -1 : 1\n    const role = selectionRange.value[0] < offset.value ? 'start' : 'end'\n    timePickerOptions[`${role}_scrollDown`](step)\n    event.preventDefault()\n    return\n  }\n}\n\nconst disabledHours_ = (role: string, compare?: Dayjs) => {\n  const defaultDisable = disabledHours ? disabledHours(role) : []\n  const isStart = role === 'start'\n  const compareDate = compare || (isStart ? endTime.value : startTime.value)\n  const compareHour = compareDate.hour()\n  const nextDisable = isStart\n    ? makeSelectRange(compareHour + 1, 23)\n    : makeSelectRange(0, compareHour - 1)\n  return union(defaultDisable, nextDisable)\n}\nconst disabledMinutes_ = (hour: number, role: string, compare?: Dayjs) => {\n  const defaultDisable = disabledMinutes ? disabledMinutes(hour, role) : []\n  const isStart = role === 'start'\n  const compareDate = compare || (isStart ? endTime.value : startTime.value)\n  const compareHour = compareDate.hour()\n  if (hour !== compareHour) {\n    return defaultDisable\n  }\n  const compareMinute = compareDate.minute()\n  const nextDisable = isStart\n    ? makeSelectRange(compareMinute + 1, 59)\n    : makeSelectRange(0, compareMinute - 1)\n  return union(defaultDisable, nextDisable)\n}\nconst disabledSeconds_ = (\n  hour: number,\n  minute: number,\n  role: string,\n  compare?: Dayjs\n) => {\n  const defaultDisable = disabledSeconds\n    ? disabledSeconds(hour, minute, role)\n    : []\n  const isStart = role === 'start'\n  const compareDate = compare || (isStart ? endTime.value : startTime.value)\n  const compareHour = compareDate.hour()\n  const compareMinute = compareDate.minute()\n  if (hour !== compareHour || minute !== compareMinute) {\n    return defaultDisable\n  }\n  const compareSecond = compareDate.second()\n  const nextDisable = isStart\n    ? makeSelectRange(compareSecond + 1, 59)\n    : makeSelectRange(0, compareSecond - 1)\n  return union(defaultDisable, nextDisable)\n}\n\nconst getRangeAvailableTime = ([start, end]: Array<Dayjs>) => {\n  return [\n    getAvailableTime(start, 'start', true, end),\n    getAvailableTime(end, 'end', false, start),\n  ] as const\n}\n\nconst { getAvailableHours, getAvailableMinutes, getAvailableSeconds } =\n  buildAvailableTimeSlotGetter(\n    disabledHours_,\n    disabledMinutes_,\n    disabledSeconds_\n  )\n\nconst {\n  timePickerOptions,\n\n  getAvailableTime,\n  onSetOption,\n} = useTimePanel({\n  getAvailableHours,\n  getAvailableMinutes,\n  getAvailableSeconds,\n})\n\nconst parseUserInput = (days: Dayjs[] | Dayjs) => {\n  if (!days) return null\n  if (isArray(days)) {\n    return days.map((d) => dayjs(d, props.format).locale(lang.value))\n  }\n  return dayjs(days, props.format).locale(lang.value)\n}\n\nconst formatToString = (days: Dayjs[] | Dayjs) => {\n  if (!days) return null\n  if (isArray(days)) {\n    return days.map((d) => d.format(props.format))\n  }\n  return days.format(props.format)\n}\n\nconst getDefaultValue = () => {\n  if (isArray(defaultValue)) {\n    return defaultValue.map((d: Date) => dayjs(d).locale(lang.value))\n  }\n  const defaultDay = dayjs(defaultValue).locale(lang.value)\n  return [defaultDay, defaultDay.add(60, 'm')]\n}\n\nemit('set-picker-option', ['formatToString', formatToString])\nemit('set-picker-option', ['parseUserInput', parseUserInput])\nemit('set-picker-option', ['isValidValue', isValidValue])\nemit('set-picker-option', ['handleKeydownInput', handleKeydown])\nemit('set-picker-option', ['getDefaultValue', getDefaultValue])\nemit('set-picker-option', ['getRangeAvailableTime', getRangeAvailableTime])\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAyFA,IAAM,MAAA,eAAA,GAAkB,CAAC,KAAA,EAAe,GAAgB,KAAA;AACtD,MAAA,MAAM,SAAmB,EAAC,CAAA;AAC1B,MAAA,KAAA,IAAS,CAAI,GAAA,KAAA,EAAO,CAAK,IAAA,GAAA,EAAK,CAAK,EAAA,EAAA;AACjC,QAAA,MAAA,CAAO,KAAK,CAAC,CAAA,CAAA;AAAA,OACf;AACA,MAAO,OAAA,MAAA,CAAA;AAAA,KACT,CAAA;AAEA,IAAM,MAAA,EAAE,CAAG,EAAA,IAAA,EAAA,GAAS,SAAU,EAAA,CAAA;AAC9B,IAAM,MAAA,MAAA,GAAS,aAAa,MAAM,CAAA,CAAA;AAClC,IAAM,MAAA,QAAA,GAAW,aAAa,QAAQ,CAAA,CAAA;AACtC,IAAM,MAAA,UAAA,GAAa,OAAO,gBAAgB,CAAA,CAAA;AAC1C,IAAM,MAAA;AAAA,MACJ,YAAA;AAAA,MACA,aAAA;AAAA,MACA,eAAA;AAAA,MACA,eAAA;AAAA,MACA,YAAA;AAAA,KAAA,GACE,UAAW,CAAA,KAAA,CAAA;AAEf,IAAM,MAAA,iBAAA,GAAoB,SAAS,MAAM;AAAA,MACvC,MAAA,CAAO,EAAG,CAAA,cAAA,EAAgB,MAAM,CAAA;AAAA,MAChC,MAAA,CAAO,EAAG,CAAA,OAAA,EAAS,SAAS,CAAA;AAAA,MAC5B,MAAA,CAAO,EAAG,CAAA,OAAA,EAAS,YAAY,CAAA;AAAA,MAC/B,WAAA,CAAY,QAAQ,aAAgB,GAAA,EAAA;AAAA,KACrC,CAAA,CAAA;AACD,IAAM,MAAA,eAAA,GAAkB,SAAS,MAAM;AAAA,MACrC,MAAA,CAAO,EAAG,CAAA,cAAA,EAAgB,MAAM,CAAA;AAAA,MAChC,MAAA,CAAO,EAAG,CAAA,OAAA,EAAS,SAAS,CAAA;AAAA,MAC5B,MAAA,CAAO,EAAG,CAAA,OAAA,EAAS,YAAY,CAAA;AAAA,MAC/B,WAAA,CAAY,QAAQ,aAAgB,GAAA,EAAA;AAAA,KACrC,CAAA,CAAA;AAED,IAAA,MAAM,SAAY,GAAA,QAAA,CAAS,MAAM,KAAA,CAAM,YAAa,CAAE,CAAA,CAAA,CAAA;AACtD,IAAA,MAAM,OAAU,GAAA,QAAA,CAAS,MAAM,KAAA,CAAM,YAAa,CAAE,CAAA,CAAA,CAAA;AACpD,IAAM,MAAA,QAAA,GAAW,YAAY,KAAK,CAAA,CAAA;AAClC,IAAA,MAAM,eAAe,MAAM;AACzB,MAAK,IAAA,CAAA,MAAA,EAAQ,QAAS,CAAA,KAAA,EAAO,KAAK,CAAA,CAAA;AAAA,KACpC,CAAA;AACA,IAAM,MAAA,WAAA,GAAc,SAAS,MAAM;AACjC,MAAO,OAAA,KAAA,CAAM,MAAO,CAAA,QAAA,CAAS,IAAI,CAAA,CAAA;AAAA,KAClC,CAAA,CAAA;AACD,IAAM,MAAA,QAAA,GAAW,SAAS,MAAM;AAC9B,MAAI,IAAA,KAAA,CAAM,MAAO,CAAA,QAAA,CAAS,GAAG,CAAA;AAAG,QAAO,OAAA,GAAA,CAAA;AACvC,MAAI,IAAA,KAAA,CAAM,MAAO,CAAA,QAAA,CAAS,GAAG,CAAA;AAAG,QAAO,OAAA,GAAA,CAAA;AACvC,MAAO,OAAA,EAAA,CAAA;AAAA,KACR,CAAA,CAAA;AAED,IAAM,MAAA,aAAA,GAAgB,CAAC,OAAA,GAAU,KAAU,KAAA;AACzC,MAAA,IAAA,CAAK,QAAQ,CAAC,SAAA,CAAU,OAAO,OAAQ,CAAA,KAAK,GAAG,OAAO,CAAA,CAAA;AAAA,KACxD,CAAA;AAEA,IAAM,MAAA,eAAA,GAAkB,CAAC,IAAgB,KAAA;AACvC,MAAA,YAAA,CAAa,IAAK,CAAA,WAAA,CAAY,CAAC,CAAA,EAAG,QAAQ,KAAK,CAAA,CAAA;AAAA,KACjD,CAAA;AACA,IAAM,MAAA,eAAA,GAAkB,CAAC,IAAgB,KAAA;AACvC,MAAA,YAAA,CAAa,SAAU,CAAA,KAAA,EAAO,IAAK,CAAA,WAAA,CAAY,CAAC,CAAC,CAAA,CAAA;AAAA,KACnD,CAAA;AAEA,IAAM,MAAA,YAAA,GAAe,CAAC,KAAmB,KAAA;AACvC,MAAM,MAAA,UAAA,GAAa,KAAM,CAAA,GAAA,CAAI,CAAC,CAAA,KAAM,KAAM,CAAA,CAAC,CAAE,CAAA,MAAA,CAAO,IAAK,CAAA,KAAK,CAAC,CAAA,CAAA;AAC/D,MAAM,MAAA,MAAA,GAAS,sBAAsB,UAAU,CAAA,CAAA;AAC/C,MAAO,OAAA,UAAA,CAAW,CAAG,CAAA,CAAA,MAAA,CAAO,MAAO,CAAA,CAAA,CAAE,KAAK,UAAW,CAAA,CAAA,CAAA,CAAG,MAAO,CAAA,MAAA,CAAO,CAAE,CAAA,CAAA,CAAA;AAAA,KAC1E,CAAA;AAEA,IAAM,MAAA,YAAA,GAAe,CAAC,KAAA,EAAc,GAAe,KAAA;AAEjD,MAAA,IAAA,CAAK,MAAQ,EAAA,CAAC,KAAO,EAAA,GAAG,GAAG,IAAI,CAAA,CAAA;AAAA,KACjC,CAAA;AACA,IAAM,MAAA,kBAAA,GAAqB,SAAS,MAAM;AACxC,MAAO,OAAA,SAAA,CAAU,QAAQ,OAAQ,CAAA,KAAA,CAAA;AAAA,KAClC,CAAA,CAAA;AAED,IAAA,MAAM,cAAiB,GAAA,GAAA,CAAI,CAAC,CAAA,EAAG,CAAC,CAAC,CAAA,CAAA;AACjC,IAAM,MAAA,oBAAA,GAAuB,CAAC,KAAA,EAAe,GAAgB,KAAA;AAC3D,MAAK,IAAA,CAAA,cAAA,EAAgB,KAAO,EAAA,GAAA,EAAK,KAAK,CAAA,CAAA;AACtC,MAAe,cAAA,CAAA,KAAA,GAAQ,CAAC,KAAA,EAAO,GAAG,CAAA,CAAA;AAAA,KACpC,CAAA;AAEA,IAAA,MAAM,SAAS,QAAS,CAAA,MAAO,WAAY,CAAA,KAAA,GAAQ,KAAK,CAAE,CAAA,CAAA;AAC1D,IAAM,MAAA,oBAAA,GAAuB,CAAC,KAAA,EAAe,GAAgB,KAAA;AAC3D,MAAK,IAAA,CAAA,cAAA,EAAgB,KAAO,EAAA,GAAA,EAAK,KAAK,CAAA,CAAA;AACtC,MAAM,MAAA,OAAA,GAAU,MAAM,MAAM,CAAA,CAAA;AAC5B,MAAA,cAAA,CAAe,KAAQ,GAAA,CAAC,KAAQ,GAAA,OAAA,EAAS,MAAM,OAAO,CAAA,CAAA;AAAA,KACxD,CAAA;AAEA,IAAM,MAAA,oBAAA,GAAuB,CAAC,IAAiB,KAAA;AAC7C,MAAA,MAAM,IAAO,GAAA,WAAA,CAAY,KAAQ,GAAA,CAAC,GAAG,CAAG,EAAA,CAAA,EAAG,EAAI,EAAA,EAAA,EAAI,EAAE,CAAI,GAAA,CAAC,CAAG,EAAA,CAAA,EAAG,GAAG,EAAE,CAAA,CAAA;AACrE,MAAA,MAAM,OAAU,GAAA,CAAC,OAAS,EAAA,SAAS,CAAE,CAAA,MAAA,CACnC,WAAY,CAAA,KAAA,GAAQ,CAAC,SAAS,CAAI,GAAA,EACpC,CAAA,CAAA;AACA,MAAA,MAAM,KAAQ,GAAA,IAAA,CAAK,OAAQ,CAAA,cAAA,CAAe,MAAM,CAAE,CAAA,CAAA,CAAA;AAClD,MAAA,MAAM,IAAQ,GAAA,CAAA,KAAA,GAAQ,IAAO,GAAA,IAAA,CAAK,UAAU,IAAK,CAAA,MAAA,CAAA;AACjD,MAAM,MAAA,IAAA,GAAO,KAAK,MAAS,GAAA,CAAA,CAAA;AAC3B,MAAA,IAAI,OAAO,IAAM,EAAA;AACf,QAAkB,iBAAA,CAAA,uBAAA,CAAA,CAAyB,QAAQ,IAAK,CAAA,CAAA,CAAA;AAAA,OACnD,MAAA;AACL,QAAkB,iBAAA,CAAA,qBAAA,CAAA,CAAuB,OAAQ,CAAA,IAAA,GAAO,IAAK,CAAA,CAAA,CAAA;AAAA,OAC/D;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,aAAA,GAAgB,CAAC,KAAyB,KAAA;AAC9C,MAAA,MAAM,OAAO,KAAM,CAAA,IAAA,CAAA;AAEnB,MAAA,MAAM,EAAE,IAAA,EAAM,KAAO,EAAA,EAAA,EAAI,IAAS,EAAA,GAAA,UAAA,CAAA;AAElC,MAAA,IAAI,CAAC,IAAM,EAAA,KAAK,CAAE,CAAA,QAAA,CAAS,IAAI,CAAG,EAAA;AAChC,QAAM,MAAA,IAAA,GAAO,IAAS,KAAA,IAAA,GAAO,CAAK,CAAA,GAAA,CAAA,CAAA;AAClC,QAAA,oBAAA,CAAqB,IAAI,CAAA,CAAA;AACzB,QAAA,KAAA,CAAM,cAAe,EAAA,CAAA;AACrB,QAAA,OAAA;AAAA,OACF;AAEA,MAAA,IAAI,CAAC,EAAI,EAAA,IAAI,CAAE,CAAA,QAAA,CAAS,IAAI,CAAG,EAAA;AAC7B,QAAM,MAAA,IAAA,GAAO,IAAS,KAAA,EAAA,GAAK,CAAK,CAAA,GAAA,CAAA,CAAA;AAChC,QAAA,MAAM,OAAO,cAAe,CAAA,KAAA,CAAM,CAAK,CAAA,GAAA,MAAA,CAAO,QAAQ,OAAU,GAAA,KAAA,CAAA;AAChE,QAAkB,iBAAA,CAAA,CAAA,EAAG,mBAAmB,IAAI,CAAA,CAAA;AAC5C,QAAA,KAAA,CAAM,cAAe,EAAA,CAAA;AACrB,QAAA,OAAA;AAAA,OACF;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,cAAA,GAAiB,CAAC,IAAA,EAAc,OAAoB,KAAA;AACxD,MAAA,MAAM,cAAiB,GAAA,aAAA,GAAgB,aAAc,CAAA,IAAI,IAAI,EAAC,CAAA;AAC9D,MAAA,MAAM,UAAU,IAAS,KAAA,OAAA,CAAA;AACzB,MAAA,MAAM,WAAc,GAAA,OAAA,KAAsB,OAAA,GAAA,OAAA,CAAQ,QAAQ,SAAU,CAAA,KAAA,CAAA,CAAA;AACpE,MAAM,MAAA,WAAA,GAAc,YAAY,IAAK,EAAA,CAAA;AACrC,MAAM,MAAA,WAAA,GAAc,OAChB,GAAA,eAAA,CAAgB,WAAc,GAAA,CAAA,EAAG,EAAE,CACnC,GAAA,eAAA,CAAgB,CAAG,EAAA,WAAA,GAAc,CAAC,CAAA,CAAA;AACtC,MAAO,OAAA,KAAA,CAAM,gBAAgB,WAAW,CAAA,CAAA;AAAA,KAC1C,CAAA;AACA,IAAA,MAAM,gBAAmB,GAAA,CAAC,IAAc,EAAA,IAAA,EAAc,OAAoB,KAAA;AACxE,MAAA,MAAM,iBAAiB,eAAkB,GAAA,eAAA,CAAgB,IAAM,EAAA,IAAI,IAAI,EAAC,CAAA;AACxE,MAAA,MAAM,UAAU,IAAS,KAAA,OAAA,CAAA;AACzB,MAAA,MAAM,WAAc,GAAA,OAAA,KAAsB,OAAA,GAAA,OAAA,CAAQ,QAAQ,SAAU,CAAA,KAAA,CAAA,CAAA;AACpE,MAAM,MAAA,WAAA,GAAc,YAAY,IAAK,EAAA,CAAA;AACrC,MAAA,IAAI,SAAS,WAAa,EAAA;AACxB,QAAO,OAAA,cAAA,CAAA;AAAA,OACT;AACA,MAAM,MAAA,aAAA,GAAgB,YAAY,MAAO,EAAA,CAAA;AACzC,MAAM,MAAA,WAAA,GAAc,OAChB,GAAA,eAAA,CAAgB,aAAgB,GAAA,CAAA,EAAG,EAAE,CACrC,GAAA,eAAA,CAAgB,CAAG,EAAA,aAAA,GAAgB,CAAC,CAAA,CAAA;AACxC,MAAO,OAAA,KAAA,CAAM,gBAAgB,WAAW,CAAA,CAAA;AAAA,KAC1C,CAAA;AACA,IAAA,MAAM,gBAAmB,GAAA,CACvB,IACA,EAAA,MAAA,EACA,MACA,OACG,KAAA;AACH,MAAA,MAAM,iBAAiB,eACnB,GAAA,eAAA,CAAgB,MAAM,MAAQ,EAAA,IAAI,IAClC,EAAC,CAAA;AACL,MAAA,MAAM,UAAU,IAAS,KAAA,OAAA,CAAA;AACzB,MAAA,MAAM,WAAc,GAAA,OAAA,KAAsB,OAAA,GAAA,OAAA,CAAQ,QAAQ,SAAU,CAAA,KAAA,CAAA,CAAA;AACpE,MAAM,MAAA,WAAA,GAAc,YAAY,IAAK,EAAA,CAAA;AACrC,MAAM,MAAA,aAAA,GAAgB,YAAY,MAAO,EAAA,CAAA;AACzC,MAAI,IAAA,IAAA,KAAS,WAAe,IAAA,MAAA,KAAW,aAAe,EAAA;AACpD,QAAO,OAAA,cAAA,CAAA;AAAA,OACT;AACA,MAAM,MAAA,aAAA,GAAgB,YAAY,MAAO,EAAA,CAAA;AACzC,MAAM,MAAA,WAAA,GAAc,OAChB,GAAA,eAAA,CAAgB,aAAgB,GAAA,CAAA,EAAG,EAAE,CACrC,GAAA,eAAA,CAAgB,CAAG,EAAA,aAAA,GAAgB,CAAC,CAAA,CAAA;AACxC,MAAO,OAAA,KAAA,CAAM,gBAAgB,WAAW,CAAA,CAAA;AAAA,KAC1C,CAAA;AAEA,IAAA,MAAM,qBAAwB,GAAA,CAAC,CAAC,KAAA,EAAO,GAAuB,CAAA,KAAA;AAC5D,MAAO,OAAA;AAAA,QACL,gBAAiB,CAAA,KAAA,EAAO,OAAS,EAAA,IAAA,EAAM,GAAG,CAAA;AAAA,QAC1C,gBAAiB,CAAA,GAAA,EAAK,KAAO,EAAA,KAAA,EAAO,KAAK,CAAA;AAAA,OAC3C,CAAA;AAAA,KACF,CAAA;AAEA,IAAA,MAAM,EAAE,iBAAmB,EAAA,mBAAA,EAAqB,wBAC9C,4BACE,CAAA,cAAA,EACA,kBACA,gBACF,CAAA,CAAA;AAEF,IAAM,MAAA;AAAA,MACJ,iBAAA;AAAA,MAEA,gBAAA;AAAA,MACA,WAAA;AAAA,KAAA,GACE,YAAa,CAAA;AAAA,MACf,iBAAA;AAAA,MACA,mBAAA;AAAA,MACA,mBAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,cAAA,GAAiB,CAAC,IAA0B,KAAA;AAChD,MAAA,IAAI,CAAC,IAAA;AAAM,QAAO,OAAA,IAAA,CAAA;AAClB,MAAI,IAAA,OAAA,CAAQ,IAAI,CAAG,EAAA;AACjB,QAAA,OAAO,IAAK,CAAA,GAAA,CAAI,CAAC,CAAA,KAAM,KAAM,CAAA,CAAA,EAAG,KAAM,CAAA,MAAM,CAAE,CAAA,MAAA,CAAO,IAAK,CAAA,KAAK,CAAC,CAAA,CAAA;AAAA,OAClE;AACA,MAAA,OAAO,MAAM,IAAM,EAAA,KAAA,CAAM,MAAM,CAAE,CAAA,MAAA,CAAO,KAAK,KAAK,CAAA,CAAA;AAAA,KACpD,CAAA;AAEA,IAAM,MAAA,cAAA,GAAiB,CAAC,IAA0B,KAAA;AAChD,MAAA,IAAI,CAAC,IAAA;AAAM,QAAO,OAAA,IAAA,CAAA;AAClB,MAAI,IAAA,OAAA,CAAQ,IAAI,CAAG,EAAA;AACjB,QAAO,OAAA,IAAA,CAAK,IAAI,CAAC,CAAA,KAAM,EAAE,MAAO,CAAA,KAAA,CAAM,MAAM,CAAC,CAAA,CAAA;AAAA,OAC/C;AACA,MAAO,OAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,MAAM,CAAA,CAAA;AAAA,KACjC,CAAA;AAEA,IAAA,MAAM,kBAAkB,MAAM;AAC5B,MAAI,IAAA,OAAA,CAAQ,YAAY,CAAG,EAAA;AACzB,QAAO,OAAA,YAAA,CAAa,GAAI,CAAA,CAAC,CAAY,KAAA,KAAA,CAAM,CAAC,CAAE,CAAA,MAAA,CAAO,IAAK,CAAA,KAAK,CAAC,CAAA,CAAA;AAAA,OAClE;AACA,MAAA,MAAM,aAAa,KAAM,CAAA,YAAY,CAAE,CAAA,MAAA,CAAO,KAAK,KAAK,CAAA,CAAA;AACxD,MAAA,OAAO,CAAC,UAAY,EAAA,UAAA,CAAW,GAAI,CAAA,EAAA,EAAI,GAAG,CAAC,CAAA,CAAA;AAAA,KAC7C,CAAA;AAEA,IAAA,IAAA,CAAK,mBAAqB,EAAA,CAAC,gBAAkB,EAAA,cAAc,CAAC,CAAA,CAAA;AAC5D,IAAA,IAAA,CAAK,mBAAqB,EAAA,CAAC,gBAAkB,EAAA,cAAc,CAAC,CAAA,CAAA;AAC5D,IAAA,IAAA,CAAK,mBAAqB,EAAA,CAAC,cAAgB,EAAA,YAAY,CAAC,CAAA,CAAA;AACxD,IAAA,IAAA,CAAK,mBAAqB,EAAA,CAAC,oBAAsB,EAAA,aAAa,CAAC,CAAA,CAAA;AAC/D,IAAA,IAAA,CAAK,mBAAqB,EAAA,CAAC,iBAAmB,EAAA,eAAe,CAAC,CAAA,CAAA;AAC9D,IAAA,IAAA,CAAK,mBAAqB,EAAA,CAAC,uBAAyB,EAAA,qBAAqB,CAAC,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}