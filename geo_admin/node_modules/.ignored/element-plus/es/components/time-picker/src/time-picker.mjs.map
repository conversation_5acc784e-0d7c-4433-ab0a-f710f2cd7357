{"version": 3, "file": "time-picker.mjs", "sources": ["../../../../../../packages/components/time-picker/src/time-picker.tsx"], "sourcesContent": ["import { defineComponent, provide, ref } from 'vue'\nimport dayjs from 'dayjs'\nimport customParseFormat from 'dayjs/plugin/customParseFormat.js'\nimport { DEFAULT_FORMATS_TIME } from './constants'\nimport Picker from './common/picker.vue'\nimport TimePickPanel from './time-picker-com/panel-time-pick.vue'\nimport TimeRangePanel from './time-picker-com/panel-time-range.vue'\nimport { timePickerDefaultProps } from './common/props'\ndayjs.extend(customParseFormat)\n\nexport default defineComponent({\n  name: 'ElTimePicker',\n  install: null,\n  props: {\n    ...timePickerDefaultProps,\n    /**\n     * @description whether to pick a time range\n     */\n    isRange: {\n      type: Boolean,\n      default: false,\n    },\n  },\n  emits: ['update:modelValue'],\n  setup(props, ctx) {\n    const commonPicker = ref<InstanceType<typeof Picker>>()\n    const [type, Panel] = props.isRange\n      ? ['timerange', TimeRangePanel]\n      : ['time', TimePickPanel]\n\n    const modelUpdater = (value: any) => ctx.emit('update:modelValue', value)\n    provide('ElPopperOptions', props.popperOptions)\n    ctx.expose({\n      /**\n       * @description focus the Input component\n       */\n      focus: (e: FocusEvent | undefined) => {\n        commonPicker.value?.handleFocusInput(e)\n      },\n      /**\n       * @description blur the Input component\n       */\n      blur: (e: FocusEvent | undefined) => {\n        commonPicker.value?.handleBlurInput(e)\n      },\n      /**\n       * @description open the TimePicker popper\n       */\n      handleOpen: () => {\n        commonPicker.value?.handleOpen()\n      },\n      /**\n       * @description close the TimePicker popper\n       */\n      handleClose: () => {\n        commonPicker.value?.handleClose()\n      },\n    })\n\n    return () => {\n      const format = props.format ?? DEFAULT_FORMATS_TIME\n\n      return (\n        <Picker\n          {...props}\n          ref={commonPicker}\n          type={type}\n          format={format}\n          onUpdate:modelValue={modelUpdater}\n        >\n          {{\n            default: (props: any) => <Panel {...props} />,\n          }}\n        </Picker>\n      )\n    }\n  },\n})\n"], "names": ["dayjs", "extend", "customParseFormat", "defineComponent", "name", "install", "props", "isRange", "type", "default", "setup", "commonPicker", "modelUpdater", "value", "ctx", "emit", "provide", "expose", "handleFocusInput", "blur", "_createVNode", "Picker", "_mergeProps", "handleOpen", "handleClose"], "mappings": ";;;;;;;;;AAQAA,KAAK,CAACC,MAAN,CAAaC,iBAAb,CAAA,CAAA;AAEA,iBAAeC,eAAe,CAAC;AAC7BC,EAAAA,IAAI,EAAE,cADuB;AAE7BC,EAAAA,OAAO,EAAE,IAFoB;EAG7BC,KAAK,EAAE;;AAEL,IAAA,OAAA,EAAA;AACJ,MAAA,IAAA,EAAA,OAAA;AACA,MAAA,OAAA,EAAA,KAAA;AACIC,KAAAA;AACEC,GAAAA;AACAC,EAAAA,KAAAA,EAAAA,CAAAA,mBAAS,CAAA;AAFF,EAAA,KAAA,CAAA,KAAA,EAAA,GAAA,EAAA;IARkB,MAAA,YAAA,GAAA,GAAA,EAAA,CAAA;IAaxB,MAAG,CAAA,IAAA,EAAA,KAAA,CAAA,GAAA,KAbqB,CAAA,OAAA,GAAA,CAAA,WAAA,EAAA,cAAA,CAAA,GAAA,CAAA,MAAA,EAAA,aAAA,CAAA,CAAA;;AAc7BC,IAAAA,OAAMJ,CAAAA,iBAAY,EAAA,KAAA,CAAA,aAAA,CAAA,CAAA;IAChB,GAAMK,CAAAA,MAAAA,CAAAA;AACN,MAAA,KAAOH,EAAD,CAAA,CAAA;;QAIAI,CAAAA,EAAAA,GAAAA,YAAgBC,CAAAA,KAAeC,KAAG,IAACC,GAAK,KAAA,CAAA,GAAA,EAAA,CAAA,gBAA9C,CAAA,CAAA,CAAA,CAAA;;AACAC,MAAAA,IAAAA,EAAQ,CAAD,CAAA,KAAA;QACHC,MAAJ,CAAW;AACT,QAAA,CAAA,EAAA,GAAA,YAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA;AACN,OAAA;AACA,MAAA,UAAA,EAAA,MAAA;QACW,IAAA,EAAE,CAA+B;AACpCN,QAAAA,CAAAA,EAAAA,GAAAA,YAAA,CAAoBO,KAAAA,KAAAA,IAAAA,GAApB,KAAA,CAAA,GAAA,EAAA,CAAA,UAAA,EAAA,CAAA;OALO;;AAOT,QAAA,IAAA,EAAA,CAAA;AACN,QAAA,CAAA,EAAA,GAAA,YAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,WAAA,EAAA,CAAA;AACA,OAAA;MACMC,CAAI;AACFR,IAAAA,OAAAA,MAAAA;MACD,IAZQ,EAAA,CAAA;;AAaT,MAAA,OAAAS,WAAA,CAAAC,YAAA,EAAAC,UAAA,CAAA,KAAA,EAAA;AACN,QAAA,KAAA,EAAA,YAAA;AACA,QAAA,MAAA,EAAA,IAAA;AACMC,QAAAA,QAAU,EAAE,MAAM;QAChBZ,qBAAA,EAAA,YAAA;OAjBO,CAAA,EAAA;;AAmBT,OAAA,CAAA,CAAA;AACN,KAAA,CAAA;AACA,GAAA;AACMa,CAAAA,CAAAA;;;;"}