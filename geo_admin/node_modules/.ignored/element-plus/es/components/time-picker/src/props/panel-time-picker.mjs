import '../../../../utils/index.mjs';
import { timePanelSharedProps } from './shared.mjs';
import { buildProps, definePropType } from '../../../../utils/vue/props/runtime.mjs';

const panelTimePickerProps = buildProps({
  ...timePanelSharedProps,
  datetimeRole: String,
  parsedValue: {
    type: definePropType(Object)
  }
});

export { panelTimePickerProps };
//# sourceMappingURL=panel-time-picker.mjs.map
