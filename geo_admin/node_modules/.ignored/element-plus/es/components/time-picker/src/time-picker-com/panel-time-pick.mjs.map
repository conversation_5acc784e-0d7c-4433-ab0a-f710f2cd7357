{"version": 3, "file": "panel-time-pick.mjs", "sources": ["../../../../../../../packages/components/time-picker/src/time-picker-com/panel-time-pick.vue"], "sourcesContent": ["<template>\n  <transition :name=\"transitionName\">\n    <div v-if=\"actualVisible || visible\" :class=\"ns.b('panel')\">\n      <div :class=\"[ns.be('panel', 'content'), { 'has-seconds': showSeconds }]\">\n        <time-spinner\n          ref=\"spinner\"\n          :role=\"datetimeRole || 'start'\"\n          :arrow-control=\"arrowControl\"\n          :show-seconds=\"showSeconds\"\n          :am-pm-mode=\"amPmMode\"\n          :spinner-date=\"(parsedValue as any)\"\n          :disabled-hours=\"disabledHours\"\n          :disabled-minutes=\"disabledMinutes\"\n          :disabled-seconds=\"disabledSeconds\"\n          @change=\"handleChange\"\n          @set-option=\"onSetOption\"\n          @select-range=\"setSelectionRange\"\n        />\n      </div>\n      <div :class=\"ns.be('panel', 'footer')\">\n        <button\n          type=\"button\"\n          :class=\"[ns.be('panel', 'btn'), 'cancel']\"\n          @click=\"handleCancel\"\n        >\n          {{ t('el.datepicker.cancel') }}\n        </button>\n        <button\n          type=\"button\"\n          :class=\"[ns.be('panel', 'btn'), 'confirm']\"\n          @click=\"handleConfirm()\"\n        >\n          {{ t('el.datepicker.confirm') }}\n        </button>\n      </div>\n    </div>\n  </transition>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, ref } from 'vue'\nimport dayjs from 'dayjs'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { isUndefined } from '@element-plus/utils'\nimport { panelTimePickerProps } from '../props/panel-time-picker'\nimport { useTimePanel } from '../composables/use-time-panel'\nimport {\n  buildAvailableTimeSlotGetter,\n  useOldValue,\n} from '../composables/use-time-picker'\nimport TimeSpinner from './basic-time-spinner.vue'\n\nimport type { Dayjs } from 'dayjs'\n\nconst props = defineProps(panelTimePickerProps)\nconst emit = defineEmits(['pick', 'select-range', 'set-picker-option'])\n\n// Injections\nconst pickerBase = inject('EP_PICKER_BASE') as any\nconst {\n  arrowControl,\n  disabledHours,\n  disabledMinutes,\n  disabledSeconds,\n  defaultValue,\n} = pickerBase.props\nconst { getAvailableHours, getAvailableMinutes, getAvailableSeconds } =\n  buildAvailableTimeSlotGetter(disabledHours, disabledMinutes, disabledSeconds)\n\nconst ns = useNamespace('time')\nconst { t, lang } = useLocale()\n// data\nconst selectionRange = ref([0, 2])\nconst oldValue = useOldValue(props)\n// computed\nconst transitionName = computed(() => {\n  return isUndefined(props.actualVisible)\n    ? `${ns.namespace.value}-zoom-in-top`\n    : ''\n})\nconst showSeconds = computed(() => {\n  return props.format.includes('ss')\n})\nconst amPmMode = computed(() => {\n  if (props.format.includes('A')) return 'A'\n  if (props.format.includes('a')) return 'a'\n  return ''\n})\n// method\nconst isValidValue = (_date: Dayjs) => {\n  const parsedDate = dayjs(_date).locale(lang.value)\n  const result = getRangeAvailableTime(parsedDate)\n  return parsedDate.isSame(result)\n}\nconst handleCancel = () => {\n  emit('pick', oldValue.value, false)\n}\nconst handleConfirm = (visible = false, first = false) => {\n  if (first) return\n  emit('pick', props.parsedValue, visible)\n}\nconst handleChange = (_date: Dayjs) => {\n  // visible avoids edge cases, when use scrolls during panel closing animation\n  if (!props.visible) {\n    return\n  }\n  const result = getRangeAvailableTime(_date).millisecond(0)\n  emit('pick', result, true)\n}\n\nconst setSelectionRange = (start: number, end: number) => {\n  emit('select-range', start, end)\n  selectionRange.value = [start, end]\n}\n\nconst changeSelectionRange = (step: number) => {\n  const list = [0, 3].concat(showSeconds.value ? [6] : [])\n  const mapping = ['hours', 'minutes'].concat(\n    showSeconds.value ? ['seconds'] : []\n  )\n  const index = list.indexOf(selectionRange.value[0])\n  const next = (index + step + list.length) % list.length\n  timePickerOptions['start_emitSelectRange'](mapping[next])\n}\n\nconst handleKeydown = (event: KeyboardEvent) => {\n  const code = event.code\n\n  const { left, right, up, down } = EVENT_CODE\n\n  if ([left, right].includes(code)) {\n    const step = code === left ? -1 : 1\n    changeSelectionRange(step)\n    event.preventDefault()\n    return\n  }\n\n  if ([up, down].includes(code)) {\n    const step = code === up ? -1 : 1\n    timePickerOptions['start_scrollDown'](step)\n    event.preventDefault()\n    return\n  }\n}\n\nconst { timePickerOptions, onSetOption, getAvailableTime } = useTimePanel({\n  getAvailableHours,\n  getAvailableMinutes,\n  getAvailableSeconds,\n})\n\nconst getRangeAvailableTime = (date: Dayjs) => {\n  return getAvailableTime(date, props.datetimeRole || '', true)\n}\n\nconst parseUserInput = (value: Dayjs) => {\n  if (!value) return null\n  return dayjs(value, props.format).locale(lang.value)\n}\n\nconst formatToString = (value: Dayjs) => {\n  if (!value) return null\n  return value.format(props.format)\n}\n\nconst getDefaultValue = () => {\n  return dayjs(defaultValue).locale(lang.value)\n}\n\nemit('set-picker-option', ['isValidValue', isValidValue])\nemit('set-picker-option', ['formatToString', formatToString])\nemit('set-picker-option', ['parseUserInput', parseUserInput])\nemit('set-picker-option', ['handleKeydownInput', handleKeydown])\nemit('set-picker-option', ['getRangeAvailableTime', getRangeAvailableTime])\nemit('set-picker-option', ['getDefaultValue', getDefaultValue])\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AA2DA,IAAM,MAAA,UAAA,GAAa,OAAO,gBAAgB,CAAA,CAAA;AAC1C,IAAM,MAAA;AAAA,MACJ,YAAA;AAAA,MACA,aAAA;AAAA,MACA,eAAA;AAAA,MACA,eAAA;AAAA,MACA,YAAA;AAAA,KAAA,GACE,UAAW,CAAA,KAAA,CAAA;AACf,IAAA,MAAM,EAAE,iBAAmB,EAAA,mBAAA,EAAqB,wBAC9C,4BAA6B,CAAA,aAAA,EAAe,iBAAiB,eAAe,CAAA,CAAA;AAE9E,IAAM,MAAA,EAAA,GAAK,aAAa,MAAM,CAAA,CAAA;AAC9B,IAAM,MAAA,EAAE,CAAG,EAAA,IAAA,EAAA,GAAS,SAAU,EAAA,CAAA;AAE9B,IAAA,MAAM,cAAiB,GAAA,GAAA,CAAI,CAAC,CAAA,EAAG,CAAC,CAAC,CAAA,CAAA;AACjC,IAAM,MAAA,QAAA,GAAW,YAAY,KAAK,CAAA,CAAA;AAElC,IAAM,MAAA,cAAA,GAAiB,SAAS,MAAM;AACpC,MAAA,OAAO,YAAY,KAAM,CAAA,aAAa,IAClC,CAAG,EAAA,EAAA,CAAG,UAAU,KAChB,CAAA,YAAA,CAAA,GAAA,EAAA,CAAA;AAAA,KACL,CAAA,CAAA;AACD,IAAM,MAAA,WAAA,GAAc,SAAS,MAAM;AACjC,MAAO,OAAA,KAAA,CAAM,MAAO,CAAA,QAAA,CAAS,IAAI,CAAA,CAAA;AAAA,KAClC,CAAA,CAAA;AACD,IAAM,MAAA,QAAA,GAAW,SAAS,MAAM;AAC9B,MAAI,IAAA,KAAA,CAAM,MAAO,CAAA,QAAA,CAAS,GAAG,CAAA;AAAG,QAAO,OAAA,GAAA,CAAA;AACvC,MAAI,IAAA,KAAA,CAAM,MAAO,CAAA,QAAA,CAAS,GAAG,CAAA;AAAG,QAAO,OAAA,GAAA,CAAA;AACvC,MAAO,OAAA,EAAA,CAAA;AAAA,KACR,CAAA,CAAA;AAED,IAAM,MAAA,YAAA,GAAe,CAAC,KAAiB,KAAA;AACrC,MAAA,MAAM,aAAa,KAAM,CAAA,KAAK,CAAE,CAAA,MAAA,CAAO,KAAK,KAAK,CAAA,CAAA;AACjD,MAAM,MAAA,MAAA,GAAS,sBAAsB,UAAU,CAAA,CAAA;AAC/C,MAAO,OAAA,UAAA,CAAW,OAAO,MAAM,CAAA,CAAA;AAAA,KACjC,CAAA;AACA,IAAA,MAAM,eAAe,MAAM;AACzB,MAAK,IAAA,CAAA,MAAA,EAAQ,QAAS,CAAA,KAAA,EAAO,KAAK,CAAA,CAAA;AAAA,KACpC,CAAA;AACA,IAAA,MAAM,aAAgB,GAAA,CAAC,OAAU,GAAA,KAAA,EAAO,QAAQ,KAAU,KAAA;AACxD,MAAI,IAAA,KAAA;AAAO,QAAA,OAAA;AACX,MAAK,IAAA,CAAA,MAAA,EAAQ,KAAM,CAAA,WAAA,EAAa,OAAO,CAAA,CAAA;AAAA,KACzC,CAAA;AACA,IAAM,MAAA,YAAA,GAAe,CAAC,KAAiB,KAAA;AAErC,MAAI,IAAA,CAAC,MAAM,OAAS,EAAA;AAClB,QAAA,OAAA;AAAA,OACF;AACA,MAAA,MAAM,MAAS,GAAA,qBAAA,CAAsB,KAAK,CAAA,CAAE,YAAY,CAAC,CAAA,CAAA;AACzD,MAAK,IAAA,CAAA,MAAA,EAAQ,QAAQ,IAAI,CAAA,CAAA;AAAA,KAC3B,CAAA;AAEA,IAAM,MAAA,iBAAA,GAAoB,CAAC,KAAA,EAAe,GAAgB,KAAA;AACxD,MAAK,IAAA,CAAA,cAAA,EAAgB,OAAO,GAAG,CAAA,CAAA;AAC/B,MAAe,cAAA,CAAA,KAAA,GAAQ,CAAC,KAAA,EAAO,GAAG,CAAA,CAAA;AAAA,KACpC,CAAA;AAEA,IAAM,MAAA,oBAAA,GAAuB,CAAC,IAAiB,KAAA;AAC7C,MAAA,MAAM,IAAO,GAAA,CAAC,CAAG,EAAA,CAAC,CAAE,CAAA,MAAA,CAAO,WAAY,CAAA,KAAA,GAAQ,CAAC,CAAC,CAAI,GAAA,EAAE,CAAA,CAAA;AACvD,MAAA,MAAM,OAAU,GAAA,CAAC,OAAS,EAAA,SAAS,CAAE,CAAA,MAAA,CACnC,WAAY,CAAA,KAAA,GAAQ,CAAC,SAAS,CAAI,GAAA,EACpC,CAAA,CAAA;AACA,MAAA,MAAM,KAAQ,GAAA,IAAA,CAAK,OAAQ,CAAA,cAAA,CAAe,MAAM,CAAE,CAAA,CAAA,CAAA;AAClD,MAAA,MAAM,IAAQ,GAAA,CAAA,KAAA,GAAQ,IAAO,GAAA,IAAA,CAAK,UAAU,IAAK,CAAA,MAAA,CAAA;AACjD,MAAkB,iBAAA,CAAA,uBAAA,CAAA,CAAyB,QAAQ,IAAK,CAAA,CAAA,CAAA;AAAA,KAC1D,CAAA;AAEA,IAAM,MAAA,aAAA,GAAgB,CAAC,KAAyB,KAAA;AAC9C,MAAA,MAAM,OAAO,KAAM,CAAA,IAAA,CAAA;AAEnB,MAAA,MAAM,EAAE,IAAA,EAAM,KAAO,EAAA,EAAA,EAAI,IAAS,EAAA,GAAA,UAAA,CAAA;AAElC,MAAA,IAAI,CAAC,IAAM,EAAA,KAAK,CAAE,CAAA,QAAA,CAAS,IAAI,CAAG,EAAA;AAChC,QAAM,MAAA,IAAA,GAAO,IAAS,KAAA,IAAA,GAAO,CAAK,CAAA,GAAA,CAAA,CAAA;AAClC,QAAA,oBAAA,CAAqB,IAAI,CAAA,CAAA;AACzB,QAAA,KAAA,CAAM,cAAe,EAAA,CAAA;AACrB,QAAA,OAAA;AAAA,OACF;AAEA,MAAA,IAAI,CAAC,EAAI,EAAA,IAAI,CAAE,CAAA,QAAA,CAAS,IAAI,CAAG,EAAA;AAC7B,QAAM,MAAA,IAAA,GAAO,IAAS,KAAA,EAAA,GAAK,CAAK,CAAA,GAAA,CAAA,CAAA;AAChC,QAAA,iBAAA,CAAkB,oBAAoB,IAAI,CAAA,CAAA;AAC1C,QAAA,KAAA,CAAM,cAAe,EAAA,CAAA;AACrB,QAAA,OAAA;AAAA,OACF;AAAA,KACF,CAAA;AAEA,IAAA,MAAM,EAAE,iBAAA,EAAmB,WAAa,EAAA,gBAAA,EAAA,GAAqB,YAAa,CAAA;AAAA,MACxE,iBAAA;AAAA,MACA,mBAAA;AAAA,MACA,mBAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,qBAAA,GAAwB,CAAC,IAAgB,KAAA;AAC7C,MAAA,OAAO,gBAAiB,CAAA,IAAA,EAAM,KAAM,CAAA,YAAA,IAAgB,IAAI,IAAI,CAAA,CAAA;AAAA,KAC9D,CAAA;AAEA,IAAM,MAAA,cAAA,GAAiB,CAAC,KAAiB,KAAA;AACvC,MAAA,IAAI,CAAC,KAAA;AAAO,QAAO,OAAA,IAAA,CAAA;AACnB,MAAA,OAAO,MAAM,KAAO,EAAA,KAAA,CAAM,MAAM,CAAE,CAAA,MAAA,CAAO,KAAK,KAAK,CAAA,CAAA;AAAA,KACrD,CAAA;AAEA,IAAM,MAAA,cAAA,GAAiB,CAAC,KAAiB,KAAA;AACvC,MAAA,IAAI,CAAC,KAAA;AAAO,QAAO,OAAA,IAAA,CAAA;AACnB,MAAO,OAAA,KAAA,CAAM,MAAO,CAAA,KAAA,CAAM,MAAM,CAAA,CAAA;AAAA,KAClC,CAAA;AAEA,IAAA,MAAM,kBAAkB,MAAM;AAC5B,MAAA,OAAO,KAAM,CAAA,YAAY,CAAE,CAAA,MAAA,CAAO,KAAK,KAAK,CAAA,CAAA;AAAA,KAC9C,CAAA;AAEA,IAAA,IAAA,CAAK,mBAAqB,EAAA,CAAC,cAAgB,EAAA,YAAY,CAAC,CAAA,CAAA;AACxD,IAAA,IAAA,CAAK,mBAAqB,EAAA,CAAC,gBAAkB,EAAA,cAAc,CAAC,CAAA,CAAA;AAC5D,IAAA,IAAA,CAAK,mBAAqB,EAAA,CAAC,gBAAkB,EAAA,cAAc,CAAC,CAAA,CAAA;AAC5D,IAAA,IAAA,CAAK,mBAAqB,EAAA,CAAC,oBAAsB,EAAA,aAAa,CAAC,CAAA,CAAA;AAC/D,IAAA,IAAA,CAAK,mBAAqB,EAAA,CAAC,uBAAyB,EAAA,qBAAqB,CAAC,CAAA,CAAA;AAC1E,IAAA,IAAA,CAAK,mBAAqB,EAAA,CAAC,iBAAmB,EAAA,eAAe,CAAC,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}