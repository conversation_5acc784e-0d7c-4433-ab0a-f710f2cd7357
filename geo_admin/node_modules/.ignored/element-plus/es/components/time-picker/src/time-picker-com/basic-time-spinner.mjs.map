{"version": 3, "file": "basic-time-spinner.mjs", "sources": ["../../../../../../../packages/components/time-picker/src/time-picker-com/basic-time-spinner.vue"], "sourcesContent": ["<template>\n  <div :class=\"[ns.b('spinner'), { 'has-seconds': showSeconds }]\">\n    <template v-if=\"!arrowControl\">\n      <el-scrollbar\n        v-for=\"item in spinnerItems\"\n        :key=\"item\"\n        :ref=\"(scrollbar: unknown) => setRef(scrollbar as any, item)\"\n        :class=\"ns.be('spinner', 'wrapper')\"\n        wrap-style=\"max-height: inherit;\"\n        :view-class=\"ns.be('spinner', 'list')\"\n        noresize\n        tag=\"ul\"\n        @mouseenter=\"emitSelectRange(item)\"\n        @mousemove=\"adjustCurrentSpinner(item)\"\n      >\n        <li\n          v-for=\"(disabled, key) in timeList[item]\"\n          :key=\"key\"\n          :class=\"[\n            ns.be('spinner', 'item'),\n            ns.is('active', key === timePartials[item]),\n            ns.is('disabled', disabled),\n          ]\"\n          @click=\"handleClick(item, { value: key, disabled })\"\n        >\n          <template v-if=\"item === 'hours'\">\n            {{ ('0' + (amPmMode ? key % 12 || 12 : key)).slice(-2)\n            }}{{ getAmPmFlag(key) }}\n          </template>\n          <template v-else>\n            {{ ('0' + key).slice(-2) }}\n          </template>\n        </li>\n      </el-scrollbar>\n    </template>\n    <template v-if=\"arrowControl\">\n      <div\n        v-for=\"item in spinnerItems\"\n        :key=\"item\"\n        :class=\"[ns.be('spinner', 'wrapper'), ns.is('arrow')]\"\n        @mouseenter=\"emitSelectRange(item)\"\n      >\n        <el-icon\n          v-repeat-click=\"onDecrement\"\n          :class=\"['arrow-up', ns.be('spinner', 'arrow')]\"\n        >\n          <arrow-up />\n        </el-icon>\n        <el-icon\n          v-repeat-click=\"onIncrement\"\n          :class=\"['arrow-down', ns.be('spinner', 'arrow')]\"\n        >\n          <arrow-down />\n        </el-icon>\n        <ul :class=\"ns.be('spinner', 'list')\">\n          <li\n            v-for=\"(time, key) in arrowControlTimeList[item]\"\n            :key=\"key\"\n            :class=\"[\n              ns.be('spinner', 'item'),\n              ns.is('active', time === timePartials[item]),\n              ns.is('disabled', timeList[item][time!]),\n            ]\"\n          >\n            <template v-if=\"typeof time === 'number'\">\n              <template v-if=\"item === 'hours'\">\n                {{ ('0' + (amPmMode ? time % 12 || 12 : time)).slice(-2)\n                }}{{ getAmPmFlag(time) }}\n              </template>\n              <template v-else>\n                {{ ('0' + time).slice(-2) }}\n              </template>\n            </template>\n          </li>\n        </ul>\n      </div>\n    </template>\n  </div>\n</template>\n<script lang=\"ts\" setup>\nimport { computed, nextTick, onMounted, ref, unref, watch } from 'vue'\nimport { debounce } from 'lodash-unified'\nimport { vRepeatClick } from '@element-plus/directives'\nimport ElScrollbar from '@element-plus/components/scrollbar'\nimport ElIcon from '@element-plus/components/icon'\nimport { ArrowDown, ArrowUp } from '@element-plus/icons-vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { getStyle } from '@element-plus/utils'\nimport { timeUnits } from '../constants'\nimport { buildTimeList } from '../utils'\nimport { basicTimeSpinnerProps } from '../props/basic-time-spinner'\nimport { getTimeLists } from '../composables/use-time-picker'\n\nimport type { Ref } from 'vue'\nimport type { ScrollbarInstance } from '@element-plus/components/scrollbar'\nimport type { TimeUnit } from '../constants'\nimport type { TimeList } from '../utils'\n\nconst props = defineProps(basicTimeSpinnerProps)\nconst emit = defineEmits(['change', 'select-range', 'set-option'])\n\nconst ns = useNamespace('time')\n\nconst { getHoursList, getMinutesList, getSecondsList } = getTimeLists(\n  props.disabledHours,\n  props.disabledMinutes,\n  props.disabledSeconds\n)\n\n// data\nlet isScrolling = false\n\nconst currentScrollbar = ref<TimeUnit>()\nconst listHoursRef = ref<ScrollbarInstance>()\nconst listMinutesRef = ref<ScrollbarInstance>()\nconst listSecondsRef = ref<ScrollbarInstance>()\nconst listRefsMap: Record<TimeUnit, Ref<ScrollbarInstance | undefined>> = {\n  hours: listHoursRef,\n  minutes: listMinutesRef,\n  seconds: listSecondsRef,\n}\n\n// computed\nconst spinnerItems = computed(() => {\n  return props.showSeconds ? timeUnits : timeUnits.slice(0, 2)\n})\n\nconst timePartials = computed<Record<TimeUnit, number>>(() => {\n  const { spinnerDate } = props\n  const hours = spinnerDate.hour()\n  const minutes = spinnerDate.minute()\n  const seconds = spinnerDate.second()\n  return { hours, minutes, seconds }\n})\n\nconst timeList = computed(() => {\n  const { hours, minutes } = unref(timePartials)\n  return {\n    hours: getHoursList(props.role),\n    minutes: getMinutesList(hours, props.role),\n    seconds: getSecondsList(hours, minutes, props.role),\n  }\n})\n\nconst arrowControlTimeList = computed<Record<TimeUnit, TimeList>>(() => {\n  const { hours, minutes, seconds } = unref(timePartials)\n\n  return {\n    hours: buildTimeList(hours, 23),\n    minutes: buildTimeList(minutes, 59),\n    seconds: buildTimeList(seconds, 59),\n  }\n})\n\nconst debouncedResetScroll = debounce((type) => {\n  isScrolling = false\n  adjustCurrentSpinner(type)\n}, 200)\n\nconst getAmPmFlag = (hour: number) => {\n  const shouldShowAmPm = !!props.amPmMode\n  if (!shouldShowAmPm) return ''\n  const isCapital = props.amPmMode === 'A'\n  // todo locale\n  let content = hour < 12 ? ' am' : ' pm'\n  if (isCapital) content = content.toUpperCase()\n  return content\n}\n\nconst emitSelectRange = (type: TimeUnit) => {\n  let range\n\n  switch (type) {\n    case 'hours':\n      range = [0, 2]\n      break\n    case 'minutes':\n      range = [3, 5]\n      break\n    case 'seconds':\n      range = [6, 8]\n      break\n  }\n  const [left, right] = range\n\n  emit('select-range', left, right)\n  currentScrollbar.value = type\n}\n\nconst adjustCurrentSpinner = (type: TimeUnit) => {\n  adjustSpinner(type, unref(timePartials)[type])\n}\n\nconst adjustSpinners = () => {\n  adjustCurrentSpinner('hours')\n  adjustCurrentSpinner('minutes')\n  adjustCurrentSpinner('seconds')\n}\n\nconst getScrollbarElement = (el: HTMLElement) =>\n  el.querySelector(`.${ns.namespace.value}-scrollbar__wrap`) as HTMLElement\n\nconst adjustSpinner = (type: TimeUnit, value: number) => {\n  if (props.arrowControl) return\n  const scrollbar = unref(listRefsMap[type])\n  if (scrollbar && scrollbar.$el) {\n    getScrollbarElement(scrollbar.$el).scrollTop = Math.max(\n      0,\n      value * typeItemHeight(type)\n    )\n  }\n}\n\nconst typeItemHeight = (type: TimeUnit): number => {\n  const scrollbar = unref(listRefsMap[type])\n  const listItem = scrollbar?.$el.querySelector('li')\n  if (listItem) {\n    return Number.parseFloat(getStyle(listItem, 'height')) || 0\n  }\n  return 0\n}\n\nconst onIncrement = () => {\n  scrollDown(1)\n}\n\nconst onDecrement = () => {\n  scrollDown(-1)\n}\n\nconst scrollDown = (step: number) => {\n  if (!currentScrollbar.value) {\n    emitSelectRange('hours')\n  }\n\n  const label = currentScrollbar.value!\n  const now = unref(timePartials)[label]\n  const total = currentScrollbar.value === 'hours' ? 24 : 60\n  const next = findNextUnDisabled(label, now, step, total)\n\n  modifyDateField(label, next)\n  adjustSpinner(label, next)\n  nextTick(() => emitSelectRange(label))\n}\n\nconst findNextUnDisabled = (\n  type: TimeUnit,\n  now: number,\n  step: number,\n  total: number\n) => {\n  let next = (now + step + total) % total\n  const list = unref(timeList)[type]\n  while (list[next] && next !== now) {\n    next = (next + step + total) % total\n  }\n  return next\n}\n\nconst modifyDateField = (type: TimeUnit, value: number) => {\n  const list = unref(timeList)[type]\n  const isDisabled = list[value]\n  if (isDisabled) return\n\n  const { hours, minutes, seconds } = unref(timePartials)\n\n  let changeTo\n  switch (type) {\n    case 'hours':\n      changeTo = props.spinnerDate.hour(value).minute(minutes).second(seconds)\n      break\n    case 'minutes':\n      changeTo = props.spinnerDate.hour(hours).minute(value).second(seconds)\n      break\n    case 'seconds':\n      changeTo = props.spinnerDate.hour(hours).minute(minutes).second(value)\n      break\n  }\n  emit('change', changeTo)\n}\n\nconst handleClick = (\n  type: TimeUnit,\n  { value, disabled }: { value: number; disabled: boolean }\n) => {\n  if (!disabled) {\n    modifyDateField(type, value)\n    emitSelectRange(type)\n    adjustSpinner(type, value)\n  }\n}\n\nconst handleScroll = (type: TimeUnit) => {\n  isScrolling = true\n  debouncedResetScroll(type)\n  const value = Math.min(\n    Math.round(\n      (getScrollbarElement(unref(listRefsMap[type])!.$el).scrollTop -\n        (scrollBarHeight(type) * 0.5 - 10) / typeItemHeight(type) +\n        3) /\n        typeItemHeight(type)\n    ),\n    type === 'hours' ? 23 : 59\n  )\n  modifyDateField(type, value)\n}\n\nconst scrollBarHeight = (type: TimeUnit) => {\n  return unref(listRefsMap[type])!.$el.offsetHeight\n}\n\nconst bindScrollEvent = () => {\n  const bindFunction = (type: TimeUnit) => {\n    const scrollbar = unref(listRefsMap[type])\n    if (scrollbar && scrollbar.$el) {\n      getScrollbarElement(scrollbar.$el).onscroll = () => {\n        // TODO: scroll is emitted when set scrollTop programmatically\n        // should find better solutions in the future!\n        handleScroll(type)\n      }\n    }\n  }\n  bindFunction('hours')\n  bindFunction('minutes')\n  bindFunction('seconds')\n}\n\nonMounted(() => {\n  nextTick(() => {\n    !props.arrowControl && bindScrollEvent()\n    adjustSpinners()\n    // set selection on the first hour part\n    if (props.role === 'start') emitSelectRange('hours')\n  })\n})\n\nconst setRef = (scrollbar: ScrollbarInstance, type: TimeUnit) => {\n  listRefsMap[type].value = scrollbar\n}\n\nemit('set-option', [`${props.role}_scrollDown`, scrollDown])\nemit('set-option', [`${props.role}_emitSelectRange`, emitSelectRange])\n\nwatch(\n  () => props.spinnerDate,\n  () => {\n    if (isScrolling) return\n    adjustSpinners()\n  }\n)\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAqGA,IAAM,MAAA,EAAA,GAAK,aAAa,MAAM,CAAA,CAAA;AAE9B,IAAM,MAAA,EAAE,YAAc,EAAA,cAAA,EAAgB,cAAmB,EAAA,GAAA,YAAA,CACvD,MAAM,aACN,EAAA,KAAA,CAAM,eACN,EAAA,KAAA,CAAM,eACR,CAAA,CAAA;AAGA,IAAA,IAAI,WAAc,GAAA,KAAA,CAAA;AAElB,IAAA,MAAM,mBAAmB,GAAc,EAAA,CAAA;AACvC,IAAA,MAAM,eAAe,GAAuB,EAAA,CAAA;AAC5C,IAAA,MAAM,iBAAiB,GAAuB,EAAA,CAAA;AAC9C,IAAA,MAAM,iBAAiB,GAAuB,EAAA,CAAA;AAC9C,IAAA,MAAM,WAAoE,GAAA;AAAA,MACxE,KAAO,EAAA,YAAA;AAAA,MACP,OAAS,EAAA,cAAA;AAAA,MACT,OAAS,EAAA,cAAA;AAAA,KACX,CAAA;AAGA,IAAM,MAAA,YAAA,GAAe,SAAS,MAAM;AAClC,MAAA,OAAO,MAAM,WAAc,GAAA,SAAA,GAAY,SAAU,CAAA,KAAA,CAAM,GAAG,CAAC,CAAA,CAAA;AAAA,KAC5D,CAAA,CAAA;AAED,IAAM,MAAA,YAAA,GAAe,SAAmC,MAAM;AAC5D,MAAA,MAAM,EAAE,WAAgB,EAAA,GAAA,KAAA,CAAA;AACxB,MAAM,MAAA,KAAA,GAAQ,YAAY,IAAK,EAAA,CAAA;AAC/B,MAAM,MAAA,OAAA,GAAU,YAAY,MAAO,EAAA,CAAA;AACnC,MAAM,MAAA,OAAA,GAAU,YAAY,MAAO,EAAA,CAAA;AACnC,MAAO,OAAA,EAAE,KAAO,EAAA,OAAA,EAAS,OAAQ,EAAA,CAAA;AAAA,KAClC,CAAA,CAAA;AAED,IAAM,MAAA,QAAA,GAAW,SAAS,MAAM;AAC9B,MAAA,MAAM,EAAE,KAAA,EAAO,OAAY,EAAA,GAAA,KAAA,CAAM,YAAY,CAAA,CAAA;AAC7C,MAAO,OAAA;AAAA,QACL,KAAA,EAAO,YAAa,CAAA,KAAA,CAAM,IAAI,CAAA;AAAA,QAC9B,OAAS,EAAA,cAAA,CAAe,KAAO,EAAA,KAAA,CAAM,IAAI,CAAA;AAAA,QACzC,OAAS,EAAA,cAAA,CAAe,KAAO,EAAA,OAAA,EAAS,MAAM,IAAI,CAAA;AAAA,OACpD,CAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,oBAAA,GAAuB,SAAqC,MAAM;AACtE,MAAA,MAAM,EAAE,KAAA,EAAO,OAAS,EAAA,OAAA,EAAA,GAAY,MAAM,YAAY,CAAA,CAAA;AAEtD,MAAO,OAAA;AAAA,QACL,KAAA,EAAO,aAAc,CAAA,KAAA,EAAO,EAAE,CAAA;AAAA,QAC9B,OAAA,EAAS,aAAc,CAAA,OAAA,EAAS,EAAE,CAAA;AAAA,QAClC,OAAA,EAAS,aAAc,CAAA,OAAA,EAAS,EAAE,CAAA;AAAA,OACpC,CAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,oBAAA,GAAuB,QAAS,CAAA,CAAC,IAAS,KAAA;AAC9C,MAAc,WAAA,GAAA,KAAA,CAAA;AACd,MAAA,oBAAA,CAAqB,IAAI,CAAA,CAAA;AAAA,OACxB,GAAG,CAAA,CAAA;AAEN,IAAM,MAAA,WAAA,GAAc,CAAC,IAAiB,KAAA;AACpC,MAAM,MAAA,cAAA,GAAiB,CAAC,CAAC,KAAM,CAAA,QAAA,CAAA;AAC/B,MAAA,IAAI,CAAC,cAAA;AAAgB,QAAO,OAAA,EAAA,CAAA;AAC5B,MAAM,MAAA,SAAA,GAAY,MAAM,QAAa,KAAA,GAAA,CAAA;AAErC,MAAI,IAAA,OAAA,GAAU,IAAO,GAAA,EAAA,GAAK,KAAQ,GAAA,KAAA,CAAA;AAClC,MAAI,IAAA,SAAA;AAAW,QAAA,OAAA,GAAU,QAAQ,WAAY,EAAA,CAAA;AAC7C,MAAO,OAAA,OAAA,CAAA;AAAA,KACT,CAAA;AAEA,IAAM,MAAA,eAAA,GAAkB,CAAC,IAAmB,KAAA;AAC1C,MAAI,IAAA,KAAA,CAAA;AAEJ,MAAQ,QAAA,IAAA;AAAA,QACD,KAAA,OAAA;AACH,UAAQ,KAAA,GAAA,CAAC,GAAG,CAAC,CAAA,CAAA;AACb,UAAA,MAAA;AAAA,QACG,KAAA,SAAA;AACH,UAAQ,KAAA,GAAA,CAAC,GAAG,CAAC,CAAA,CAAA;AACb,UAAA,MAAA;AAAA,QACG,KAAA,SAAA;AACH,UAAQ,KAAA,GAAA,CAAC,GAAG,CAAC,CAAA,CAAA;AACb,UAAA,MAAA;AAAA,OAAA;AAEJ,MAAM,MAAA,CAAC,MAAM,KAAS,CAAA,GAAA,KAAA,CAAA;AAEtB,MAAK,IAAA,CAAA,cAAA,EAAgB,MAAM,KAAK,CAAA,CAAA;AAChC,MAAA,gBAAA,CAAiB,KAAQ,GAAA,IAAA,CAAA;AAAA,KAC3B,CAAA;AAEA,IAAM,MAAA,oBAAA,GAAuB,CAAC,IAAmB,KAAA;AAC/C,MAAA,aAAA,CAAc,IAAM,EAAA,KAAA,CAAM,YAAY,CAAA,CAAE,IAAK,CAAA,CAAA,CAAA;AAAA,KAC/C,CAAA;AAEA,IAAA,MAAM,iBAAiB,MAAM;AAC3B,MAAA,oBAAA,CAAqB,OAAO,CAAA,CAAA;AAC5B,MAAA,oBAAA,CAAqB,SAAS,CAAA,CAAA;AAC9B,MAAA,oBAAA,CAAqB,SAAS,CAAA,CAAA;AAAA,KAChC,CAAA;AAEA,IAAM,MAAA,mBAAA,GAAsB,CAAC,EAC3B,KAAA,EAAA,CAAG,cAAc,CAAI,CAAA,EAAA,EAAA,CAAG,UAAU,KAAuB,CAAA,gBAAA,CAAA,CAAA,CAAA;AAE3D,IAAM,MAAA,aAAA,GAAgB,CAAC,IAAA,EAAgB,KAAkB,KAAA;AACvD,MAAA,IAAI,KAAM,CAAA,YAAA;AAAc,QAAA,OAAA;AACxB,MAAM,MAAA,SAAA,GAAY,KAAM,CAAA,WAAA,CAAY,IAAK,CAAA,CAAA,CAAA;AACzC,MAAI,IAAA,SAAA,IAAa,UAAU,GAAK,EAAA;AAC9B,QAAoB,mBAAA,CAAA,SAAA,CAAU,GAAG,CAAA,CAAE,SAAY,GAAA,IAAA,CAAK,IAClD,CACA,EAAA,KAAA,GAAQ,cAAe,CAAA,IAAI,CAC7B,CAAA,CAAA;AAAA,OACF;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,cAAA,GAAiB,CAAC,IAA2B,KAAA;AACjD,MAAM,MAAA,SAAA,GAAY,KAAM,CAAA,WAAA,CAAY,IAAK,CAAA,CAAA,CAAA;AACzC,MAAA,MAAM,QAAW,GAAA,SAAA,IAAe,IAAA,GAAA,KAAA,CAAA,GAAc,SAAI,CAAA,GAAA,CAAA,aAAA,CAAA,IAAA,CAAA,CAAA;AAClD,MAAA,IAAI,QAAU,EAAA;AACZ,QAAA,OAAO,OAAO,UAAW,CAAA,QAAA,CAAS,QAAU,EAAA,QAAQ,CAAC,CAAK,IAAA,CAAA,CAAA;AAAA,OAC5D;AACA,MAAO,OAAA,CAAA,CAAA;AAAA,KACT,CAAA;AAEA,IAAA,MAAM,cAAc,MAAM;AACxB,MAAA,UAAA,CAAW,CAAC,CAAA,CAAA;AAAA,KACd,CAAA;AAEA,IAAA,MAAM,cAAc,MAAM;AACxB,MAAA,UAAA,CAAW,CAAE,CAAA,CAAA,CAAA;AAAA,KACf,CAAA;AAEA,IAAM,MAAA,UAAA,GAAa,CAAC,IAAiB,KAAA;AACnC,MAAI,IAAA,CAAC,iBAAiB,KAAO,EAAA;AAC3B,QAAA,eAAA,CAAgB,OAAO,CAAA,CAAA;AAAA,OACzB;AAEA,MAAA,MAAM,QAAQ,gBAAiB,CAAA,KAAA,CAAA;AAC/B,MAAM,MAAA,GAAA,GAAM,KAAM,CAAA,YAAY,CAAE,CAAA,KAAA,CAAA,CAAA;AAChC,MAAA,MAAM,KAAQ,GAAA,gBAAA,CAAiB,KAAU,KAAA,OAAA,GAAU,EAAK,GAAA,EAAA,CAAA;AACxD,MAAA,MAAM,IAAO,GAAA,kBAAA,CAAmB,KAAO,EAAA,GAAA,EAAK,MAAM,KAAK,CAAA,CAAA;AAEvD,MAAA,eAAA,CAAgB,OAAO,IAAI,CAAA,CAAA;AAC3B,MAAA,aAAA,CAAc,OAAO,IAAI,CAAA,CAAA;AACzB,MAAS,QAAA,CAAA,MAAM,eAAgB,CAAA,KAAK,CAAC,CAAA,CAAA;AAAA,KACvC,CAAA;AAEA,IAAA,MAAM,kBAAqB,GAAA,CACzB,IACA,EAAA,GAAA,EACA,MACA,KACG,KAAA;AACH,MAAI,IAAA,IAAA,GAAQ,CAAM,GAAA,GAAA,IAAA,GAAO,KAAS,IAAA,KAAA,CAAA;AAClC,MAAM,MAAA,IAAA,GAAO,KAAM,CAAA,QAAQ,CAAE,CAAA,IAAA,CAAA,CAAA;AAC7B,MAAO,OAAA,IAAA,CAAK,IAAS,CAAA,IAAA,IAAA,KAAS,GAAK,EAAA;AACjC,QAAQ,IAAA,GAAA,CAAA,IAAA,GAAO,OAAO,KAAS,IAAA,KAAA,CAAA;AAAA,OACjC;AACA,MAAO,OAAA,IAAA,CAAA;AAAA,KACT,CAAA;AAEA,IAAM,MAAA,eAAA,GAAkB,CAAC,IAAA,EAAgB,KAAkB,KAAA;AACzD,MAAM,MAAA,IAAA,GAAO,KAAM,CAAA,QAAQ,CAAE,CAAA,IAAA,CAAA,CAAA;AAC7B,MAAA,MAAM,aAAa,IAAK,CAAA,KAAA,CAAA,CAAA;AACxB,MAAI,IAAA,UAAA;AAAY,QAAA,OAAA;AAEhB,MAAA,MAAM,EAAE,KAAA,EAAO,OAAS,EAAA,OAAA,EAAA,GAAY,MAAM,YAAY,CAAA,CAAA;AAEtD,MAAI,IAAA,QAAA,CAAA;AACJ,MAAQ,QAAA,IAAA;AAAA,QACD,KAAA,OAAA;AACH,UAAW,QAAA,GAAA,KAAA,CAAM,YAAY,IAAK,CAAA,KAAK,EAAE,MAAO,CAAA,OAAO,CAAE,CAAA,MAAA,CAAO,OAAO,CAAA,CAAA;AACvE,UAAA,MAAA;AAAA,QACG,KAAA,SAAA;AACH,UAAW,QAAA,GAAA,KAAA,CAAM,YAAY,IAAK,CAAA,KAAK,EAAE,MAAO,CAAA,KAAK,CAAE,CAAA,MAAA,CAAO,OAAO,CAAA,CAAA;AACrE,UAAA,MAAA;AAAA,QACG,KAAA,SAAA;AACH,UAAW,QAAA,GAAA,KAAA,CAAM,YAAY,IAAK,CAAA,KAAK,EAAE,MAAO,CAAA,OAAO,CAAE,CAAA,MAAA,CAAO,KAAK,CAAA,CAAA;AACrE,UAAA,MAAA;AAAA,OAAA;AAEJ,MAAA,IAAA,CAAK,UAAU,QAAQ,CAAA,CAAA;AAAA,KACzB,CAAA;AAEA,IAAA,MAAM,WAAc,GAAA,CAClB,IACA,EAAA,EAAE,OAAO,QACN,EAAA,KAAA;AACH,MAAA,IAAI,CAAC,QAAU,EAAA;AACb,QAAA,eAAA,CAAgB,MAAM,KAAK,CAAA,CAAA;AAC3B,QAAA,eAAA,CAAgB,IAAI,CAAA,CAAA;AACpB,QAAA,aAAA,CAAc,MAAM,KAAK,CAAA,CAAA;AAAA,OAC3B;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,YAAA,GAAe,CAAC,IAAmB,KAAA;AACvC,MAAc,WAAA,GAAA,IAAA,CAAA;AACd,MAAA,oBAAA,CAAqB,IAAI,CAAA,CAAA;AACzB,MAAA,MAAM,KAAQ,GAAA,IAAA,CAAK,GACjB,CAAA,IAAA,CAAK,KACF,CAAA,CAAA,mBAAA,CAAoB,KAAM,CAAA,WAAA,CAAY,IAAK,CAAA,CAAA,CAAG,GAAG,CAAA,CAAE,SACjD,GAAA,CAAA,eAAA,CAAgB,IAAI,CAAA,GAAI,GAAM,GAAA,EAAA,IAAM,cAAe,CAAA,IAAI,CACxD,GAAA,CAAA,IACA,cAAe,CAAA,IAAI,CACvB,CAAA,EACA,IAAS,KAAA,OAAA,GAAU,KAAK,EAC1B,CAAA,CAAA;AACA,MAAA,eAAA,CAAgB,MAAM,KAAK,CAAA,CAAA;AAAA,KAC7B,CAAA;AAEA,IAAM,MAAA,eAAA,GAAkB,CAAC,IAAmB,KAAA;AAC1C,MAAA,OAAO,KAAM,CAAA,WAAA,CAAY,IAAK,CAAA,CAAA,CAAG,GAAI,CAAA,YAAA,CAAA;AAAA,KACvC,CAAA;AAEA,IAAA,MAAM,kBAAkB,MAAM;AAC5B,MAAM,MAAA,YAAA,GAAe,CAAC,IAAmB,KAAA;AACvC,QAAM,MAAA,SAAA,GAAY,KAAM,CAAA,WAAA,CAAY,IAAK,CAAA,CAAA,CAAA;AACzC,QAAI,IAAA,SAAA,IAAa,UAAU,GAAK,EAAA;AAC9B,UAAA,mBAAA,CAAoB,SAAU,CAAA,GAAG,CAAE,CAAA,QAAA,GAAW,MAAM;AAGlD,YAAA,YAAA,CAAa,IAAI,CAAA,CAAA;AAAA,WACnB,CAAA;AAAA,SACF;AAAA,OACF,CAAA;AACA,MAAA,YAAA,CAAa,OAAO,CAAA,CAAA;AACpB,MAAA,YAAA,CAAa,SAAS,CAAA,CAAA;AACtB,MAAA,YAAA,CAAa,SAAS,CAAA,CAAA;AAAA,KACxB,CAAA;AAEA,IAAA,SAAA,CAAU,MAAM;AACd,MAAA,QAAA,CAAS,MAAM;AACb,QAAC,CAAA,KAAA,CAAM,gBAAgB,eAAgB,EAAA,CAAA;AACvC,QAAe,cAAA,EAAA,CAAA;AAEf,QAAA,IAAI,MAAM,IAAS,KAAA,OAAA;AAAS,UAAA,eAAA,CAAgB,OAAO,CAAA,CAAA;AAAA,OACpD,CAAA,CAAA;AAAA,KACF,CAAA,CAAA;AAED,IAAM,MAAA,MAAA,GAAS,CAAC,SAAA,EAA8B,IAAmB,KAAA;AAC/D,MAAA,WAAA,CAAY,MAAM,KAAQ,GAAA,SAAA,CAAA;AAAA,KAC5B,CAAA;AAEA,IAAA,IAAA,CAAK,cAAc,CAAC,CAAA,EAAG,KAAM,CAAA,IAAA,CAAA,WAAA,CAAA,EAAmB,UAAU,CAAC,CAAA,CAAA;AAC3D,IAAA,IAAA,CAAK,cAAc,CAAC,CAAA,EAAG,KAAM,CAAA,IAAA,CAAA,gBAAA,CAAA,EAAwB,eAAe,CAAC,CAAA,CAAA;AAErE,IACE,KAAA,CAAA,MAAM,KAAM,CAAA,WAAA,EACZ,MAAM;AACJ,MAAI,IAAA,WAAA;AAAa,QAAA,OAAA;AACjB,MAAe,cAAA,EAAA,CAAA;AAAA,KAEnB,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}