{"version": 3, "file": "utils.mjs", "sources": ["../../../../../../packages/components/time-picker/src/utils.ts"], "sourcesContent": ["import dayjs from 'dayjs'\nimport { isArray, isDate, isEmpty } from '@element-plus/utils'\n\nimport type { Dayjs } from 'dayjs'\nexport type TimeList = [number | undefined, number, undefined | number]\n\nexport const buildTimeList = (value: number, bound: number): TimeList => {\n  return [\n    value > 0 ? value - 1 : undefined,\n    value,\n    value < bound ? value + 1 : undefined,\n  ]\n}\n\nexport const rangeArr = (n: number) =>\n  Array.from(Array.from({ length: n }).keys())\n\nexport const extractDateFormat = (format: string) => {\n  return format\n    .replace(/\\W?m{1,2}|\\W?ZZ/g, '')\n    .replace(/\\W?h{1,2}|\\W?s{1,3}|\\W?a/gi, '')\n    .trim()\n}\n\nexport const extractTimeFormat = (format: string) => {\n  return format\n    .replace(/\\W?D{1,2}|\\W?Do|\\W?d{1,4}|\\W?M{1,4}|\\W?Y{2,4}/g, '')\n    .trim()\n}\n\nexport const dateEquals = function (a: Date | unknown, b: Date | unknown) {\n  const aIsDate = isDate(a)\n  const bIsDate = isDate(b)\n  if (aIsDate && bIsDate) {\n    return a.getTime() === b.getTime()\n  }\n  if (!aIsDate && !bIsDate) {\n    return a === b\n  }\n  return false\n}\n\nexport const valueEquals = function (\n  a: Array<Date> | unknown,\n  b: Array<Date> | unknown\n) {\n  const aIsArray = isArray(a)\n  const bIsArray = isArray(b)\n  if (aIsArray && bIsArray) {\n    if (a.length !== b.length) {\n      return false\n    }\n    return a.every((item, index) => dateEquals(item, b[index]))\n  }\n  if (!aIsArray && !bIsArray) {\n    return dateEquals(a, b)\n  }\n  return false\n}\n\nexport const parseDate = function (\n  date: string | number | Date,\n  format: string | undefined,\n  lang: string\n) {\n  const day =\n    isEmpty(format) || format === 'x'\n      ? dayjs(date).locale(lang)\n      : dayjs(date, format).locale(lang)\n  return day.isValid() ? day : undefined\n}\n\nexport const formatter = function (\n  date: string | number | Date | Dayjs,\n  format: string | undefined,\n  lang: string\n) {\n  if (isEmpty(format)) return date\n  if (format === 'x') return +date\n  return dayjs(date).locale(lang).format(format)\n}\n\nexport const makeList = (total: number, method?: () => number[]) => {\n  const arr: boolean[] = []\n  const disabledArr = method?.()\n  for (let i = 0; i < total; i++) {\n    arr.push(disabledArr?.includes(i) ?? false)\n  }\n  return arr\n}\n"], "names": [], "mappings": ";;;;;AAEY,MAAC,aAAa,GAAG,CAAC,KAAK,EAAE,KAAK,KAAK;AAC/C,EAAE,OAAO;AACT,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC;AAClC,IAAI,KAAK;AACT,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC;AACtC,GAAG,CAAC;AACJ,EAAE;AACU,MAAC,QAAQ,GAAG,CAAC,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE;AAChE,MAAC,iBAAiB,GAAG,CAAC,MAAM,KAAK;AAC7C,EAAE,OAAO,MAAM,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,4BAA4B,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;AACjG,EAAE;AACU,MAAC,iBAAiB,GAAG,CAAC,MAAM,KAAK;AAC7C,EAAE,OAAO,MAAM,CAAC,OAAO,CAAC,gDAAgD,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;AACrF,EAAE;AACU,MAAC,UAAU,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;AACzC,EAAE,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5B,EAAE,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,IAAI,OAAO,EAAE;AAC1B,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC;AACvC,GAAG;AACH,EAAE,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,EAAE;AAC5B,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC;AACnB,GAAG;AACH,EAAE,OAAO,KAAK,CAAC;AACf,EAAE;AACU,MAAC,WAAW,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;AAC1C,EAAE,MAAM,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AAC9B,EAAE,MAAM,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AAC9B,EAAE,IAAI,QAAQ,IAAI,QAAQ,EAAE;AAC5B,IAAI,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,EAAE;AAC/B,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK;AACL,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAChE,GAAG;AACH,EAAE,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,EAAE;AAC9B,IAAI,OAAO,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5B,GAAG;AACH,EAAE,OAAO,KAAK,CAAC;AACf,EAAE;AACU,MAAC,SAAS,GAAG,SAAS,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE;AACtD,EAAE,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,KAAK,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC9G,EAAE,OAAO,GAAG,CAAC,OAAO,EAAE,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC;AACtC,EAAE;AACU,MAAC,SAAS,GAAG,SAAS,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE;AACtD,EAAE,IAAI,OAAO,CAAC,MAAM,CAAC;AACrB,IAAI,OAAO,IAAI,CAAC;AAChB,EAAE,IAAI,MAAM,KAAK,GAAG;AACpB,IAAI,OAAO,CAAC,IAAI,CAAC;AACjB,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACjD,EAAE;AACU,MAAC,QAAQ,GAAG,CAAC,KAAK,EAAE,MAAM,KAAK;AAC3C,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,MAAM,GAAG,GAAG,EAAE,CAAC;AACjB,EAAE,MAAM,WAAW,GAAG,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC;AACzD,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;AAClC,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC;AACjG,GAAG;AACH,EAAE,OAAO,GAAG,CAAC;AACb;;;;"}