{"version": 3, "file": "constants.mjs", "sources": ["../../../../../../packages/components/time-picker/src/constants.ts"], "sourcesContent": ["export const timeUnits = ['hours', 'minutes', 'seconds'] as const\n\nexport const DEFAULT_FORMATS_TIME = 'HH:mm:ss'\nexport const DEFAULT_FORMATS_DATE = 'YYYY-MM-DD'\nexport const DEFAULT_FORMATS_DATEPICKER = {\n  date: DEFAULT_FORMATS_DATE,\n  dates: DEFAULT_FORMATS_DATE,\n  week: 'gggg[w]ww',\n  year: 'YYYY',\n  month: 'YYYY-MM',\n  datetime: `${DEFAULT_FORMATS_DATE} ${DEFAULT_FORMATS_TIME}`,\n  monthrange: 'YYYY-MM',\n  daterange: DEFAULT_FORMATS_DATE,\n  datetimerange: `${DEFAULT_FORMATS_DATE} ${DEFAULT_FORMATS_TIME}`,\n}\n\nexport type TimeUnit = typeof timeUnits[number]\n"], "names": [], "mappings": "AAAY,MAAC,SAAS,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE;AAC7C,MAAC,oBAAoB,GAAG,WAAW;AACnC,MAAC,oBAAoB,GAAG,aAAa;AACrC,MAAC,0BAA0B,GAAG;AAC1C,EAAE,IAAI,EAAE,oBAAoB;AAC5B,EAAE,KAAK,EAAE,oBAAoB;AAC7B,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,QAAQ,EAAE,CAAC,EAAE,oBAAoB,CAAC,CAAC,EAAE,oBAAoB,CAAC,CAAC;AAC7D,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,SAAS,EAAE,oBAAoB;AACjC,EAAE,aAAa,EAAE,CAAC,EAAE,oBAAoB,CAAC,CAAC,EAAE,oBAAoB,CAAC,CAAC;AAClE;;;;"}