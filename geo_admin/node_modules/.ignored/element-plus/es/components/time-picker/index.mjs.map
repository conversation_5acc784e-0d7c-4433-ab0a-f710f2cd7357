{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/time-picker/index.ts"], "sourcesContent": ["import TimePicker from './src/time-picker'\nimport CommonPicker from './src/common/picker.vue'\nimport TimePickPanel from './src/time-picker-com/panel-time-pick.vue'\n\nimport type { App } from 'vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport * from './src/utils'\nexport * from './src/constants'\nexport * from './src/common/props'\n\nconst _TimePicker = TimePicker as SFCWithInstall<typeof TimePicker>\n\n_TimePicker.install = (app: App) => {\n  app.component(_TimePicker.name, _TimePicker)\n}\n\nexport { CommonPicker, TimePickPanel }\nexport default _TimePicker\nexport const ElTimePicker = _TimePicker\n"], "names": [], "mappings": ";;;;;;;AAMK,MAAC,WAAW,GAAG,WAAW;AAC/B,WAAW,CAAC,OAAO,GAAG,CAAC,GAAG,KAAK;AAC/B,EAAE,GAAG,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AAC/C,CAAC,CAAC;AAGU,MAAC,YAAY,GAAG;;;;"}