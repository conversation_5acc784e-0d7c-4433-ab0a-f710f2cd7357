{"version": 3, "file": "time-select.mjs", "sources": ["../../../../../../packages/components/time-select/src/time-select.vue"], "sourcesContent": ["<template>\n  <el-select\n    ref=\"select\"\n    :model-value=\"value\"\n    :disabled=\"_disabled\"\n    :clearable=\"clearable\"\n    :clear-icon=\"clearIcon\"\n    :size=\"size\"\n    :effect=\"effect\"\n    :placeholder=\"placeholder\"\n    default-first-option\n    :filterable=\"editable\"\n    @update:model-value=\"(event) => $emit('update:modelValue', event)\"\n    @change=\"(event) => $emit('change', event)\"\n    @blur=\"(event) => $emit('blur', event)\"\n    @focus=\"(event) => $emit('focus', event)\"\n  >\n    <el-option\n      v-for=\"item in items\"\n      :key=\"item.value\"\n      :label=\"item.value\"\n      :value=\"item.value\"\n      :disabled=\"item.disabled\"\n    />\n    <template #prefix>\n      <el-icon v-if=\"prefixIcon\" :class=\"nsInput.e('prefix-icon')\">\n        <component :is=\"prefixIcon\" />\n      </el-icon>\n    </template>\n  </el-select>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, ref } from 'vue'\nimport dayjs from 'dayjs'\nimport customParseFormat from 'dayjs/plugin/customParseFormat.js'\nimport ElSelect from '@element-plus/components/select'\nimport { useFormDisabled } from '@element-plus/components/form'\nimport ElIcon from '@element-plus/components/icon'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { timeSelectProps } from './time-select'\nimport { compareTime, formatTime, nextTime, parseTime } from './utils'\n\ndayjs.extend(customParseFormat)\n\nconst { Option: ElOption } = ElSelect\n\ndefineOptions({\n  name: 'ElTimeSelect',\n})\n\ndefineEmits(['change', 'blur', 'focus', 'update:modelValue'])\n\nconst props = defineProps(timeSelectProps)\n\nconst nsInput = useNamespace('input')\nconst select = ref<typeof ElSelect>()\n\nconst _disabled = useFormDisabled()\nconst { lang } = useLocale()\n\nconst value = computed(() => props.modelValue)\nconst start = computed(() => {\n  const time = parseTime(props.start)\n  return time ? formatTime(time) : null\n})\n\nconst end = computed(() => {\n  const time = parseTime(props.end)\n  return time ? formatTime(time) : null\n})\n\nconst step = computed(() => {\n  const time = parseTime(props.step)\n  return time ? formatTime(time) : null\n})\n\nconst minTime = computed(() => {\n  const time = parseTime(props.minTime || '')\n  return time ? formatTime(time) : null\n})\n\nconst maxTime = computed(() => {\n  const time = parseTime(props.maxTime || '')\n  return time ? formatTime(time) : null\n})\n\nconst items = computed(() => {\n  const result: { value: string; disabled: boolean }[] = []\n  if (props.start && props.end && props.step) {\n    let current = start.value\n    let currentTime: string\n    while (current && end.value && compareTime(current, end.value) <= 0) {\n      currentTime = dayjs(current, 'HH:mm')\n        .locale(lang.value)\n        .format(props.format)\n      result.push({\n        value: currentTime,\n        disabled:\n          compareTime(current, minTime.value || '-1:-1') <= 0 ||\n          compareTime(current, maxTime.value || '100:100') >= 0,\n      })\n      current = nextTime(current, step.value!)\n    }\n  }\n  return result\n})\n\nconst blur = () => {\n  select.value?.blur?.()\n}\n\nconst focus = () => {\n  select.value?.focus?.()\n}\n\ndefineExpose({\n  /**\n   * @description focus the Input component\n   */\n  blur,\n  /**\n   * @description blur the Input component\n   */\n  focus,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;mCA+Cc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AANA,IAAA,KAAA,CAAM,OAAO,iBAAiB,CAAA,CAAA;AAE9B,IAAM,MAAA,EAAE,QAAQ,QAAa,EAAA,GAAA,QAAA,CAAA;AAU7B,IAAM,MAAA,OAAA,GAAU,aAAa,OAAO,CAAA,CAAA;AACpC,IAAA,MAAM,SAAS,GAAqB,EAAA,CAAA;AAEpC,IAAA,MAAM,YAAY,eAAgB,EAAA,CAAA;AAClC,IAAM,MAAA,EAAE,SAAS,SAAU,EAAA,CAAA;AAE3B,IAAA,MAAM,KAAQ,GAAA,QAAA,CAAS,MAAM,KAAA,CAAM,UAAU,CAAA,CAAA;AAC7C,IAAM,MAAA,KAAA,GAAQ,SAAS,MAAM;AAC3B,MAAM,MAAA,IAAA,GAAO,SAAU,CAAA,KAAA,CAAM,KAAK,CAAA,CAAA;AAClC,MAAO,OAAA,IAAA,GAAO,UAAW,CAAA,IAAI,CAAI,GAAA,IAAA,CAAA;AAAA,KAClC,CAAA,CAAA;AAED,IAAM,MAAA,GAAA,GAAM,SAAS,MAAM;AACzB,MAAM,MAAA,IAAA,GAAO,SAAU,CAAA,KAAA,CAAM,GAAG,CAAA,CAAA;AAChC,MAAO,OAAA,IAAA,GAAO,UAAW,CAAA,IAAI,CAAI,GAAA,IAAA,CAAA;AAAA,KAClC,CAAA,CAAA;AAED,IAAM,MAAA,IAAA,GAAO,SAAS,MAAM;AAC1B,MAAM,MAAA,IAAA,GAAO,SAAU,CAAA,KAAA,CAAM,IAAI,CAAA,CAAA;AACjC,MAAO,OAAA,IAAA,GAAO,UAAW,CAAA,IAAI,CAAI,GAAA,IAAA,CAAA;AAAA,KAClC,CAAA,CAAA;AAED,IAAM,MAAA,OAAA,GAAU,SAAS,MAAM;AAC7B,MAAA,MAAM,IAAO,GAAA,SAAA,CAAU,KAAM,CAAA,OAAA,IAAW,EAAE,CAAA,CAAA;AAC1C,MAAO,OAAA,IAAA,GAAO,UAAW,CAAA,IAAI,CAAI,GAAA,IAAA,CAAA;AAAA,KAClC,CAAA,CAAA;AAED,IAAM,MAAA,OAAA,GAAU,SAAS,MAAM;AAC7B,MAAA,MAAM,IAAO,GAAA,SAAA,CAAU,KAAM,CAAA,OAAA,IAAW,EAAE,CAAA,CAAA;AAC1C,MAAO,OAAA,IAAA,GAAO,UAAW,CAAA,IAAI,CAAI,GAAA,IAAA,CAAA;AAAA,KAClC,CAAA,CAAA;AAED,IAAM,MAAA,KAAA,GAAQ,SAAS,MAAM;AAC3B,MAAA,MAAM,SAAiD,EAAC,CAAA;AACxD,MAAA,IAAI,KAAM,CAAA,KAAA,IAAS,KAAM,CAAA,GAAA,IAAO,MAAM,IAAM,EAAA;AAC1C,QAAA,IAAI,UAAU,KAAM,CAAA,KAAA,CAAA;AACpB,QAAI,IAAA,WAAA,CAAA;AACJ,QAAO,OAAA,OAAA,IAAW,IAAI,KAAS,IAAA,WAAA,CAAY,SAAS,GAAI,CAAA,KAAK,KAAK,CAAG,EAAA;AACnE,UAAc,WAAA,GAAA,KAAA,CAAM,OAAS,EAAA,OAAO,CACjC,CAAA,MAAA,CAAO,KAAK,KAAK,CAAA,CACjB,MAAO,CAAA,KAAA,CAAM,MAAM,CAAA,CAAA;AACtB,UAAA,MAAA,CAAO,IAAK,CAAA;AAAA,YACV,KAAO,EAAA,WAAA;AAAA,YACP,QACE,EAAA,WAAA,CAAY,OAAS,EAAA,OAAA,CAAQ,KAAS,IAAA,OAAO,CAAK,IAAA,CAAA,IAClD,WAAY,CAAA,OAAA,EAAS,OAAQ,CAAA,KAAA,IAAS,SAAS,CAAK,IAAA,CAAA;AAAA,WACvD,CAAA,CAAA;AACD,UAAU,OAAA,GAAA,QAAA,CAAS,OAAS,EAAA,IAAA,CAAK,KAAM,CAAA,CAAA;AAAA,SACzC;AAAA,OACF;AACA,MAAO,OAAA,MAAA,CAAA;AAAA,KACR,CAAA,CAAA;AAED,IAAA,MAAM,OAAO,MAAM;AACjB,MAAA,IAAA,EAAA;AAAqB,MACvB,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,MAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAA,MAAA,cAAsB;AAAA,MACxB,IAAA,EAAA,EAAA,EAAA,CAAA;AAEA,MAAa,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,MAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;AAAA,KAIX,CAAA;AAAA,IAIA,MAAA,CAAA;AAAA,MACD,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}