{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/time-select/index.ts"], "sourcesContent": ["import TimeSelect from './src/time-select.vue'\n\nimport type { App } from 'vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nTimeSelect.install = (app: App): void => {\n  app.component(TimeSelect.name, TimeSelect)\n}\n\nconst _TimeSelect = TimeSelect as SFCWithInstall<typeof TimeSelect>\n\nexport default _TimeSelect\nexport const ElTimeSelect = _TimeSelect\n"], "names": [], "mappings": ";;AACA,UAAU,CAAC,OAAO,GAAG,CAAC,GAAG,KAAK;AAC9B,EAAE,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;AAC7C,CAAC,CAAC;AACG,MAAC,WAAW,GAAG,WAAW;AAEnB,MAAC,YAAY,GAAG;;;;"}