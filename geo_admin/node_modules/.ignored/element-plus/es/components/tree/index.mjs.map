{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/tree/index.ts"], "sourcesContent": ["import Tree from './src/tree.vue'\n\nimport type { App } from 'vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nTree.install = (app: App): void => {\n  app.component(Tree.name, Tree)\n}\n\nconst _Tree = Tree as SFCWithInstall<typeof Tree>\n\nexport default _Tree\nexport const ElTree = _Tree\n"], "names": [], "mappings": ";;AACA,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,KAAK;AACxB,EAAE,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACjC,CAAC,CAAC;AACG,MAAC,KAAK,GAAG,KAAK;AAEP,MAAC,MAAM,GAAG;;;;"}