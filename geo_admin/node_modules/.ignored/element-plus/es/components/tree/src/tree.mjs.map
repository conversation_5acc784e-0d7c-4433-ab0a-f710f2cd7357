{"version": 3, "file": "tree.mjs", "sources": ["../../../../../../packages/components/tree/src/tree.vue"], "sourcesContent": ["<template>\n  <div\n    ref=\"el$\"\n    :class=\"[\n      ns.b(),\n      ns.is('dragging', !!dragState.draggingNode),\n      ns.is('drop-not-allow', !dragState.allowDrop),\n      ns.is('drop-inner', dragState.dropType === 'inner'),\n      { [ns.m('highlight-current')]: highlightCurrent },\n    ]\"\n    role=\"tree\"\n  >\n    <el-tree-node\n      v-for=\"child in root.childNodes\"\n      :key=\"getNodeKey(child)\"\n      :node=\"child\"\n      :props=\"props\"\n      :accordion=\"accordion\"\n      :render-after-expand=\"renderAfterExpand\"\n      :show-checkbox=\"showCheckbox\"\n      :render-content=\"renderContent\"\n      @node-expand=\"handleNodeExpand\"\n    />\n    <div v-if=\"isEmpty\" :class=\"ns.e('empty-block')\">\n      <slot name=\"empty\">\n        <span :class=\"ns.e('empty-text')\">\n          {{ emptyText ?? t('el.tree.emptyText') }}\n        </span>\n      </slot>\n    </div>\n    <div\n      v-show=\"dragState.showDropIndicator\"\n      ref=\"dropIndicator$\"\n      :class=\"ns.e('drop-indicator')\"\n    />\n  </div>\n</template>\n<script lang=\"ts\">\n// @ts-nocheck\nimport {\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  provide,\n  ref,\n  watch,\n} from 'vue'\nimport { iconPropType } from '@element-plus/utils'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { formItemContextKey } from '@element-plus/components/form'\nimport TreeStore from './model/tree-store'\nimport { getNodeKey as getNodeKeyUtil, handleCurrentChange } from './model/util'\nimport ElTreeNode from './tree-node.vue'\nimport { useNodeExpandEventBroadcast } from './model/useNodeExpandEventBroadcast'\nimport { useDragNodeHandler } from './model/useDragNode'\nimport { useKeydown } from './model/useKeydown'\nimport type Node from './model/node'\n\nimport type { ComponentInternalInstance, PropType } from 'vue'\nimport type { Nullable } from '@element-plus/utils'\nimport type {\n  TreeComponentProps,\n  TreeData,\n  TreeKey,\n  TreeNodeData,\n} from './tree.type'\n\nexport default defineComponent({\n  name: 'ElTree',\n  components: { ElTreeNode },\n  props: {\n    data: {\n      type: Array,\n      default: () => [],\n    },\n    emptyText: {\n      type: String,\n    },\n    renderAfterExpand: {\n      type: Boolean,\n      default: true,\n    },\n    nodeKey: String,\n    checkStrictly: Boolean,\n    defaultExpandAll: Boolean,\n    expandOnClickNode: {\n      type: Boolean,\n      default: true,\n    },\n    checkOnClickNode: Boolean,\n    checkDescendants: {\n      type: Boolean,\n      default: false,\n    },\n    autoExpandParent: {\n      type: Boolean,\n      default: true,\n    },\n    defaultCheckedKeys: Array as PropType<\n      TreeComponentProps['defaultCheckedKeys']\n    >,\n    defaultExpandedKeys: Array as PropType<\n      TreeComponentProps['defaultExpandedKeys']\n    >,\n    currentNodeKey: [String, Number] as PropType<string | number>,\n    renderContent: Function,\n    showCheckbox: {\n      type: Boolean,\n      default: false,\n    },\n    draggable: {\n      type: Boolean,\n      default: false,\n    },\n    allowDrag: Function,\n    allowDrop: Function,\n    props: {\n      type: Object as PropType<TreeComponentProps['props']>,\n      default: () => ({\n        children: 'children',\n        label: 'label',\n        disabled: 'disabled',\n      }),\n    },\n    lazy: {\n      type: Boolean,\n      default: false,\n    },\n    highlightCurrent: Boolean,\n    load: Function as PropType<TreeComponentProps['load']>,\n    filterNodeMethod: Function as PropType<\n      TreeComponentProps['filterNodeMethod']\n    >,\n    accordion: Boolean,\n    indent: {\n      type: Number,\n      default: 18,\n    },\n    icon: {\n      type: iconPropType,\n    },\n  },\n  emits: [\n    'check-change',\n    'current-change',\n    'node-click',\n    'node-contextmenu',\n    'node-collapse',\n    'node-expand',\n    'check',\n    'node-drag-start',\n    'node-drag-end',\n    'node-drop',\n    'node-drag-leave',\n    'node-drag-enter',\n    'node-drag-over',\n  ],\n  setup(props, ctx) {\n    const { t } = useLocale()\n    const ns = useNamespace('tree')\n\n    const store = ref<TreeStore>(\n      new TreeStore({\n        key: props.nodeKey,\n        data: props.data,\n        lazy: props.lazy,\n        props: props.props,\n        load: props.load,\n        currentNodeKey: props.currentNodeKey,\n        checkStrictly: props.checkStrictly,\n        checkDescendants: props.checkDescendants,\n        defaultCheckedKeys: props.defaultCheckedKeys,\n        defaultExpandedKeys: props.defaultExpandedKeys,\n        autoExpandParent: props.autoExpandParent,\n        defaultExpandAll: props.defaultExpandAll,\n        filterNodeMethod: props.filterNodeMethod,\n      })\n    )\n\n    store.value.initialize()\n\n    const root = ref<Node>(store.value.root)\n    const currentNode = ref<Node>(null)\n    const el$ = ref<Nullable<HTMLElement>>(null)\n    const dropIndicator$ = ref<Nullable<HTMLElement>>(null)\n\n    const { broadcastExpanded } = useNodeExpandEventBroadcast(props)\n\n    const { dragState } = useDragNodeHandler({\n      props,\n      ctx,\n      el$,\n      dropIndicator$,\n      store,\n    })\n\n    useKeydown({ el$ }, store)\n\n    const isEmpty = computed(() => {\n      const { childNodes } = root.value\n      return (\n        !childNodes ||\n        childNodes.length === 0 ||\n        childNodes.every(({ visible }) => !visible)\n      )\n    })\n\n    watch(\n      () => props.currentNodeKey,\n      (newVal) => {\n        store.value.setCurrentNodeKey(newVal)\n      }\n    )\n\n    watch(\n      () => props.defaultCheckedKeys,\n      (newVal) => {\n        store.value.setDefaultCheckedKey(newVal)\n      }\n    )\n\n    watch(\n      () => props.defaultExpandedKeys,\n      (newVal) => {\n        store.value.setDefaultExpandedKeys(newVal)\n      }\n    )\n\n    watch(\n      () => props.data,\n      (newVal) => {\n        store.value.setData(newVal)\n      },\n      { deep: true }\n    )\n\n    watch(\n      () => props.checkStrictly,\n      (newVal) => {\n        store.value.checkStrictly = newVal\n      }\n    )\n\n    const filter = (value) => {\n      if (!props.filterNodeMethod)\n        throw new Error('[Tree] filterNodeMethod is required when filter')\n      store.value.filter(value)\n    }\n\n    const getNodeKey = (node: Node) => {\n      return getNodeKeyUtil(props.nodeKey, node.data)\n    }\n\n    const getNodePath = (data: TreeKey | TreeNodeData) => {\n      if (!props.nodeKey)\n        throw new Error('[Tree] nodeKey is required in getNodePath')\n      const node = store.value.getNode(data)\n      if (!node) return []\n      const path = [node.data]\n      let parent = node.parent\n      while (parent && parent !== root.value) {\n        path.push(parent.data)\n        parent = parent.parent\n      }\n      return path.reverse()\n    }\n\n    const getCheckedNodes = (\n      leafOnly?: boolean,\n      includeHalfChecked?: boolean\n    ): TreeNodeData[] => {\n      return store.value.getCheckedNodes(leafOnly, includeHalfChecked)\n    }\n\n    const getCheckedKeys = (leafOnly?: boolean): TreeKey[] => {\n      return store.value.getCheckedKeys(leafOnly)\n    }\n\n    const getCurrentNode = (): TreeNodeData => {\n      const currentNode = store.value.getCurrentNode()\n      return currentNode ? currentNode.data : null\n    }\n\n    const getCurrentKey = (): any => {\n      if (!props.nodeKey)\n        throw new Error('[Tree] nodeKey is required in getCurrentKey')\n      const currentNode = getCurrentNode()\n      return currentNode ? currentNode[props.nodeKey] : null\n    }\n\n    const setCheckedNodes = (nodes: Node[], leafOnly?: boolean) => {\n      if (!props.nodeKey)\n        throw new Error('[Tree] nodeKey is required in setCheckedNodes')\n      store.value.setCheckedNodes(nodes, leafOnly)\n    }\n\n    const setCheckedKeys = (keys: TreeKey[], leafOnly?: boolean) => {\n      if (!props.nodeKey)\n        throw new Error('[Tree] nodeKey is required in setCheckedKeys')\n      store.value.setCheckedKeys(keys, leafOnly)\n    }\n\n    const setChecked = (\n      data: TreeKey | TreeNodeData,\n      checked: boolean,\n      deep: boolean\n    ) => {\n      store.value.setChecked(data, checked, deep)\n    }\n\n    const getHalfCheckedNodes = (): TreeNodeData[] => {\n      return store.value.getHalfCheckedNodes()\n    }\n\n    const getHalfCheckedKeys = (): TreeKey[] => {\n      return store.value.getHalfCheckedKeys()\n    }\n\n    const setCurrentNode = (node: Node, shouldAutoExpandParent = true) => {\n      if (!props.nodeKey)\n        throw new Error('[Tree] nodeKey is required in setCurrentNode')\n\n      handleCurrentChange(store, ctx.emit, () =>\n        store.value.setUserCurrentNode(node, shouldAutoExpandParent)\n      )\n    }\n\n    const setCurrentKey = (key?: TreeKey, shouldAutoExpandParent = true) => {\n      if (!props.nodeKey)\n        throw new Error('[Tree] nodeKey is required in setCurrentKey')\n\n      handleCurrentChange(store, ctx.emit, () =>\n        store.value.setCurrentNodeKey(key, shouldAutoExpandParent)\n      )\n    }\n\n    const getNode = (data: TreeKey | TreeNodeData): Node => {\n      return store.value.getNode(data)\n    }\n\n    const remove = (data: TreeNodeData | Node) => {\n      store.value.remove(data)\n    }\n\n    const append = (\n      data: TreeNodeData,\n      parentNode: TreeNodeData | TreeKey | Node\n    ) => {\n      store.value.append(data, parentNode)\n    }\n\n    const insertBefore = (\n      data: TreeNodeData,\n      refNode: TreeKey | TreeNodeData | Node\n    ) => {\n      store.value.insertBefore(data, refNode)\n    }\n\n    const insertAfter = (\n      data: TreeNodeData,\n      refNode: TreeKey | TreeNodeData | Node\n    ) => {\n      store.value.insertAfter(data, refNode)\n    }\n\n    const handleNodeExpand = (\n      nodeData: TreeNodeData,\n      node: Node,\n      instance: ComponentInternalInstance\n    ) => {\n      broadcastExpanded(node)\n      ctx.emit('node-expand', nodeData, node, instance)\n    }\n\n    const updateKeyChildren = (key: TreeKey, data: TreeData) => {\n      if (!props.nodeKey)\n        throw new Error('[Tree] nodeKey is required in updateKeyChild')\n      store.value.updateChildren(key, data)\n    }\n\n    provide('RootTree', {\n      ctx,\n      props,\n      store,\n      root,\n      currentNode,\n      instance: getCurrentInstance(),\n    } as any)\n\n    provide(formItemContextKey, undefined)\n\n    return {\n      ns,\n      // ref\n      store,\n      root,\n      currentNode,\n      dragState,\n      el$,\n      dropIndicator$,\n\n      // computed\n      isEmpty,\n\n      // methods\n      filter,\n      getNodeKey,\n      getNodePath,\n      getCheckedNodes,\n      getCheckedKeys,\n      getCurrentNode,\n      getCurrentKey,\n      setCheckedNodes,\n      setCheckedKeys,\n      setChecked,\n      getHalfCheckedNodes,\n      getHalfCheckedKeys,\n      setCurrentNode,\n      setCurrentKey,\n      t,\n      getNode,\n      remove,\n      append,\n      insertBefore,\n      insertAfter,\n      handleNodeExpand,\n      updateKeyChildren,\n    }\n  },\n})\n</script>\n"], "names": ["getNodeKey", "getNodeKeyUtil", "_createElementBlock", "_normalizeClass", "_openBlock", "_Fragment", "_renderList", "_createBlock", "_renderSlot", "_createElementVNode", "_createCommentVNode", "_withDirectives"], "mappings": ";;;;;;;;;;;;;;;;AAmEA,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,QAAA;AAAA,EACN,UAAA,EAAY,EAAE,UAAW,EAAA;AAAA,EACzB,KAAO,EAAA;AAAA,IACL,IAAM,EAAA;AAAA,MACJ,IAAM,EAAA,KAAA;AAAA,MACN,OAAA,EAAS,MAAM,EAAC;AAAA,KAClB;AAAA,IACA,SAAW,EAAA;AAAA,MACT,IAAM,EAAA,MAAA;AAAA,KACR;AAAA,IACA,iBAAmB,EAAA;AAAA,MACjB,IAAM,EAAA,OAAA;AAAA,MACN,OAAS,EAAA,IAAA;AAAA,KACX;AAAA,IACA,OAAS,EAAA,MAAA;AAAA,IACT,aAAe,EAAA,OAAA;AAAA,IACf,gBAAkB,EAAA,OAAA;AAAA,IAClB,iBAAmB,EAAA;AAAA,MACjB,IAAM,EAAA,OAAA;AAAA,MACN,OAAS,EAAA,IAAA;AAAA,KACX;AAAA,IACA,gBAAkB,EAAA,OAAA;AAAA,IAClB,gBAAkB,EAAA;AAAA,MAChB,IAAM,EAAA,OAAA;AAAA,MACN,OAAS,EAAA,KAAA;AAAA,KACX;AAAA,IACA,gBAAkB,EAAA;AAAA,MAChB,IAAM,EAAA,OAAA;AAAA,MACN,OAAS,EAAA,IAAA;AAAA,KACX;AAAA,IACA,kBAAoB,EAAA,KAAA;AAAA,IAGpB,mBAAqB,EAAA,KAAA;AAAA,IAGrB,cAAA,EAAgB,CAAC,MAAA,EAAQ,MAAM,CAAA;AAAA,IAC/B,aAAe,EAAA,QAAA;AAAA,IACf,YAAc,EAAA;AAAA,MACZ,IAAM,EAAA,OAAA;AAAA,MACN,OAAS,EAAA,KAAA;AAAA,KACX;AAAA,IACA,SAAW,EAAA;AAAA,MACT,IAAM,EAAA,OAAA;AAAA,MACN,OAAS,EAAA,KAAA;AAAA,KACX;AAAA,IACA,SAAW,EAAA,QAAA;AAAA,IACX,SAAW,EAAA,QAAA;AAAA,IACX,KAAO,EAAA;AAAA,MACL,IAAM,EAAA,MAAA;AAAA,MACN,SAAS,OAAO;AAAA,QACd,QAAU,EAAA,UAAA;AAAA,QACV,KAAO,EAAA,OAAA;AAAA,QACP,QAAU,EAAA,UAAA;AAAA,OACZ,CAAA;AAAA,KACF;AAAA,IACA,IAAM,EAAA;AAAA,MACJ,IAAM,EAAA,OAAA;AAAA,MACN,OAAS,EAAA,KAAA;AAAA,KACX;AAAA,IACA,gBAAkB,EAAA,OAAA;AAAA,IAClB,IAAM,EAAA,QAAA;AAAA,IACN,gBAAkB,EAAA,QAAA;AAAA,IAGlB,SAAW,EAAA,OAAA;AAAA,IACX,MAAQ,EAAA;AAAA,MACN,IAAM,EAAA,MAAA;AAAA,MACN,OAAS,EAAA,EAAA;AAAA,KACX;AAAA,IACA,IAAM,EAAA;AAAA,MACJ,IAAM,EAAA,YAAA;AAAA,KACR;AAAA,GACF;AAAA,EACA,KAAO,EAAA;AAAA,IACL,cAAA;AAAA,IACA,gBAAA;AAAA,IACA,YAAA;AAAA,IACA,kBAAA;AAAA,IACA,eAAA;AAAA,IACA,aAAA;AAAA,IACA,OAAA;AAAA,IACA,iBAAA;AAAA,IACA,eAAA;AAAA,IACA,WAAA;AAAA,IACA,iBAAA;AAAA,IACA,iBAAA;AAAA,IACA,gBAAA;AAAA,GACF;AAAA,EACA,KAAA,CAAM,OAAO,GAAK,EAAA;AAChB,IAAM,MAAA,EAAE,MAAM,SAAU,EAAA,CAAA;AACxB,IAAM,MAAA,EAAA,GAAK,aAAa,MAAM,CAAA,CAAA;AAE9B,IAAM,MAAA,KAAA,GAAQ,GACZ,CAAA,IAAI,SAAU,CAAA;AAAA,MACZ,KAAK,KAAM,CAAA,OAAA;AAAA,MACX,MAAM,KAAM,CAAA,IAAA;AAAA,MACZ,MAAM,KAAM,CAAA,IAAA;AAAA,MACZ,OAAO,KAAM,CAAA,KAAA;AAAA,MACb,MAAM,KAAM,CAAA,IAAA;AAAA,MACZ,gBAAgB,KAAM,CAAA,cAAA;AAAA,MACtB,eAAe,KAAM,CAAA,aAAA;AAAA,MACrB,kBAAkB,KAAM,CAAA,gBAAA;AAAA,MACxB,oBAAoB,KAAM,CAAA,kBAAA;AAAA,MAC1B,qBAAqB,KAAM,CAAA,mBAAA;AAAA,MAC3B,kBAAkB,KAAM,CAAA,gBAAA;AAAA,MACxB,kBAAkB,KAAM,CAAA,gBAAA;AAAA,MACxB,kBAAkB,KAAM,CAAA,gBAAA;AAAA,KACzB,CACH,CAAA,CAAA;AAEA,IAAA,KAAA,CAAM,MAAM,UAAW,EAAA,CAAA;AAEvB,IAAA,MAAM,IAAO,GAAA,GAAA,CAAU,KAAM,CAAA,KAAA,CAAM,IAAI,CAAA,CAAA;AACvC,IAAM,MAAA,WAAA,GAAc,IAAU,IAAI,CAAA,CAAA;AAClC,IAAM,MAAA,GAAA,GAAM,IAA2B,IAAI,CAAA,CAAA;AAC3C,IAAM,MAAA,cAAA,GAAiB,IAA2B,IAAI,CAAA,CAAA;AAEtD,IAAM,MAAA,EAAE,iBAAsB,EAAA,GAAA,2BAAA,CAA4B,KAAK,CAAA,CAAA;AAE/D,IAAM,MAAA,EAAE,cAAc,kBAAmB,CAAA;AAAA,MACvC,KAAA;AAAA,MACA,GAAA;AAAA,MACA,GAAA;AAAA,MACA,cAAA;AAAA,MACA,KAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAW,UAAA,CAAA,EAAE,GAAI,EAAA,EAAG,KAAK,CAAA,CAAA;AAEzB,IAAM,MAAA,OAAA,GAAU,SAAS,MAAM;AAC7B,MAAM,MAAA,EAAE,eAAe,IAAK,CAAA,KAAA,CAAA;AAC5B,MACE,OAAA,CAAC,UACD,IAAA,UAAA,CAAW,MAAW,KAAA,CAAA,IACtB,UAAW,CAAA,KAAA,CAAM,CAAC,EAAE,OAAc,EAAA,KAAA,CAAC,OAAO,CAAA,CAAA;AAAA,KAE7C,CAAA,CAAA;AAED,IAAA,KAAA,CACE,MAAM,KAAA,CAAM,cACZ,EAAA,CAAC,MAAW,KAAA;AACV,MAAM,KAAA,CAAA,KAAA,CAAM,kBAAkB,MAAM,CAAA,CAAA;AAAA,KAExC,CAAA,CAAA;AAEA,IAAA,KAAA,CACE,MAAM,KAAA,CAAM,kBACZ,EAAA,CAAC,MAAW,KAAA;AACV,MAAM,KAAA,CAAA,KAAA,CAAM,qBAAqB,MAAM,CAAA,CAAA;AAAA,KAE3C,CAAA,CAAA;AAEA,IAAA,KAAA,CACE,MAAM,KAAA,CAAM,mBACZ,EAAA,CAAC,MAAW,KAAA;AACV,MAAM,KAAA,CAAA,KAAA,CAAM,uBAAuB,MAAM,CAAA,CAAA;AAAA,KAE7C,CAAA,CAAA;AAEA,IAAA,KAAA,CACE,MAAM,KAAA,CAAM,IACZ,EAAA,CAAC,MAAW,KAAA;AACV,MAAM,KAAA,CAAA,KAAA,CAAM,QAAQ,MAAM,CAAA,CAAA;AAAA,KAE5B,EAAA,EAAE,IAAM,EAAA,IAAA,EACV,CAAA,CAAA;AAEA,IAAA,KAAA,CACE,MAAM,KAAA,CAAM,aACZ,EAAA,CAAC,MAAW,KAAA;AACV,MAAA,KAAA,CAAM,MAAM,aAAgB,GAAA,MAAA,CAAA;AAAA,KAEhC,CAAA,CAAA;AAEA,IAAM,MAAA,MAAA,GAAS,CAAC,KAAU,KAAA;AACxB,MAAA,IAAI,CAAC,KAAM,CAAA,gBAAA;AACT,QAAM,MAAA,IAAI,MAAM,iDAAiD,CAAA,CAAA;AACnE,MAAM,KAAA,CAAA,KAAA,CAAM,OAAO,KAAK,CAAA,CAAA;AAAA,KAC1B,CAAA;AAEA,IAAM,MAAAA,YAAA,GAAa,CAAC,IAAe,KAAA;AACjC,MAAA,OAAOC,UAAe,CAAA,KAAA,CAAM,OAAS,EAAA,IAAA,CAAK,IAAI,CAAA,CAAA;AAAA,KAChD,CAAA;AAEA,IAAM,MAAA,WAAA,GAAc,CAAC,IAAiC,KAAA;AACpD,MAAA,IAAI,CAAC,KAAM,CAAA,OAAA;AACT,QAAM,MAAA,IAAI,MAAM,2CAA2C,CAAA,CAAA;AAC7D,MAAA,MAAM,IAAO,GAAA,KAAA,CAAM,KAAM,CAAA,OAAA,CAAQ,IAAI,CAAA,CAAA;AACrC,MAAA,IAAI,CAAC,IAAA;AAAM,QAAA,OAAO,EAAC,CAAA;AACnB,MAAM,MAAA,IAAA,GAAO,CAAC,IAAA,CAAK,IAAI,CAAA,CAAA;AACvB,MAAA,IAAI,SAAS,IAAK,CAAA,MAAA,CAAA;AAClB,MAAO,OAAA,MAAA,IAAU,MAAW,KAAA,IAAA,CAAK,KAAO,EAAA;AACtC,QAAK,IAAA,CAAA,IAAA,CAAK,OAAO,IAAI,CAAA,CAAA;AACrB,QAAA,MAAA,GAAS,MAAO,CAAA,MAAA,CAAA;AAAA,OAClB;AACA,MAAA,OAAO,KAAK,OAAQ,EAAA,CAAA;AAAA,KACtB,CAAA;AAEA,IAAM,MAAA,eAAA,GAAkB,CACtB,QAAA,EACA,kBACmB,KAAA;AACnB,MAAA,OAAO,KAAM,CAAA,KAAA,CAAM,eAAgB,CAAA,QAAA,EAAU,kBAAkB,CAAA,CAAA;AAAA,KACjE,CAAA;AAEA,IAAM,MAAA,cAAA,GAAiB,CAAC,QAAkC,KAAA;AACxD,MAAO,OAAA,KAAA,CAAM,KAAM,CAAA,cAAA,CAAe,QAAQ,CAAA,CAAA;AAAA,KAC5C,CAAA;AAEA,IAAA,MAAM,iBAAiB,MAAoB;AACzC,MAAM,MAAA,YAAA,GAAc,KAAM,CAAA,KAAA,CAAM,cAAe,EAAA,CAAA;AAC/C,MAAO,OAAA,YAAA,GAAc,aAAY,IAAO,GAAA,IAAA,CAAA;AAAA,KAC1C,CAAA;AAEA,IAAA,MAAM,gBAAgB,MAAW;AAC/B,MAAA,IAAI,CAAC,KAAM,CAAA,OAAA;AACT,QAAM,MAAA,IAAI,MAAM,6CAA6C,CAAA,CAAA;AAC/D,MAAA,MAAM,eAAc,cAAe,EAAA,CAAA;AACnC,MAAO,OAAA,YAAA,GAAc,YAAY,CAAA,KAAA,CAAM,OAAW,CAAA,GAAA,IAAA,CAAA;AAAA,KACpD,CAAA;AAEA,IAAM,MAAA,eAAA,GAAkB,CAAC,KAAA,EAAe,QAAuB,KAAA;AAC7D,MAAA,IAAI,CAAC,KAAM,CAAA,OAAA;AACT,QAAM,MAAA,IAAI,MAAM,+CAA+C,CAAA,CAAA;AACjE,MAAM,KAAA,CAAA,KAAA,CAAM,eAAgB,CAAA,KAAA,EAAO,QAAQ,CAAA,CAAA;AAAA,KAC7C,CAAA;AAEA,IAAM,MAAA,cAAA,GAAiB,CAAC,IAAA,EAAiB,QAAuB,KAAA;AAC9D,MAAA,IAAI,CAAC,KAAM,CAAA,OAAA;AACT,QAAM,MAAA,IAAI,MAAM,8CAA8C,CAAA,CAAA;AAChE,MAAM,KAAA,CAAA,KAAA,CAAM,cAAe,CAAA,IAAA,EAAM,QAAQ,CAAA,CAAA;AAAA,KAC3C,CAAA;AAEA,IAAA,MAAM,UAAa,GAAA,CACjB,IACA,EAAA,OAAA,EACA,IACG,KAAA;AACH,MAAA,KAAA,CAAM,KAAM,CAAA,UAAA,CAAW,IAAM,EAAA,OAAA,EAAS,IAAI,CAAA,CAAA;AAAA,KAC5C,CAAA;AAEA,IAAA,MAAM,sBAAsB,MAAsB;AAChD,MAAO,OAAA,KAAA,CAAM,MAAM,mBAAoB,EAAA,CAAA;AAAA,KACzC,CAAA;AAEA,IAAA,MAAM,qBAAqB,MAAiB;AAC1C,MAAO,OAAA,KAAA,CAAM,MAAM,kBAAmB,EAAA,CAAA;AAAA,KACxC,CAAA;AAEA,IAAA,MAAM,cAAiB,GAAA,CAAC,IAAY,EAAA,sBAAA,GAAyB,IAAS,KAAA;AACpE,MAAA,IAAI,CAAC,KAAM,CAAA,OAAA;AACT,QAAM,MAAA,IAAI,MAAM,8CAA8C,CAAA,CAAA;AAEhE,MAAoB,mBAAA,CAAA,KAAA,EAAO,IAAI,IAAM,EAAA,MACnC,MAAM,KAAM,CAAA,kBAAA,CAAmB,IAAM,EAAA,sBAAsB,CAC7D,CAAA,CAAA;AAAA,KACF,CAAA;AAEA,IAAA,MAAM,aAAgB,GAAA,CAAC,GAAe,EAAA,sBAAA,GAAyB,IAAS,KAAA;AACtE,MAAA,IAAI,CAAC,KAAM,CAAA,OAAA;AACT,QAAM,MAAA,IAAI,MAAM,6CAA6C,CAAA,CAAA;AAE/D,MAAoB,mBAAA,CAAA,KAAA,EAAO,IAAI,IAAM,EAAA,MACnC,MAAM,KAAM,CAAA,iBAAA,CAAkB,GAAK,EAAA,sBAAsB,CAC3D,CAAA,CAAA;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,OAAA,GAAU,CAAC,IAAuC,KAAA;AACtD,MAAO,OAAA,KAAA,CAAM,KAAM,CAAA,OAAA,CAAQ,IAAI,CAAA,CAAA;AAAA,KACjC,CAAA;AAEA,IAAM,MAAA,MAAA,GAAS,CAAC,IAA8B,KAAA;AAC5C,MAAM,KAAA,CAAA,KAAA,CAAM,OAAO,IAAI,CAAA,CAAA;AAAA,KACzB,CAAA;AAEA,IAAM,MAAA,MAAA,GAAS,CACb,IAAA,EACA,UACG,KAAA;AACH,MAAM,KAAA,CAAA,KAAA,CAAM,MAAO,CAAA,IAAA,EAAM,UAAU,CAAA,CAAA;AAAA,KACrC,CAAA;AAEA,IAAM,MAAA,YAAA,GAAe,CACnB,IAAA,EACA,OACG,KAAA;AACH,MAAM,KAAA,CAAA,KAAA,CAAM,YAAa,CAAA,IAAA,EAAM,OAAO,CAAA,CAAA;AAAA,KACxC,CAAA;AAEA,IAAM,MAAA,WAAA,GAAc,CAClB,IAAA,EACA,OACG,KAAA;AACH,MAAM,KAAA,CAAA,KAAA,CAAM,WAAY,CAAA,IAAA,EAAM,OAAO,CAAA,CAAA;AAAA,KACvC,CAAA;AAEA,IAAA,MAAM,gBAAmB,GAAA,CACvB,QACA,EAAA,IAAA,EACA,QACG,KAAA;AACH,MAAA,iBAAA,CAAkB,IAAI,CAAA,CAAA;AACtB,MAAA,GAAA,CAAI,IAAK,CAAA,aAAA,EAAe,QAAU,EAAA,IAAA,EAAM,QAAQ,CAAA,CAAA;AAAA,KAClD,CAAA;AAEA,IAAM,MAAA,iBAAA,GAAoB,CAAC,GAAA,EAAc,IAAmB,KAAA;AAC1D,MAAA,IAAI,CAAC,KAAM,CAAA,OAAA;AACT,QAAM,MAAA,IAAI,MAAM,8CAA8C,CAAA,CAAA;AAChE,MAAM,KAAA,CAAA,KAAA,CAAM,cAAe,CAAA,GAAA,EAAK,IAAI,CAAA,CAAA;AAAA,KACtC,CAAA;AAEA,IAAA,OAAA,CAAQ,UAAY,EAAA;AAAA,MAClB,GAAA;AAAA,MACA,KAAA;AAAA,MACA,KAAA;AAAA,MACA,IAAA;AAAA,MACA,WAAA;AAAA,MACA,UAAU,kBAAmB,EAAA;AAAA,KACvB,CAAA,CAAA;AAER,IAAA,OAAA,CAAQ,oBAAoB,KAAS,CAAA,CAAA,CAAA;AAErC,IAAO,OAAA;AAAA,MACL,EAAA;AAAA,MAEA,KAAA;AAAA,MACA,IAAA;AAAA,MACA,WAAA;AAAA,MACA,SAAA;AAAA,MACA,GAAA;AAAA,MACA,cAAA;AAAA,MAGA,OAAA;AAAA,MAGA,MAAA;AAAA,kBACAD,YAAA;AAAA,MACA,WAAA;AAAA,MACA,eAAA;AAAA,MACA,cAAA;AAAA,MACA,cAAA;AAAA,MACA,aAAA;AAAA,MACA,eAAA;AAAA,MACA,cAAA;AAAA,MACA,UAAA;AAAA,MACA,mBAAA;AAAA,MACA,kBAAA;AAAA,MACA,cAAA;AAAA,MACA,aAAA;AAAA,MACA,CAAA;AAAA,MACA,OAAA;AAAA,MACA,MAAA;AAAA,MACA,MAAA;AAAA,MACA,YAAA;AAAA,MACA,WAAA;AAAA,MACA,gBAAA;AAAA,MACA,iBAAA;AAAA,KACF,CAAA;AAAA,GACF;AACF,CAAC,CAAA,CAAA;;;sBA5aCE,kBAkCM,CAAA,KAAA,EAAA;AAAA,IAjCJ,GAAI,EAAA,KAAA;AAAA,IACH,KAAK,EAAAC,cAAA,CAAA;AAAA,MAAU,QAAG,CAAC,EAAA;AAAA,MAAU,IAAG,CAAA,EAAA,CAAA,EAAA,CAAE,UAAe,EAAA,CAAA,CAAA,IAAA,CAAA,SAAA,CAAU,YAAY,CAAA;AAAA,MAAS,IAAG,CAAA,EAAA,CAAA,EAAA,CAAE,gBAAoB,EAAA,CAAA,IAAA,CAAA,SAAA,CAAU,SAAS,CAAA;AAAA,MAAS,IAAG,CAAA,EAAA,CAAA,EAAA,CAAE,YAAe,EAAA,IAAA,CAAA,SAAA,CAAU,QAAQ,KAAA,OAAA,CAAA;AAAA,MAAwB,EAAA,CAAA,IAAA,CAAA,EAAA,CAAG,CAAC,CAAA,mBAAA,CAAA,GAAwB,IAAgB,CAAA,gBAAA,EAAA;AAAA,KAAA,CAAA;IAOhP,IAAK,EAAA,MAAA;AAAA,GAAA,EAAA;AAEL,KAAAC,SAAA,CAAA,IAAA,CAAA,EAAAF,kBAAA,CAUEG,QATgB,EAAA,IAAA,EAAAC,UAAA,CAAA,IAAA,CAAA,IAAA,CAAK,UAAU,EAAA,CAAxB,KAAK,KAAA;0BADdC,WAUE,CAAA,uBAAA,EAAA;AAAA,QARC,GAAA,EAAK,gBAAW,KAAK,CAAA;AAAA,QACrB,IAAM,EAAA,KAAA;AAAA,QACN,KAAO,EAAA,IAAA,CAAA,KAAA;AAAA,QACP,SAAW,EAAA,IAAA,CAAA,SAAA;AAAA,QACX,qBAAqB,EAAA,IAAA,CAAA,iBAAA;AAAA,QACrB,eAAe,EAAA,IAAA,CAAA,YAAA;AAAA,QACf,gBAAgB,EAAA,IAAA,CAAA,aAAA;AAAA,QAChB,YAAa,EAAA,IAAA,CAAA,gBAAA;AAAA,OAAA,EAAA,IAAA,EAAA,CAAA,EAAA,CAAA,MAAA,EAAA,OAAA,EAAA,WAAA,EAAA,qBAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,cAAA,CAAA,CAAA,CAAA;;AAEL,IAAA,IAAA,CAAA,OAAA,IAAAH,SAAA,EAAA,EAAXF,kBAMM,CAAA,KAAA,EAAA;AAAA,MAAA,GAAA,EAAA,CAAA;MANe,KAAK,EAAAC,cAAA,CAAE,QAAG,CAAC,CAAA,aAAA,CAAA,CAAA;AAAA,KAAA,EAAA;AAC9B,MAAAK,UAAA,CAIO,0BAJP,MAIO;AAAA,QAHL,IAEO,EAAA,CAAA;AAAA,QAFA,OAAA;AAAW,UAAAC;AACC,YAAA,KAAA,EAAAN,cAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA;;;AAQrB,OAFI,CAAA;AAAA,KACH,EAAA,CAAA,CAAA,IAAKO,kBAAE,CAAA,MAAI,EAAA,IAAA,CAAA;AAAA,IAAAC,cAAA,CAAAF,kBAAA,CAAA,KAAA,EAAA;AAFJ,MAAA,GAAA,EAAA,gBAAA;AAA2B,MAAA,KAAA,EAAAN,cAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,gBAAA,CAAA,CAAA;;;;;;;;;;"}