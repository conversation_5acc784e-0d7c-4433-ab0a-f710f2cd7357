{"version": 3, "file": "tree-node.mjs", "sources": ["../../../../../../packages/components/tree/src/tree-node.vue"], "sourcesContent": ["<template>\n  <div\n    v-show=\"node.visible\"\n    ref=\"node$\"\n    :class=\"[\n      ns.b('node'),\n      ns.is('expanded', expanded),\n      ns.is('current', node.isCurrent),\n      ns.is('hidden', !node.visible),\n      ns.is('focusable', !node.disabled),\n      ns.is('checked', !node.disabled && node.checked),\n      getNodeClass(node),\n    ]\"\n    role=\"treeitem\"\n    tabindex=\"-1\"\n    :aria-expanded=\"expanded\"\n    :aria-disabled=\"node.disabled\"\n    :aria-checked=\"node.checked\"\n    :draggable=\"tree.props.draggable\"\n    :data-key=\"getNodeKey(node)\"\n    @click.stop=\"handleClick\"\n    @contextmenu=\"handleContextMenu\"\n    @dragstart.stop=\"handleDragStart\"\n    @dragover.stop=\"handleDragOver\"\n    @dragend.stop=\"handleDragEnd\"\n    @drop.stop=\"handleDrop\"\n  >\n    <div\n      :class=\"ns.be('node', 'content')\"\n      :style=\"{ paddingLeft: (node.level - 1) * tree.props.indent + 'px' }\"\n    >\n      <el-icon\n        v-if=\"tree.props.icon || CaretRight\"\n        :class=\"[\n          ns.be('node', 'expand-icon'),\n          ns.is('leaf', node.isLeaf),\n          {\n            expanded: !node.isLeaf && expanded,\n          },\n        ]\"\n        @click.stop=\"handleExpandIconClick\"\n      >\n        <component :is=\"tree.props.icon || CaretRight\" />\n      </el-icon>\n      <el-checkbox\n        v-if=\"showCheckbox\"\n        :model-value=\"node.checked\"\n        :indeterminate=\"node.indeterminate\"\n        :disabled=\"!!node.disabled\"\n        @click.stop\n        @change=\"handleCheckChange\"\n      />\n      <el-icon\n        v-if=\"node.loading\"\n        :class=\"[ns.be('node', 'loading-icon'), ns.is('loading')]\"\n      >\n        <loading />\n      </el-icon>\n      <node-content :node=\"node\" :render-content=\"renderContent\" />\n    </div>\n    <el-collapse-transition>\n      <div\n        v-if=\"!renderAfterExpand || childNodeRendered\"\n        v-show=\"expanded\"\n        :class=\"ns.be('node', 'children')\"\n        role=\"group\"\n        :aria-expanded=\"expanded\"\n      >\n        <el-tree-node\n          v-for=\"child in node.childNodes\"\n          :key=\"getNodeKey(child)\"\n          :render-content=\"renderContent\"\n          :render-after-expand=\"renderAfterExpand\"\n          :show-checkbox=\"showCheckbox\"\n          :node=\"child\"\n          :accordion=\"accordion\"\n          :props=\"props\"\n          @node-expand=\"handleChildNodeExpand\"\n        />\n      </div>\n    </el-collapse-transition>\n  </div>\n</template>\n<script lang=\"ts\">\n// @ts-nocheck\nimport {\n  defineComponent,\n  getCurrentInstance,\n  inject,\n  nextTick,\n  provide,\n  ref,\n  watch,\n} from 'vue'\nimport { isFunction, isString } from '@vue/shared'\nimport ElCollapseTransition from '@element-plus/components/collapse-transition'\nimport ElCheckbox from '@element-plus/components/checkbox'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { CaretRight, Loading } from '@element-plus/icons-vue'\nimport { debugWarn } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport NodeContent from './tree-node-content.vue'\nimport { getNodeKey as getNodeKeyUtil, handleCurrentChange } from './model/util'\nimport { useNodeExpandEventBroadcast } from './model/useNodeExpandEventBroadcast'\nimport { dragEventsKey } from './model/useDragNode'\nimport Node from './model/node'\n\nimport type { ComponentInternalInstance, PropType } from 'vue'\nimport type { Nullable } from '@element-plus/utils'\nimport type { RootTreeType, TreeNodeData, TreeOptionProps } from './tree.type'\n\nexport default defineComponent({\n  name: 'ElTreeNode',\n  components: {\n    ElCollapseTransition,\n    ElCheckbox,\n    NodeContent,\n    ElIcon,\n    Loading,\n  },\n  props: {\n    node: {\n      type: Node,\n      default: () => ({}),\n    },\n    props: {\n      type: Object as PropType<TreeOptionProps>,\n      default: () => ({}),\n    },\n    accordion: Boolean,\n    renderContent: Function,\n    renderAfterExpand: Boolean,\n    showCheckbox: {\n      type: Boolean,\n      default: false,\n    },\n  },\n  emits: ['node-expand'],\n  setup(props, ctx) {\n    const ns = useNamespace('tree')\n    const { broadcastExpanded } = useNodeExpandEventBroadcast(props)\n    const tree = inject<RootTreeType>('RootTree')\n    const expanded = ref(false)\n    const childNodeRendered = ref(false)\n    const oldChecked = ref<boolean>(null)\n    const oldIndeterminate = ref<boolean>(null)\n    const node$ = ref<Nullable<HTMLElement>>(null)\n    const dragEvents = inject(dragEventsKey)\n    const instance = getCurrentInstance()\n\n    provide('NodeInstance', instance)\n    if (!tree) {\n      debugWarn('Tree', \"Can not find node's tree.\")\n    }\n\n    if (props.node.expanded) {\n      expanded.value = true\n      childNodeRendered.value = true\n    }\n\n    const childrenKey = tree.props.props['children'] || 'children'\n    watch(\n      () => {\n        const children = props.node.data[childrenKey]\n        return children && [...children]\n      },\n      () => {\n        props.node.updateChildren()\n      }\n    )\n\n    watch(\n      () => props.node.indeterminate,\n      (val) => {\n        handleSelectChange(props.node.checked, val)\n      }\n    )\n\n    watch(\n      () => props.node.checked,\n      (val) => {\n        handleSelectChange(val, props.node.indeterminate)\n      }\n    )\n\n    watch(\n      () => props.node.expanded,\n      (val) => {\n        nextTick(() => (expanded.value = val))\n        if (val) {\n          childNodeRendered.value = true\n        }\n      }\n    )\n\n    const getNodeKey = (node: Node): any => {\n      return getNodeKeyUtil(tree.props.nodeKey, node.data)\n    }\n\n    const getNodeClass = (node: Node) => {\n      const nodeClassFunc = props.props.class\n      if (!nodeClassFunc) {\n        return {}\n      }\n      let className\n      if (isFunction(nodeClassFunc)) {\n        const { data } = node\n        className = nodeClassFunc(data, node)\n      } else {\n        className = nodeClassFunc\n      }\n\n      if (isString(className)) {\n        return { [className]: true }\n      } else {\n        return className\n      }\n    }\n\n    const handleSelectChange = (checked: boolean, indeterminate: boolean) => {\n      if (\n        oldChecked.value !== checked ||\n        oldIndeterminate.value !== indeterminate\n      ) {\n        tree.ctx.emit('check-change', props.node.data, checked, indeterminate)\n      }\n      oldChecked.value = checked\n      oldIndeterminate.value = indeterminate\n    }\n\n    const handleClick = (e: MouseEvent) => {\n      handleCurrentChange(tree.store, tree.ctx.emit, () =>\n        tree.store.value.setCurrentNode(props.node)\n      )\n      tree.currentNode.value = props.node\n\n      if (tree.props.expandOnClickNode) {\n        handleExpandIconClick()\n      }\n\n      if (tree.props.checkOnClickNode && !props.node.disabled) {\n        handleCheckChange(null, {\n          target: { checked: !props.node.checked },\n        })\n      }\n      tree.ctx.emit('node-click', props.node.data, props.node, instance, e)\n    }\n\n    const handleContextMenu = (event: Event) => {\n      if (tree.instance.vnode.props['onNodeContextmenu']) {\n        event.stopPropagation()\n        event.preventDefault()\n      }\n      tree.ctx.emit(\n        'node-contextmenu',\n        event,\n        props.node.data,\n        props.node,\n        instance\n      )\n    }\n\n    const handleExpandIconClick = () => {\n      if (props.node.isLeaf) return\n      if (expanded.value) {\n        tree.ctx.emit('node-collapse', props.node.data, props.node, instance)\n        props.node.collapse()\n      } else {\n        props.node.expand()\n        ctx.emit('node-expand', props.node.data, props.node, instance)\n      }\n    }\n\n    const handleCheckChange = (value, ev) => {\n      props.node.setChecked(ev.target.checked, !tree.props.checkStrictly)\n      nextTick(() => {\n        const store = tree.store.value\n        tree.ctx.emit('check', props.node.data, {\n          checkedNodes: store.getCheckedNodes(),\n          checkedKeys: store.getCheckedKeys(),\n          halfCheckedNodes: store.getHalfCheckedNodes(),\n          halfCheckedKeys: store.getHalfCheckedKeys(),\n        })\n      })\n    }\n\n    const handleChildNodeExpand = (\n      nodeData: TreeNodeData,\n      node: Node,\n      instance: ComponentInternalInstance\n    ) => {\n      broadcastExpanded(node)\n      tree.ctx.emit('node-expand', nodeData, node, instance)\n    }\n\n    const handleDragStart = (event: DragEvent) => {\n      if (!tree.props.draggable) return\n      dragEvents.treeNodeDragStart({ event, treeNode: props })\n    }\n\n    const handleDragOver = (event: DragEvent) => {\n      event.preventDefault()\n      if (!tree.props.draggable) return\n      dragEvents.treeNodeDragOver({\n        event,\n        treeNode: { $el: node$.value, node: props.node },\n      })\n    }\n\n    const handleDrop = (event: DragEvent) => {\n      event.preventDefault()\n    }\n\n    const handleDragEnd = (event: DragEvent) => {\n      if (!tree.props.draggable) return\n      dragEvents.treeNodeDragEnd(event)\n    }\n\n    return {\n      ns,\n      node$,\n      tree,\n      expanded,\n      childNodeRendered,\n      oldChecked,\n      oldIndeterminate,\n      getNodeKey,\n      getNodeClass,\n      handleSelectChange,\n      handleClick,\n      handleContextMenu,\n      handleExpandIconClick,\n      handleCheckChange,\n      handleChildNodeExpand,\n      handleDragStart,\n      handleDragOver,\n      handleDrop,\n      handleDragEnd,\n      CaretRight,\n    }\n  },\n})\n</script>\n"], "names": ["ElCollapseTransition", "getNodeKey", "getNodeKeyUtil", "_createElementBlock", "_normalizeClass", "_createElementVNode", "_normalizeStyle", "_openBlock", "_createBlock", "_withModifiers", "_resolveDynamicComponent", "_createCommentVNode", "_createVNode", "_withCtx", "_Fragment", "_renderList", "_vShow"], "mappings": ";;;;;;;;;;;;;;;;;AA+GA,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,YAAA;AAAA,EACN,UAAY,EAAA;AAAA,0BACVA,mBAAA;AAAA,IACA,UAAA;AAAA,IACA,WAAA;AAAA,IACA,MAAA;AAAA,IACA,OAAA;AAAA,GACF;AAAA,EACA,KAAO,EAAA;AAAA,IACL,IAAM,EAAA;AAAA,MACJ,IAAM,EAAA,IAAA;AAAA,MACN,OAAA,EAAS,OAAQ,EAAA,CAAA;AAAA,KACnB;AAAA,IACA,KAAO,EAAA;AAAA,MACL,IAAM,EAAA,MAAA;AAAA,MACN,OAAA,EAAS,OAAQ,EAAA,CAAA;AAAA,KACnB;AAAA,IACA,SAAW,EAAA,OAAA;AAAA,IACX,aAAe,EAAA,QAAA;AAAA,IACf,iBAAmB,EAAA,OAAA;AAAA,IACnB,YAAc,EAAA;AAAA,MACZ,IAAM,EAAA,OAAA;AAAA,MACN,OAAS,EAAA,KAAA;AAAA,KACX;AAAA,GACF;AAAA,EACA,KAAA,EAAO,CAAC,aAAa,CAAA;AAAA,EACrB,KAAA,CAAM,OAAO,GAAK,EAAA;AAChB,IAAM,MAAA,EAAA,GAAK,aAAa,MAAM,CAAA,CAAA;AAC9B,IAAM,MAAA,EAAE,iBAAsB,EAAA,GAAA,2BAAA,CAA4B,KAAK,CAAA,CAAA;AAC/D,IAAM,MAAA,IAAA,GAAO,OAAqB,UAAU,CAAA,CAAA;AAC5C,IAAM,MAAA,QAAA,GAAW,IAAI,KAAK,CAAA,CAAA;AAC1B,IAAM,MAAA,iBAAA,GAAoB,IAAI,KAAK,CAAA,CAAA;AACnC,IAAM,MAAA,UAAA,GAAa,IAAa,IAAI,CAAA,CAAA;AACpC,IAAM,MAAA,gBAAA,GAAmB,IAAa,IAAI,CAAA,CAAA;AAC1C,IAAM,MAAA,KAAA,GAAQ,IAA2B,IAAI,CAAA,CAAA;AAC7C,IAAM,MAAA,UAAA,GAAa,OAAO,aAAa,CAAA,CAAA;AACvC,IAAA,MAAM,WAAW,kBAAmB,EAAA,CAAA;AAEpC,IAAA,OAAA,CAAQ,gBAAgB,QAAQ,CAAA,CAAA;AAChC,IAAA,IAAI,CAAC,IAAM,EAAA;AACT,MAAA,SAAA,CAAU,QAAQ,2BAA2B,CAAA,CAAA;AAAA,KAC/C;AAEA,IAAI,IAAA,KAAA,CAAM,KAAK,QAAU,EAAA;AACvB,MAAA,QAAA,CAAS,KAAQ,GAAA,IAAA,CAAA;AACjB,MAAA,iBAAA,CAAkB,KAAQ,GAAA,IAAA,CAAA;AAAA,KAC5B;AAEA,IAAA,MAAM,WAAc,GAAA,IAAA,CAAK,KAAM,CAAA,KAAA,CAAM,UAAe,CAAA,IAAA,UAAA,CAAA;AACpD,IAAA,KAAA,CACE,MAAM;AACJ,MAAM,MAAA,QAAA,GAAW,KAAM,CAAA,IAAA,CAAK,IAAK,CAAA,WAAA,CAAA,CAAA;AACjC,MAAO,OAAA,QAAA,IAAY,CAAC,GAAG,QAAQ,CAAA,CAAA;AAAA,OAEjC,MAAM;AACJ,MAAA,KAAA,CAAM,KAAK,cAAe,EAAA,CAAA;AAAA,KAE9B,CAAA,CAAA;AAEA,IAAA,KAAA,CACE,MAAM,KAAA,CAAM,IAAK,CAAA,aAAA,EACjB,CAAC,GAAQ,KAAA;AACP,MAAmB,kBAAA,CAAA,KAAA,CAAM,IAAK,CAAA,OAAA,EAAS,GAAG,CAAA,CAAA;AAAA,KAE9C,CAAA,CAAA;AAEA,IAAA,KAAA,CACE,MAAM,KAAA,CAAM,IAAK,CAAA,OAAA,EACjB,CAAC,GAAQ,KAAA;AACP,MAAmB,kBAAA,CAAA,GAAA,EAAK,KAAM,CAAA,IAAA,CAAK,aAAa,CAAA,CAAA;AAAA,KAEpD,CAAA,CAAA;AAEA,IAAA,KAAA,CACE,MAAM,KAAA,CAAM,IAAK,CAAA,QAAA,EACjB,CAAC,GAAQ,KAAA;AACP,MAAS,QAAA,CAAA,MAAO,QAAS,CAAA,KAAA,GAAQ,GAAI,CAAA,CAAA;AACrC,MAAA,IAAI,GAAK,EAAA;AACP,QAAA,iBAAA,CAAkB,KAAQ,GAAA,IAAA,CAAA;AAAA,OAC5B;AAAA,KAEJ,CAAA,CAAA;AAEA,IAAM,MAAAC,YAAA,GAAa,CAAC,IAAoB,KAAA;AACtC,MAAA,OAAOC,UAAe,CAAA,IAAA,CAAK,KAAM,CAAA,OAAA,EAAS,KAAK,IAAI,CAAA,CAAA;AAAA,KACrD,CAAA;AAEA,IAAM,MAAA,YAAA,GAAe,CAAC,IAAe,KAAA;AACnC,MAAM,MAAA,aAAA,GAAgB,MAAM,KAAM,CAAA,KAAA,CAAA;AAClC,MAAA,IAAI,CAAC,aAAe,EAAA;AAClB,QAAA,OAAO,EAAC,CAAA;AAAA,OACV;AACA,MAAI,IAAA,SAAA,CAAA;AACJ,MAAI,IAAA,UAAA,CAAW,aAAa,CAAG,EAAA;AAC7B,QAAA,MAAM,EAAE,IAAS,EAAA,GAAA,IAAA,CAAA;AACjB,QAAY,SAAA,GAAA,aAAA,CAAc,MAAM,IAAI,CAAA,CAAA;AAAA,OAC/B,MAAA;AACL,QAAY,SAAA,GAAA,aAAA,CAAA;AAAA,OACd;AAEA,MAAI,IAAA,QAAA,CAAS,SAAS,CAAG,EAAA;AACvB,QAAO,OAAA,EAAE,CAAC,SAAA,GAAY,IAAK,EAAA,CAAA;AAAA,OACtB,MAAA;AACL,QAAO,OAAA,SAAA,CAAA;AAAA,OACT;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,kBAAA,GAAqB,CAAC,OAAA,EAAkB,aAA2B,KAAA;AACvE,MAAA,IACE,UAAW,CAAA,KAAA,KAAU,OACrB,IAAA,gBAAA,CAAiB,UAAU,aAC3B,EAAA;AACA,QAAA,IAAA,CAAK,IAAI,IAAK,CAAA,cAAA,EAAgB,MAAM,IAAK,CAAA,IAAA,EAAM,SAAS,aAAa,CAAA,CAAA;AAAA,OACvE;AACA,MAAA,UAAA,CAAW,KAAQ,GAAA,OAAA,CAAA;AACnB,MAAA,gBAAA,CAAiB,KAAQ,GAAA,aAAA,CAAA;AAAA,KAC3B,CAAA;AAEA,IAAM,MAAA,WAAA,GAAc,CAAC,CAAkB,KAAA;AACrC,MAAA,mBAAA,CAAoB,IAAK,CAAA,KAAA,EAAO,IAAK,CAAA,GAAA,CAAI,IAAM,EAAA,MAC7C,IAAK,CAAA,KAAA,CAAM,KAAM,CAAA,cAAA,CAAe,KAAM,CAAA,IAAI,CAC5C,CAAA,CAAA;AACA,MAAK,IAAA,CAAA,WAAA,CAAY,QAAQ,KAAM,CAAA,IAAA,CAAA;AAE/B,MAAI,IAAA,IAAA,CAAK,MAAM,iBAAmB,EAAA;AAChC,QAAsB,qBAAA,EAAA,CAAA;AAAA,OACxB;AAEA,MAAA,IAAI,KAAK,KAAM,CAAA,gBAAA,IAAoB,CAAC,KAAA,CAAM,KAAK,QAAU,EAAA;AACvD,QAAA,iBAAA,CAAkB,IAAM,EAAA;AAAA,UACtB,QAAQ,EAAE,OAAA,EAAS,CAAC,KAAA,CAAM,KAAK,OAAQ,EAAA;AAAA,SACxC,CAAA,CAAA;AAAA,OACH;AACA,MAAK,IAAA,CAAA,GAAA,CAAI,KAAK,YAAc,EAAA,KAAA,CAAM,KAAK,IAAM,EAAA,KAAA,CAAM,IAAM,EAAA,QAAA,EAAU,CAAC,CAAA,CAAA;AAAA,KACtE,CAAA;AAEA,IAAM,MAAA,iBAAA,GAAoB,CAAC,KAAiB,KAAA;AAC1C,MAAA,IAAI,IAAK,CAAA,QAAA,CAAS,KAAM,CAAA,KAAA,CAAM,mBAAsB,CAAA,EAAA;AAClD,QAAA,KAAA,CAAM,eAAgB,EAAA,CAAA;AACtB,QAAA,KAAA,CAAM,cAAe,EAAA,CAAA;AAAA,OACvB;AACA,MAAK,IAAA,CAAA,GAAA,CAAI,KACP,kBACA,EAAA,KAAA,EACA,MAAM,IAAK,CAAA,IAAA,EACX,KAAM,CAAA,IAAA,EACN,QACF,CAAA,CAAA;AAAA,KACF,CAAA;AAEA,IAAA,MAAM,wBAAwB,MAAM;AAClC,MAAA,IAAI,MAAM,IAAK,CAAA,MAAA;AAAQ,QAAA,OAAA;AACvB,MAAA,IAAI,SAAS,KAAO,EAAA;AAClB,QAAK,IAAA,CAAA,GAAA,CAAI,KAAK,eAAiB,EAAA,KAAA,CAAM,KAAK,IAAM,EAAA,KAAA,CAAM,MAAM,QAAQ,CAAA,CAAA;AACpE,QAAA,KAAA,CAAM,KAAK,QAAS,EAAA,CAAA;AAAA,OACf,MAAA;AACL,QAAA,KAAA,CAAM,KAAK,MAAO,EAAA,CAAA;AAClB,QAAA,GAAA,CAAI,KAAK,aAAe,EAAA,KAAA,CAAM,KAAK,IAAM,EAAA,KAAA,CAAM,MAAM,QAAQ,CAAA,CAAA;AAAA,OAC/D;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,iBAAA,GAAoB,CAAC,KAAA,EAAO,EAAO,KAAA;AACvC,MAAM,KAAA,CAAA,IAAA,CAAK,WAAW,EAAG,CAAA,MAAA,CAAO,SAAS,CAAC,IAAA,CAAK,MAAM,aAAa,CAAA,CAAA;AAClE,MAAA,QAAA,CAAS,MAAM;AACb,QAAM,MAAA,KAAA,GAAQ,KAAK,KAAM,CAAA,KAAA,CAAA;AACzB,QAAA,IAAA,CAAK,GAAI,CAAA,IAAA,CAAK,OAAS,EAAA,KAAA,CAAM,KAAK,IAAM,EAAA;AAAA,UACtC,YAAA,EAAc,MAAM,eAAgB,EAAA;AAAA,UACpC,WAAA,EAAa,MAAM,cAAe,EAAA;AAAA,UAClC,gBAAA,EAAkB,MAAM,mBAAoB,EAAA;AAAA,UAC5C,eAAA,EAAiB,MAAM,kBAAmB,EAAA;AAAA,SAC3C,CAAA,CAAA;AAAA,OACF,CAAA,CAAA;AAAA,KACH,CAAA;AAEA,IAAA,MAAM,qBAAwB,GAAA,CAC5B,QACA,EAAA,IAAA,EACA,SACG,KAAA;AACH,MAAA,iBAAA,CAAkB,IAAI,CAAA,CAAA;AACtB,MAAA,IAAA,CAAK,GAAI,CAAA,IAAA,CAAK,aAAe,EAAA,QAAA,EAAU,MAAM,SAAQ,CAAA,CAAA;AAAA,KACvD,CAAA;AAEA,IAAM,MAAA,eAAA,GAAkB,CAAC,KAAqB,KAAA;AAC5C,MAAI,IAAA,CAAC,KAAK,KAAM,CAAA,SAAA;AAAW,QAAA,OAAA;AAC3B,MAAA,UAAA,CAAW,iBAAkB,CAAA,EAAE,KAAO,EAAA,QAAA,EAAU,OAAO,CAAA,CAAA;AAAA,KACzD,CAAA;AAEA,IAAM,MAAA,cAAA,GAAiB,CAAC,KAAqB,KAAA;AAC3C,MAAA,KAAA,CAAM,cAAe,EAAA,CAAA;AACrB,MAAI,IAAA,CAAC,KAAK,KAAM,CAAA,SAAA;AAAW,QAAA,OAAA;AAC3B,MAAA,UAAA,CAAW,gBAAiB,CAAA;AAAA,QAC1B,KAAA;AAAA,QACA,UAAU,EAAE,GAAA,EAAK,MAAM,KAAO,EAAA,IAAA,EAAM,MAAM,IAAK,EAAA;AAAA,OAChD,CAAA,CAAA;AAAA,KACH,CAAA;AAEA,IAAM,MAAA,UAAA,GAAa,CAAC,KAAqB,KAAA;AACvC,MAAA,KAAA,CAAM,cAAe,EAAA,CAAA;AAAA,KACvB,CAAA;AAEA,IAAM,MAAA,aAAA,GAAgB,CAAC,KAAqB,KAAA;AAC1C,MAAI,IAAA,CAAC,KAAK,KAAM,CAAA,SAAA;AAAW,QAAA,OAAA;AAC3B,MAAA,UAAA,CAAW,gBAAgB,KAAK,CAAA,CAAA;AAAA,KAClC,CAAA;AAEA,IAAO,OAAA;AAAA,MACL,EAAA;AAAA,MACA,KAAA;AAAA,MACA,IAAA;AAAA,MACA,QAAA;AAAA,MACA,iBAAA;AAAA,MACA,UAAA;AAAA,MACA,gBAAA;AAAA,kBACAD,YAAA;AAAA,MACA,YAAA;AAAA,MACA,kBAAA;AAAA,MACA,WAAA;AAAA,MACA,iBAAA;AAAA,MACA,qBAAA;AAAA,MACA,iBAAA;AAAA,MACA,qBAAA;AAAA,MACA,eAAA;AAAA,MACA,cAAA;AAAA,MACA,UAAA;AAAA,MACA,aAAA;AAAA,MACA,UAAA;AAAA,KACF,CAAA;AAAA,GACF;AACF,CAAC,CAAA,CAAA;;;;;;;;;;sCApVCE,kBAgFM,CAAA,KAAA,EAAA;AAAA,IA9EJ,GAAI,EAAA,OAAA;AAAA,IACH,KAAK,EAAAC,cAAA,CAAA;AAAA,MAAU,QAAG,CAAC,CAAA,MAAA,CAAA;AAAA,MAAgB,IAAA,CAAA,EAAA,CAAG,EAAE,CAAA,UAAA,EAAa,IAAQ,CAAA,QAAA,CAAA;AAAA,MAAS,IAAG,CAAA,EAAA,CAAA,EAAA,CAAE,SAAY,EAAA,IAAA,CAAA,IAAA,CAAK,SAAS,CAAA;AAAA,MAAS,IAAG,CAAA,EAAA,CAAA,EAAA,CAAE,QAAY,EAAA,CAAA,IAAA,CAAA,IAAA,CAAK,OAAO,CAAA;AAAA,MAAS,IAAG,CAAA,EAAA,CAAA,EAAA,CAAE,WAAe,EAAA,CAAA,IAAA,CAAA,IAAA,CAAK,QAAQ,CAAA;AAAA,MAAS,QAAG,EAAE,CAAA,SAAA,EAAA,CAAa,IAAK,CAAA,IAAA,CAAA,QAAA,IAAY,UAAK,OAAO,CAAA;AAAA,MAAS,kBAAa,IAAI,CAAA,IAAA,CAAA;AAAA,KAAA,CAAA;IASxQ,IAAK,EAAA,UAAA;AAAA,IACL,QAAS,EAAA,IAAA;AAAA,IACR,eAAe,EAAA,IAAA,CAAA,QAAA;AAAA,IACf,iBAAe,IAAK,CAAA,IAAA,CAAA,QAAA;AAAA,IACpB,gBAAc,IAAK,CAAA,IAAA,CAAA,OAAA;AAAA,IACnB,SAAA,EAAW,UAAK,KAAM,CAAA,SAAA;AAAA,IACtB,UAAA,EAAU,gBAAW,IAAI,CAAA,IAAA,CAAA;AAAA,IACzB,OAAA,EAAK,qDAAO,IAAW,CAAA,WAAA,IAAA,IAAA,CAAA,WAAA,CAAA,GAAA,IAAA,CAAA,EAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,IACvB,eAAW,MAAE,CAAA,CAAA,CAAA,KAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,GAAA,IAAA,KAAA,IAAA,CAAA,iBAAA,IAAA,IAAA,CAAA,iBAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AAAA,IACb,WAAA,EAAS,qDAAO,IAAe,CAAA,eAAA,IAAA,IAAA,CAAA,eAAA,CAAA,GAAA,IAAA,CAAA,EAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,IAC/B,UAAA,EAAQ,qDAAO,IAAc,CAAA,cAAA,IAAA,IAAA,CAAA,cAAA,CAAA,GAAA,IAAA,CAAA,EAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,IAC7B,SAAA,EAAO,qDAAO,IAAa,CAAA,aAAA,IAAA,IAAA,CAAA,aAAA,CAAA,GAAA,IAAA,CAAA,EAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,IAC3B,MAAA,EAAI,qDAAO,IAAU,CAAA,UAAA,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAA,CAAA,EAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,GAAA,EAAA;IAEtBC,kBAgCM,CAAA,KAAA,EAAA;AAAA,MA/BH,KAAA,EAAKD,cAAE,CAAA,IAAA,CAAA,EAAA,CAAG,EAAE,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA;AAAA,MACZ,OAAKE,cAAkB,CAAA,EAAA,WAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAK,QAAK,CAAQ,IAAA,IAAA,CAAA,IAAA,CAAK,MAAM,MAAM,GAAA,IAAA,EAAA,CAAA;AAAA,KAAA,EAAA;MAGnD,IAAK,CAAA,IAAA,CAAA,KAAA,CAAM,IAAQ,IAAA,IAAA,CAAA,UAAA,IAAAC,SAAA,EAAA,EAD3BC,WAYU,CAAA,kBAAA,EAAA;AAAA,QAAA,GAAA,EAAA,CAAA;QAVP,KAAK,EAAAJ,cAAA,CAAA;AAAA,UAAc,QAAG,EAAE,CAAA,MAAA,EAAA,aAAA,CAAA;AAAA,UAAmC,IAAG,CAAA,EAAA,CAAA,EAAA,CAAE,MAAS,EAAA,IAAA,CAAA,IAAA,CAAK,MAAM,CAAA;AAAA,UAAA;AAAsC,YAAA,QAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAK,MAAU,IAAA,IAAA,CAAA,QAAA;AAAA,WAAA;;AAOzI,QAAA,OAAA,EAAKK,cAAO,IAAqB,CAAA,qBAAA,EAAA,CAAA,MAAA,CAAA,CAAA;AAAA,OAAA,EAAA;yBAElC,MAAiD;AAAA,WAAAF,SAAA,EAAA,EAAjDC,WAAiD,CAAAE,uBAAA,CAAjC,IAAK,CAAA,IAAA,CAAA,KAAA,CAAM,QAAQ,IAAU,CAAA,UAAA,CAAA,CAAA;AAAA,SAAA,CAAA;;;AAGvC,MAAA,IAAA,CAAA,YAAA,IAAAH,SAAA,EAAA,EADRC,WAOE,CAAA,sBAAA,EAAA;AAAA,QAAA,GAAA,EAAA,CAAA;AALC,QAAA,aAAA,EAAa,IAAK,CAAA,IAAA,CAAA,OAAA;AAAA,QAClB,eAAe,IAAK,CAAA,IAAA,CAAA,aAAA;AAAA,QACpB,QAAA,EAAQ,EAAI,IAAK,CAAA,IAAA,CAAA,QAAA;AAAA,QACjB,SAAK,MAAN,CAAA,CAAA,CAAA,KAAA,MAAA,CAAA,CAAA,CAAA,GAAAC,aAAA,CAAA,MAAA;AAAA,SAAW,EAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,QACV,QAAQ,EAAA,IAAA,CAAA,iBAAA;AAAA,OAAA,EAAA,IAAA,EAAA,CAAA,EAAA,CAAA,aAAA,EAAA,eAAA,EAAA,UAAA,EAAA,UAAA,CAAA,CAAA,IAAAE,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAGH,MAAA,IAAA,CAAA,IAAA,CAAK,wBADbH,WAKU,CAAA,kBAAA,EAAA;AAAA,QAAA,GAAA,EAAA,CAAA;AAHP,QAAA,KAAA,EAAKJ,cAAG,CAAA,CAAA,IAAA,CAAA,EAAA,CAAG,EAAE,CAAA,MAAA,EAAA,cAAA,CAAA,EAA0B,QAAG,EAAE,CAAA,SAAA,CAAA,CAAA,CAAA;AAAA,OAAA,EAAA;yBAE7C,MAAW;AAAA,UAAXQ,WAAW,CAAA,kBAAA,CAAA;AAAA,SAAA,CAAA;;;MAEbA,WAA6D,CAAA,uBAAA,EAAA;AAAA,QAA9C,IAAM,EAAA,IAAA,CAAA,IAAA;AAAA,QAAO,gBAAgB,EAAA,IAAA,CAAA,aAAA;AAAA,OAAA,EAAA,IAAA,EAAA,CAAA,EAAA,CAAA,MAAA,EAAA,gBAAA,CAAA,CAAA;;IAE9CA,WAoByB,CAAA,iCAAA,EAAA,IAAA,EAAA;AAAA,MAAA,OAAA,EAAAC,OAAA,CAnBvB,MAkBM;AAAA,QAjBG,CAAA,IAAA,CAAA,iBAAA,IAAqB,sDAD9BV,kBAkBM,CAAA,KAAA,EAAA;AAAA,UAAA,GAAA,EAAA,CAAA;UAfH,KAAK,EAAAC,cAAA,CAAE,QAAG,EAAE,CAAA,MAAA,EAAA,UAAA,CAAA,CAAA;AAAA,UACb,IAAK,EAAA,OAAA;AAAA,UACJ,eAAe,EAAA,IAAA,CAAA,QAAA;AAAA,SAAA,EAAA;AAEhB,WAAAG,SAAA,CAAA,IAAA,CAAA,EAAAJ,kBAAA,CAUEW,QATgB,EAAA,IAAA,EAAAC,UAAA,CAAA,IAAA,CAAA,IAAA,CAAK,UAAU,EAAA,CAAxB,KAAK,KAAA;gCADdP,WAUE,CAAA,uBAAA,EAAA;AAAA,cARC,GAAA,EAAK,gBAAW,KAAK,CAAA;AAAA,cACrB,gBAAgB,EAAA,IAAA,CAAA,aAAA;AAAA,cAChB,qBAAqB,EAAA,IAAA,CAAA,iBAAA;AAAA,cACrB,eAAe,EAAA,IAAA,CAAA,YAAA;AAAA,cACf,IAAM,EAAA,KAAA;AAAA,cACN,SAAW,EAAA,IAAA,CAAA,SAAA;AAAA,cACX,KAAO,EAAA,IAAA,CAAA,KAAA;AAAA,cACP,YAAa,EAAA,IAAA,CAAA,qBAAA;AAAA,aAAA,EAAA,IAAA,EAAA,CAAA,EAAA,CAAA,gBAAA,EAAA,qBAAA,EAAA,eAAA,EAAA,MAAA,EAAA,WAAA,EAAA,OAAA,EAAA,cAAA,CAAA,CAAA,CAAA;;;kBAdR,IAAQ,CAAA,QAAA,CAAA;AAAA,SAAA,CAAA,GAAAG,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;;;;;AA7DZ,IAAA,CAAAK,KAAA,EAAA,IAAA,CAAA,IAAA,CAAK,OAAO,CAAA;AAAA,GAAA,CAAA,CAAA;;;;;;"}