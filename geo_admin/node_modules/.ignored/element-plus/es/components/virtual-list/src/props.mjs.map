{"version": 3, "file": "props.mjs", "sources": ["../../../../../../packages/components/virtual-list/src/props.ts"], "sourcesContent": ["import {\n  buildProp,\n  buildProps,\n  definePropType,\n  mutable,\n} from '@element-plus/utils'\nimport { VERTICAL } from './defaults'\n\nimport type { ExtractPropTypes, StyleValue } from 'vue'\nimport type { GridItemKeyGetter, ItemSize } from './types'\n\nconst itemSize = buildProp({\n  type: definePropType<number | ItemSize>([Number, Function]),\n  required: true,\n} as const)\n\nconst estimatedItemSize = buildProp({\n  type: Number,\n} as const)\n\nconst cache = buildProp({\n  type: Number,\n  default: 2,\n} as const)\n\nconst direction = buildProp({\n  type: String,\n  values: ['ltr', 'rtl'],\n  default: 'ltr',\n} as const)\n\nconst initScrollOffset = buildProp({\n  type: Number,\n  default: 0,\n} as const)\n\nconst total = buildProp({\n  type: Number,\n  required: true,\n} as const)\n\nconst layout = buildProp({\n  type: String,\n  values: ['horizontal', 'vertical'],\n  default: VERTICAL,\n} as const)\n\nexport const virtualizedProps = buildProps({\n  className: {\n    type: String,\n    default: '',\n  },\n\n  containerElement: {\n    type: definePropType<string | Element>([String, Object]),\n    default: 'div',\n  },\n\n  data: {\n    type: definePropType<any[]>(Array),\n    default: () => mutable([] as const),\n  },\n\n  /**\n   * @description controls the horizontal direction.\n   */\n  direction,\n\n  height: {\n    type: [String, Number],\n    required: true,\n  },\n\n  innerElement: {\n    type: [String, Object],\n    default: 'div',\n  },\n\n  style: {\n    type: definePropType<StyleValue>([Object, String, Array]),\n  },\n\n  useIsScrolling: {\n    type: Boolean,\n    default: false,\n  },\n\n  width: {\n    type: [Number, String],\n    required: false,\n  },\n\n  perfMode: {\n    type: Boolean,\n    default: true,\n  },\n  scrollbarAlwaysOn: {\n    type: Boolean,\n    default: false,\n  },\n} as const)\n\nexport const virtualizedListProps = buildProps({\n  /**\n   * @description describes how many items should be pre rendered to the head\n   * and the tail of the window\n   */\n  cache,\n\n  estimatedItemSize,\n\n  /**\n   * @description controls the list's orientation\n   */\n  layout,\n\n  initScrollOffset,\n\n  /**\n   * @description describes the total number of the list.\n   */\n  total,\n\n  itemSize,\n  ...virtualizedProps,\n} as const)\n\nconst scrollbarSize = {\n  type: Number,\n  default: 6,\n} as const\n\nconst startGap = { type: Number, default: 0 } as const\nconst endGap = { type: Number, default: 2 } as const\n\nexport const virtualizedGridProps = buildProps({\n  columnCache: cache,\n  columnWidth: itemSize,\n  estimatedColumnWidth: estimatedItemSize,\n  estimatedRowHeight: estimatedItemSize,\n  initScrollLeft: initScrollOffset,\n  initScrollTop: initScrollOffset,\n  itemKey: {\n    type: definePropType<GridItemKeyGetter>(Function),\n    default: ({\n      columnIndex,\n      rowIndex,\n    }: {\n      columnIndex: number\n      rowIndex: number\n    }) => `${rowIndex}:${columnIndex}`,\n  },\n  rowCache: cache,\n  rowHeight: itemSize,\n  totalColumn: total,\n  totalRow: total,\n  hScrollbarSize: scrollbarSize,\n  vScrollbarSize: scrollbarSize,\n  scrollbarStartGap: startGap,\n  scrollbarEndGap: endGap,\n  role: String,\n  ...virtualizedProps,\n} as const)\n\nexport const virtualizedScrollbarProps = buildProps({\n  alwaysOn: Boolean,\n  class: String,\n  layout,\n  total,\n  ratio: {\n    type: Number,\n    required: true,\n  },\n  clientSize: {\n    type: Number,\n    required: true,\n  },\n  scrollFrom: {\n    type: Number,\n    required: true,\n  },\n  scrollbarSize,\n  startGap,\n  endGap,\n\n  visible: Boolean,\n} as const)\n\nexport type VirtualizedProps = ExtractPropTypes<typeof virtualizedProps>\nexport type VirtualizedListProps = ExtractPropTypes<typeof virtualizedListProps>\nexport type VirtualizedGridProps = ExtractPropTypes<typeof virtualizedGridProps>\n\nexport type VirtualizedScrollbarProps = ExtractPropTypes<\n  typeof virtualizedScrollbarProps\n>\n"], "names": [], "mappings": ";;;;;AAOA,MAAM,QAAQ,GAAG,SAAS,CAAC;AAC3B,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC1C,EAAE,QAAQ,EAAE,IAAI;AAChB,CAAC,CAAC,CAAC;AACH,MAAM,iBAAiB,GAAG,SAAS,CAAC;AACpC,EAAE,IAAI,EAAE,MAAM;AACd,CAAC,CAAC,CAAC;AACH,MAAM,KAAK,GAAG,SAAS,CAAC;AACxB,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC,CAAC;AACH,MAAM,SAAS,GAAG,SAAS,CAAC;AAC5B,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;AACxB,EAAE,OAAO,EAAE,KAAK;AAChB,CAAC,CAAC,CAAC;AACH,MAAM,gBAAgB,GAAG,SAAS,CAAC;AACnC,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC,CAAC;AACH,MAAM,KAAK,GAAG,SAAS,CAAC;AACxB,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,QAAQ,EAAE,IAAI;AAChB,CAAC,CAAC,CAAC;AACH,MAAM,MAAM,GAAG,SAAS,CAAC;AACzB,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,MAAM,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC;AACpC,EAAE,OAAO,EAAE,QAAQ;AACnB,CAAC,CAAC,CAAC;AACS,MAAC,gBAAgB,GAAG,UAAU,CAAC;AAC3C,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,gBAAgB,EAAE;AACpB,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC;AAC9B,GAAG;AACH,EAAE,SAAS;AACX,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AACjD,GAAG;AACH,EAAE,cAAc,EAAE;AAClB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,QAAQ,EAAE,KAAK;AACnB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,iBAAiB,EAAE;AACrB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,oBAAoB,GAAG,UAAU,CAAC;AAC/C,EAAE,KAAK;AACP,EAAE,iBAAiB;AACnB,EAAE,MAAM;AACR,EAAE,gBAAgB;AAClB,EAAE,KAAK;AACP,EAAE,QAAQ;AACV,EAAE,GAAG,gBAAgB;AACrB,CAAC,EAAE;AACH,MAAM,aAAa,GAAG;AACtB,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC;AACF,MAAM,QAAQ,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;AAC9C,MAAM,MAAM,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;AAChC,MAAC,oBAAoB,GAAG,UAAU,CAAC;AAC/C,EAAE,WAAW,EAAE,KAAK;AACpB,EAAE,WAAW,EAAE,QAAQ;AACvB,EAAE,oBAAoB,EAAE,iBAAiB;AACzC,EAAE,kBAAkB,EAAE,iBAAiB;AACvC,EAAE,cAAc,EAAE,gBAAgB;AAClC,EAAE,aAAa,EAAE,gBAAgB;AACjC,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,IAAI,OAAO,EAAE,CAAC;AACd,MAAM,WAAW;AACjB,MAAM,QAAQ;AACd,KAAK,KAAK,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;AACtC,GAAG;AACH,EAAE,QAAQ,EAAE,KAAK;AACjB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,WAAW,EAAE,KAAK;AACpB,EAAE,QAAQ,EAAE,KAAK;AACjB,EAAE,cAAc,EAAE,aAAa;AAC/B,EAAE,cAAc,EAAE,aAAa;AAC/B,EAAE,iBAAiB,EAAE,QAAQ;AAC7B,EAAE,eAAe,EAAE,MAAM;AACzB,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,GAAG,gBAAgB;AACrB,CAAC,EAAE;AACS,MAAC,yBAAyB,GAAG,UAAU,CAAC;AACpD,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,MAAM;AACR,EAAE,KAAK;AACP,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,aAAa;AACf,EAAE,QAAQ;AACV,EAAE,MAAM;AACR,EAAE,OAAO,EAAE,OAAO;AAClB,CAAC;;;;"}