export declare const ElSpace: import("element-plus/es/utils").SFCWithInstall<import("vue").DefineComponent<{
    readonly direction: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "horizontal" | "vertical", unknown, "horizontal", boolean>;
    readonly class: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("element-plus/es/utils").Arrayable<string | Record<string, boolean>> & {}) | (() => import("element-plus/es/utils").Arrayable<string | Record<string, boolean>>) | ((new (...args: any[]) => import("element-plus/es/utils").Arrayable<string | Record<string, boolean>> & {}) | (() => import("element-plus/es/utils").Arrayable<string | Record<string, boolean>>))[], unknown, unknown, "", boolean>;
    readonly style: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("vue").StyleValue & {}) | (() => import("vue").StyleValue) | ((new (...args: any[]) => import("vue").StyleValue & {}) | (() => import("vue").StyleValue))[], unknown, unknown, "", boolean>;
    readonly alignment: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => string & {}) | (() => string) | ((new (...args: any[]) => string & {}) | (() => string))[], unknown, unknown, "center", boolean>;
    readonly prefixCls: {
        readonly type: import("vue").PropType<string>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly spacer: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("vue").VNodeChild & {}) | (() => import("vue").VNodeChild) | ((new (...args: any[]) => import("vue").VNodeChild & {}) | (() => import("vue").VNodeChild))[], unknown, unknown, null, boolean>;
    readonly wrap: BooleanConstructor;
    readonly fill: BooleanConstructor;
    readonly fillRatio: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 100, boolean>;
    readonly size: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, ArrayConstructor, NumberConstructor], "" | "default" | "small" | "large", number | [number, number]>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
}, () => string | import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}> | {
    [name: string]: unknown;
    $stable?: boolean | undefined;
} | null, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    readonly direction: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "horizontal" | "vertical", unknown, "horizontal", boolean>;
    readonly class: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("element-plus/es/utils").Arrayable<string | Record<string, boolean>> & {}) | (() => import("element-plus/es/utils").Arrayable<string | Record<string, boolean>>) | ((new (...args: any[]) => import("element-plus/es/utils").Arrayable<string | Record<string, boolean>> & {}) | (() => import("element-plus/es/utils").Arrayable<string | Record<string, boolean>>))[], unknown, unknown, "", boolean>;
    readonly style: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("vue").StyleValue & {}) | (() => import("vue").StyleValue) | ((new (...args: any[]) => import("vue").StyleValue & {}) | (() => import("vue").StyleValue))[], unknown, unknown, "", boolean>;
    readonly alignment: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => string & {}) | (() => string) | ((new (...args: any[]) => string & {}) | (() => string))[], unknown, unknown, "center", boolean>;
    readonly prefixCls: {
        readonly type: import("vue").PropType<string>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly spacer: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("vue").VNodeChild & {}) | (() => import("vue").VNodeChild) | ((new (...args: any[]) => import("vue").VNodeChild & {}) | (() => import("vue").VNodeChild))[], unknown, unknown, null, boolean>;
    readonly wrap: BooleanConstructor;
    readonly fill: BooleanConstructor;
    readonly fillRatio: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 100, boolean>;
    readonly size: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, ArrayConstructor, NumberConstructor], "" | "default" | "small" | "large", number | [number, number]>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
}>>, {
    readonly fill: boolean;
    readonly style: import("vue").StyleValue;
    readonly class: import("element-plus/es/utils").Arrayable<string | Record<string, boolean>>;
    readonly wrap: boolean;
    readonly direction: import("element-plus/es/utils").EpPropMergeType<StringConstructor, "horizontal" | "vertical", unknown>;
    readonly alignment: string;
    readonly spacer: import("vue").VNodeChild;
    readonly fillRatio: number;
}>> & Record<string, any>;
export default ElSpace;
export * from './src/space';
export * from './src/item';
export * from './src/use-space';
