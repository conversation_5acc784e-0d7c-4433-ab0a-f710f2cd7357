{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/space/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport Space from './src/space'\n\nexport const ElSpace = withInstall(Space)\nexport default ElSpace\n\nexport * from './src/space'\nexport * from './src/item'\nexport * from './src/use-space'\n"], "names": [], "mappings": ";;;;;;;AAEY,MAAC,OAAO,GAAG,WAAW,CAAC,KAAK;;;;"}