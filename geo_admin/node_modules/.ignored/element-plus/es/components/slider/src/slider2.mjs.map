{"version": 3, "file": "slider2.mjs", "sources": ["../../../../../../packages/components/slider/src/slider.vue"], "sourcesContent": ["<template>\n  <div\n    :id=\"range ? inputId : undefined\"\n    ref=\"sliderWrapper\"\n    :class=\"sliderKls\"\n    :role=\"range ? 'group' : undefined\"\n    :aria-label=\"range && !isLabeledByFormItem ? groupLabel : undefined\"\n    :aria-labelledby=\"\n      range && isLabeledByFormItem ? elFormItem?.labelId : undefined\n    \"\n    @touchstart=\"onSliderWrapperPrevent\"\n    @touchmove=\"onSliderWrapperPrevent\"\n  >\n    <div\n      ref=\"slider\"\n      :class=\"[\n        ns.e('runway'),\n        { 'show-input': showInput && !range },\n        ns.is('disabled', sliderDisabled),\n      ]\"\n      :style=\"runwayStyle\"\n      @mousedown=\"onSliderDown\"\n      @touchstart=\"onSliderDown\"\n    >\n      <div :class=\"ns.e('bar')\" :style=\"barStyle\" />\n      <slider-button\n        :id=\"!range ? inputId : undefined\"\n        ref=\"firstButton\"\n        :model-value=\"firstValue\"\n        :vertical=\"vertical\"\n        :tooltip-class=\"tooltipClass\"\n        :placement=\"placement\"\n        role=\"slider\"\n        :aria-label=\"\n          range || !isLabeledByFormItem ? firstButtonLabel : undefined\n        \"\n        :aria-labelledby=\"\n          !range && isLabeledByFormItem ? elFormItem?.labelId : undefined\n        \"\n        :aria-valuemin=\"min\"\n        :aria-valuemax=\"range ? secondValue : max\"\n        :aria-valuenow=\"firstValue\"\n        :aria-valuetext=\"firstValueText\"\n        :aria-orientation=\"vertical ? 'vertical' : 'horizontal'\"\n        :aria-disabled=\"sliderDisabled\"\n        @update:model-value=\"setFirstValue\"\n      />\n      <slider-button\n        v-if=\"range\"\n        ref=\"secondButton\"\n        :model-value=\"secondValue\"\n        :vertical=\"vertical\"\n        :tooltip-class=\"tooltipClass\"\n        :placement=\"placement\"\n        role=\"slider\"\n        :aria-label=\"secondButtonLabel\"\n        :aria-valuemin=\"firstValue\"\n        :aria-valuemax=\"max\"\n        :aria-valuenow=\"secondValue\"\n        :aria-valuetext=\"secondValueText\"\n        :aria-orientation=\"vertical ? 'vertical' : 'horizontal'\"\n        :aria-disabled=\"sliderDisabled\"\n        @update:model-value=\"setSecondValue\"\n      />\n      <div v-if=\"showStops\">\n        <div\n          v-for=\"(item, key) in stops\"\n          :key=\"key\"\n          :class=\"ns.e('stop')\"\n          :style=\"getStopStyle(item)\"\n        />\n      </div>\n      <template v-if=\"markList.length > 0\">\n        <div>\n          <div\n            v-for=\"(item, key) in markList\"\n            :key=\"key\"\n            :style=\"getStopStyle(item.position)\"\n            :class=\"[ns.e('stop'), ns.e('marks-stop')]\"\n          />\n        </div>\n        <div :class=\"ns.e('marks')\">\n          <slider-marker\n            v-for=\"(item, key) in markList\"\n            :key=\"key\"\n            :mark=\"item.mark\"\n            :style=\"getStopStyle(item.position)\"\n          />\n        </div>\n      </template>\n    </div>\n    <el-input-number\n      v-if=\"showInput && !range\"\n      ref=\"input\"\n      :model-value=\"firstValue\"\n      :class=\"ns.e('input')\"\n      :step=\"step\"\n      :disabled=\"sliderDisabled\"\n      :controls=\"showInputControls\"\n      :min=\"min\"\n      :max=\"max\"\n      :debounce=\"debounce\"\n      :size=\"sliderInputSize\"\n      @update:model-value=\"setFirstValue\"\n      @change=\"emitChange\"\n    />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, provide, reactive, toRefs } from 'vue'\nimport ElInputNumber from '@element-plus/components/input-number'\nimport { useFormItemInputId, useFormSize } from '@element-plus/components/form'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { sliderContextKey } from './constants'\nimport { sliderEmits, sliderProps } from './slider'\nimport SliderButton from './button.vue'\nimport SliderMarker from './marker'\nimport {\n  useLifecycle,\n  useMarks,\n  useSlide,\n  useStops,\n  useWatch,\n} from './composables'\nimport type { SliderInitData } from './slider'\n\ndefineOptions({\n  name: 'ElSlider',\n})\n\nconst props = defineProps(sliderProps)\nconst emit = defineEmits(sliderEmits)\n\nconst ns = useNamespace('slider')\nconst { t } = useLocale()\n\nconst initData = reactive<SliderInitData>({\n  firstValue: 0,\n  secondValue: 0,\n  oldValue: 0,\n  dragging: false,\n  sliderSize: 1,\n})\n\nconst {\n  elFormItem,\n  slider,\n  firstButton,\n  secondButton,\n  sliderDisabled,\n  minValue,\n  maxValue,\n  runwayStyle,\n  barStyle,\n  resetSize,\n  emitChange,\n  onSliderWrapperPrevent,\n  onSliderClick,\n  onSliderDown,\n  setFirstValue,\n  setSecondValue,\n} = useSlide(props, initData, emit)\n\nconst { stops, getStopStyle } = useStops(props, initData, minValue, maxValue)\n\nconst { inputId, isLabeledByFormItem } = useFormItemInputId(props, {\n  formItemContext: elFormItem,\n})\n\nconst sliderWrapperSize = useFormSize()\nconst sliderInputSize = computed(\n  () => props.inputSize || sliderWrapperSize.value\n)\n\nconst groupLabel = computed<string>(() => {\n  return (\n    props.label ||\n    t('el.slider.defaultLabel', {\n      min: props.min,\n      max: props.max,\n    })\n  )\n})\n\nconst firstButtonLabel = computed<string>(() => {\n  if (props.range) {\n    return props.rangeStartLabel || t('el.slider.defaultRangeStartLabel')\n  } else {\n    return groupLabel.value\n  }\n})\n\nconst firstValueText = computed<string>(() => {\n  return props.formatValueText\n    ? props.formatValueText(firstValue.value)\n    : `${firstValue.value}`\n})\n\nconst secondButtonLabel = computed<string>(() => {\n  return props.rangeEndLabel || t('el.slider.defaultRangeEndLabel')\n})\n\nconst secondValueText = computed<string>(() => {\n  return props.formatValueText\n    ? props.formatValueText(secondValue.value)\n    : `${secondValue.value}`\n})\n\nconst sliderKls = computed(() => [\n  ns.b(),\n  ns.m(sliderWrapperSize.value),\n  ns.is('vertical', props.vertical),\n  { [ns.m('with-input')]: props.showInput },\n])\n\nconst markList = useMarks(props)\n\nuseWatch(props, initData, minValue, maxValue, emit, elFormItem!)\n\nconst precision = computed(() => {\n  const precisions = [props.min, props.max, props.step].map((item) => {\n    const decimal = `${item}`.split('.')[1]\n    return decimal ? decimal.length : 0\n  })\n  return Math.max.apply(null, precisions)\n})\n\nconst { sliderWrapper } = useLifecycle(props, initData, resetSize)\n\nconst { firstValue, secondValue, sliderSize } = toRefs(initData)\n\nconst updateDragging = (val: boolean) => {\n  initData.dragging = val\n}\n\nprovide(sliderContextKey, {\n  ...toRefs(props),\n  sliderSize,\n  disabled: sliderDisabled,\n  precision,\n  emitChange,\n  resetSize,\n  updateDragging,\n})\n\ndefineExpose({\n  onSliderClick,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;mCA+Hc,CAAA;AAAA,EACZ,IAAM,EAAA,UAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA,EAAA,GAAK,aAAa,QAAQ,CAAA,CAAA;AAChC,IAAM,MAAA,EAAE,MAAM,SAAU,EAAA,CAAA;AAExB,IAAA,MAAM,WAAW,QAAyB,CAAA;AAAA,MACxC,UAAY,EAAA,CAAA;AAAA,MACZ,WAAa,EAAA,CAAA;AAAA,MACb,QAAU,EAAA,CAAA;AAAA,MACV,QAAU,EAAA,KAAA;AAAA,MACV,UAAY,EAAA,CAAA;AAAA,KACb,CAAA,CAAA;AAED,IAAM,MAAA;AAAA,MACJ,UAAA;AAAA,MACA,MAAA;AAAA,MACA,WAAA;AAAA,MACA,YAAA;AAAA,MACA,cAAA;AAAA,MACA,QAAA;AAAA,MACA,QAAA;AAAA,MACA,WAAA;AAAA,MACA,QAAA;AAAA,MACA,SAAA;AAAA,MACA,UAAA;AAAA,MACA,sBAAA;AAAA,MACA,aAAA;AAAA,MACA,YAAA;AAAA,MACA,aAAA;AAAA,MACA,cAAA;AAAA,KACE,GAAA,QAAA,CAAS,KAAO,EAAA,QAAA,EAAU,IAAI,CAAA,CAAA;AAElC,IAAA,MAAM,EAAE,KAAO,EAAA,YAAA,EAAA,GAAiB,SAAS,KAAO,EAAA,QAAA,EAAU,UAAU,QAAQ,CAAA,CAAA;AAE5E,IAAA,MAAM,EAAE,OAAA,EAAS,mBAAwB,EAAA,GAAA,kBAAA,CAAmB,KAAO,EAAA;AAAA,MACjE,eAAiB,EAAA,UAAA;AAAA,KAClB,CAAA,CAAA;AAED,IAAA,MAAM,oBAAoB,WAAY,EAAA,CAAA;AACtC,IAAA,MAAM,kBAAkB,QACtB,CAAA,MAAM,KAAM,CAAA,SAAA,IAAa,kBAAkB,KAC7C,CAAA,CAAA;AAEA,IAAM,MAAA,UAAA,GAAa,SAAiB,MAAM;AACxC,MACE,OAAA,KAAA,CAAM,KACN,IAAA,CAAA,CAAE,wBAA0B,EAAA;AAAA,QAC1B,KAAK,KAAM,CAAA,GAAA;AAAA,QACX,KAAK,KAAM,CAAA,GAAA;AAAA,OACZ,CAAA,CAAA;AAAA,KAEJ,CAAA,CAAA;AAED,IAAM,MAAA,gBAAA,GAAmB,SAAiB,MAAM;AAC9C,MAAA,IAAI,MAAM,KAAO,EAAA;AACf,QAAO,OAAA,KAAA,CAAM,eAAmB,IAAA,CAAA,CAAE,kCAAkC,CAAA,CAAA;AAAA,OAC/D,MAAA;AACL,QAAA,OAAO,UAAW,CAAA,KAAA,CAAA;AAAA,OACpB;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,cAAA,GAAiB,SAAiB,MAAM;AAC5C,MAAO,OAAA,KAAA,CAAM,kBACT,KAAM,CAAA,eAAA,CAAgB,WAAW,KAAK,CAAA,GACtC,GAAG,UAAW,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA,KACnB,CAAA,CAAA;AAED,IAAM,MAAA,iBAAA,GAAoB,SAAiB,MAAM;AAC/C,MAAO,OAAA,KAAA,CAAM,aAAiB,IAAA,CAAA,CAAE,gCAAgC,CAAA,CAAA;AAAA,KACjE,CAAA,CAAA;AAED,IAAM,MAAA,eAAA,GAAkB,SAAiB,MAAM;AAC7C,MAAO,OAAA,KAAA,CAAM,kBACT,KAAM,CAAA,eAAA,CAAgB,YAAY,KAAK,CAAA,GACvC,GAAG,WAAY,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA,KACpB,CAAA,CAAA;AAED,IAAM,MAAA,SAAA,GAAY,SAAS,MAAM;AAAA,MAC/B,GAAG,CAAE,EAAA;AAAA,MACL,EAAA,CAAG,CAAE,CAAA,iBAAA,CAAkB,KAAK,CAAA;AAAA,MAC5B,EAAG,CAAA,EAAA,CAAG,UAAY,EAAA,KAAA,CAAM,QAAQ,CAAA;AAAA,MAChC,EAAE,CAAC,EAAA,CAAG,EAAE,YAAY,CAAA,GAAI,MAAM,SAAU,EAAA;AAAA,KACzC,CAAA,CAAA;AAED,IAAM,MAAA,QAAA,GAAW,SAAS,KAAK,CAAA,CAAA;AAE/B,IAAA,QAAA,CAAS,KAAO,EAAA,QAAA,EAAU,QAAU,EAAA,QAAA,EAAU,MAAM,UAAW,CAAA,CAAA;AAE/D,IAAM,MAAA,SAAA,GAAY,SAAS,MAAM;AAC/B,MAAM,MAAA,UAAA,GAAa,CAAC,KAAA,CAAM,GAAK,EAAA,KAAA,CAAM,GAAK,EAAA,KAAA,CAAM,IAAI,CAAA,CAAE,GAAI,CAAA,CAAC,IAAS,KAAA;AAClE,QAAA,MAAM,OAAU,GAAA,CAAA,EAAG,IAAO,CAAA,CAAA,CAAA,KAAA,CAAM,GAAG,CAAE,CAAA,CAAA,CAAA,CAAA;AACrC,QAAO,OAAA,OAAA,GAAU,QAAQ,MAAS,GAAA,CAAA,CAAA;AAAA,OACnC,CAAA,CAAA;AACD,MAAA,OAAO,IAAK,CAAA,GAAA,CAAI,KAAM,CAAA,IAAA,EAAM,UAAU,CAAA,CAAA;AAAA,KACvC,CAAA,CAAA;AAED,IAAA,MAAM,EAAE,aAAA,EAAA,GAAkB,YAAa,CAAA,KAAA,EAAO,UAAU,SAAS,CAAA,CAAA;AAEjE,IAAA,MAAM,EAAE,UAAA,EAAY,WAAa,EAAA,UAAA,EAAA,GAAe,OAAO,QAAQ,CAAA,CAAA;AAE/D,IAAM,MAAA,cAAA,GAAiB,CAAC,GAAiB,KAAA;AACvC,MAAA,QAAA,CAAS,QAAW,GAAA,GAAA,CAAA;AAAA,KACtB,CAAA;AAEA,IAAA,OAAA,CAAQ,gBAAkB,EAAA;AAAA,MACxB,GAAG,OAAO,KAAK,CAAA;AAAA,MACf,UAAA;AAAA,MACA,QAAU,EAAA,cAAA;AAAA,MACV,SAAA;AAAA,MACA,UAAA;AAAA,MACA,SAAA;AAAA,MACA,cAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAa,MAAA,CAAA;AAAA,MACX,aAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}