{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/slider/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport Slider from './src/slider.vue'\n\nexport const ElSlider = withInstall(Slider)\nexport default ElSlider\n\nexport * from './src/slider'\nexport * from './src/constants'\n"], "names": [], "mappings": ";;;;;;AAEY,MAAC,QAAQ,GAAG,WAAW,CAAC,MAAM;;;;"}