{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/select-v2/index.ts"], "sourcesContent": ["import Select from './src/select.vue'\n\nimport type { App } from 'vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nSelect.install = (app: App): void => {\n  app.component(Select.name, Select)\n}\n\nconst _Select = Select as SFCWithInstall<typeof Select>\n\nexport default _Select\nexport const ElSelectV2 = _Select\n\nexport * from './src/token'\n"], "names": [], "mappings": ";;;AACA,MAAM,CAAC,OAAO,GAAG,CAAC,GAAG,KAAK;AAC1B,EAAE,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AACrC,CAAC,CAAC;AACG,MAAC,OAAO,GAAG,OAAO;AAEX,MAAC,UAAU,GAAG;;;;"}