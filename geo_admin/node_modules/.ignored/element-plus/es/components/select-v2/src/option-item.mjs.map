{"version": 3, "file": "option-item.mjs", "sources": ["../../../../../../packages/components/select-v2/src/option-item.vue"], "sourcesContent": ["<template>\n  <li\n    :aria-selected=\"selected\"\n    :style=\"style\"\n    :class=\"[\n      ns.be('dropdown', 'option-item'),\n      ns.is('selected', selected),\n      ns.is('disabled', disabled),\n      ns.is('created', created),\n      { hover: hovering },\n    ]\"\n    @mouseenter=\"hoverItem\"\n    @click.stop=\"selectOptionClick\"\n  >\n    <slot :item=\"item\" :index=\"index\" :disabled=\"disabled\">\n      <span>{{ getLabel(item) }}</span>\n    </slot>\n  </li>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, inject } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useOption } from './useOption'\nimport { useProps } from './useProps'\nimport { OptionProps } from './defaults'\nimport { selectV2InjectionKey } from './token'\n\nexport default defineComponent({\n  props: OptionProps,\n  emits: ['select', 'hover'],\n  setup(props, { emit }) {\n    const select = inject(selectV2InjectionKey)!\n    const ns = useNamespace('select')\n    const { hoverItem, selectOptionClick } = useOption(props, { emit })\n    const { getLabel } = useProps(select.props)\n\n    return {\n      ns,\n      hoverItem,\n      selectOptionClick,\n      getLabel,\n    }\n  },\n})\n</script>\n"], "names": ["_createElementBlock", "_normalizeStyle", "_normalizeClass", "_renderSlot", "_createElementVNode", "_toDisplayString"], "mappings": ";;;;;;;;;AA4BA,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,KAAO,EAAA,WAAA;AAAA,EACP,KAAA,EAAO,CAAC,QAAA,EAAU,OAAO,CAAA;AAAA,EACzB,KAAA,CAAM,KAAO,EAAA,EAAE,IAAQ,EAAA,EAAA;AACrB,IAAM,MAAA,MAAA,GAAS,OAAO,oBAAoB,CAAA,CAAA;AAC1C,IAAM,MAAA,EAAA,GAAK,aAAa,QAAQ,CAAA,CAAA;AAChC,IAAA,MAAM,EAAE,SAAW,EAAA,iBAAA,EAAA,GAAsB,UAAU,KAAO,EAAA,EAAE,MAAM,CAAA,CAAA;AAClE,IAAA,MAAM,EAAE,QAAA,EAAA,GAAa,QAAS,CAAA,MAAA,CAAO,KAAK,CAAA,CAAA;AAE1C,IAAO,OAAA;AAAA,MACL,EAAA;AAAA,MACA,SAAA;AAAA,MACA,iBAAA;AAAA,MACA,QAAA;AAAA,KACF,CAAA;AAAA,GACF;AACF,CAAC,CAAA,CAAA;;;sBA3CCA,kBAgBK,CAAA,IAAA,EAAA;AAAA,IAfF,eAAe,EAAA,IAAA,CAAA,QAAA;AAAA,IACf,KAAA,EAAKC,eAAE,IAAK,CAAA,KAAA,CAAA;AAAA,IACZ,KAAK,EAAAC,cAAA,CAAA;AAAA,MAAU,QAAG,EAAE,CAAA,UAAA,EAAA,aAAA,CAAA;AAAA,MAAmC,IAAA,CAAA,EAAA,CAAG,EAAE,CAAA,UAAA,EAAa,IAAQ,CAAA,QAAA,CAAA;AAAA,MAAS,IAAA,CAAA,EAAA,CAAG,EAAE,CAAA,UAAA,EAAa,IAAQ,CAAA,QAAA,CAAA;AAAA,MAAS,IAAA,CAAA,EAAA,CAAG,EAAE,CAAA,SAAA,EAAY,IAAO,CAAA,OAAA,CAAA;AAAA,MAAA,EAAA,KAAA,EAAkB,IAAQ,CAAA,QAAA,EAAA;AAAA,KAAA,CAAA;AAO/K,IAAA,YAAA,EAAU,MAAE,CAAA,CAAA,CAAA,KAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,GAAA,IAAA,KAAA,IAAA,CAAA,SAAA,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AAAA,IACZ,OAAA,EAAK,qDAAO,IAAiB,CAAA,iBAAA,IAAA,IAAA,CAAA,iBAAA,CAAA,GAAA,IAAA,CAAA,EAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,GAAA,EAAA;IAE9BC,UAEO,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,EAAA;AAAA,MAFA,IAAM,EAAA,IAAA,CAAA,IAAA;AAAA,MAAO,KAAO,EAAA,IAAA,CAAA,KAAA;AAAA,MAAQ,QAAU,EAAA,IAAA,CAAA,QAAA;AAAA,KAAA,EAA7C,MAEO;AAAA,MADLC,kBAAA,CAAiC,MAAxB,EAAA,IAAA,EAAAC,eAAA,CAAA,IAAA,CAAA,QAAA,CAAS,IAAI,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA,CAAA;AAAA,KAAA,CAAA;;;;;;;"}