declare const _default: import("vue").DefineComponent<{
    data: {
        type: ArrayConstructor;
        required: true;
    };
    hoveringIndex: NumberConstructor;
    width: NumberConstructor;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    data: {
        type: ArrayConstructor;
        required: true;
    };
    hoveringIndex: NumberConstructor;
    width: NumberConstructor;
}>>, {}>;
export default _default;
