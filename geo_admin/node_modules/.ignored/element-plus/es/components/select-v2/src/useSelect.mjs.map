{"version": 3, "file": "useSelect.mjs", "sources": ["../../../../../../packages/components/select-v2/src/useSelect.ts"], "sourcesContent": ["// @ts-nocheck\nimport { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'\nimport { isArray, isFunction, isObject } from '@vue/shared'\nimport { get, isEqual, isNil, debounce as lodashDebounce } from 'lodash-unified'\nimport { useResizeObserver } from '@vueuse/core'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport {\n  ValidateComponentsMap,\n  debugWarn,\n  escapeStringRegexp,\n} from '@element-plus/utils'\nimport { useFormItem, useFormSize } from '@element-plus/components/form'\n\nimport { ArrowUp } from '@element-plus/icons-vue'\nimport { useAllowCreate } from './useAllowCreate'\nimport { useInput } from './useInput'\nimport { useProps } from './useProps'\n\nimport type { CSSProperties } from 'vue'\nimport type ElTooltip from '@element-plus/components/tooltip'\nimport type { Option, OptionType } from './select.types'\nimport type { ISelectProps } from './token'\n\nconst DEFAULT_INPUT_PLACEHOLDER = ''\nconst MINIMUM_INPUT_WIDTH = 11\nconst TAG_BASE_WIDTH = {\n  larget: 51,\n  default: 42,\n  small: 33,\n}\n\nconst useSelect = (props: ISelectProps, emit) => {\n  // inject\n  const { t } = useLocale()\n  const nsSelectV2 = useNamespace('select-v2')\n  const nsInput = useNamespace('input')\n  const { form: elForm, formItem: elFormItem } = useFormItem()\n  const { getLabel, getValue, getDisabled, getOptions } = useProps(props)\n\n  const states = reactive({\n    inputValue: DEFAULT_INPUT_PLACEHOLDER,\n    displayInputValue: DEFAULT_INPUT_PLACEHOLDER,\n    calculatedWidth: 0,\n    cachedPlaceholder: '',\n    cachedOptions: [] as Option[],\n    createdOptions: [] as Option[],\n    createdLabel: '',\n    createdSelected: false,\n    currentPlaceholder: '',\n    hoveringIndex: -1,\n    comboBoxHovering: false,\n    isOnComposition: false,\n    isSilentBlur: false,\n    isComposing: false,\n    inputLength: 20,\n    selectWidth: 200,\n    initialInputHeight: 0,\n    previousQuery: null,\n    previousValue: undefined,\n    query: '',\n    selectedLabel: '',\n    softFocus: false,\n    tagInMultiLine: false,\n  })\n\n  // data refs\n  const selectedIndex = ref(-1)\n  const popperSize = ref(-1)\n\n  // DOM & Component refs\n  const controlRef = ref(null)\n  const inputRef = ref(null) // el-input ref\n  const menuRef = ref(null)\n  const popper = ref<InstanceType<typeof ElTooltip> | null>(null)\n  const selectRef = ref(null)\n  const selectionRef = ref(null) // tags ref\n  const calculatorRef = ref<HTMLElement>(null)\n\n  // the controller of the expanded popup\n  const expanded = ref(false)\n\n  const selectDisabled = computed(() => props.disabled || elForm?.disabled)\n\n  const popupHeight = computed(() => {\n    const totalHeight = filteredOptions.value.length * props.itemHeight\n    return totalHeight > props.height ? props.height : totalHeight\n  })\n\n  const hasModelValue = computed(() => {\n    return !isNil(props.modelValue)\n  })\n\n  const showClearBtn = computed(() => {\n    const hasValue = props.multiple\n      ? Array.isArray(props.modelValue) && props.modelValue.length > 0\n      : hasModelValue.value\n\n    const criteria =\n      props.clearable &&\n      !selectDisabled.value &&\n      states.comboBoxHovering &&\n      hasValue\n    return criteria\n  })\n\n  const iconComponent = computed(() =>\n    props.remote && props.filterable ? '' : ArrowUp\n  )\n\n  const iconReverse = computed(\n    () => iconComponent.value && nsSelectV2.is('reverse', expanded.value)\n  )\n\n  const validateState = computed(() => elFormItem?.validateState || '')\n  const validateIcon = computed(\n    () => ValidateComponentsMap[validateState.value]\n  )\n\n  const debounce = computed(() => (props.remote ? 300 : 0))\n\n  // filteredOptions includes flatten the data into one dimensional array.\n  const emptyText = computed(() => {\n    const options = filteredOptions.value\n    if (props.loading) {\n      return props.loadingText || t('el.select.loading')\n    } else {\n      if (props.remote && states.inputValue === '' && options.length === 0)\n        return false\n      if (props.filterable && states.inputValue && options.length > 0) {\n        return props.noMatchText || t('el.select.noMatch')\n      }\n      if (options.length === 0) {\n        return props.noDataText || t('el.select.noData')\n      }\n    }\n    return null\n  })\n\n  const filteredOptions = computed(() => {\n    const isValidOption = (o: Option): boolean => {\n      // fill the conditions here.\n      const query = states.inputValue\n      // when query was given, we should test on the label see whether the label contains the given query\n      const regexp = new RegExp(escapeStringRegexp(query), 'i')\n      const containsQueryString = query ? regexp.test(getLabel(o) || '') : true\n      return containsQueryString\n    }\n    if (props.loading) {\n      return []\n    }\n\n    return [...props.options, ...states.createdOptions].reduce((all, item) => {\n      const options = getOptions(item)\n\n      if (isArray(options)) {\n        const filtered = options.filter(isValidOption)\n\n        if (filtered.length > 0) {\n          all.push(\n            {\n              label: getLabel(item),\n              isTitle: true,\n              type: 'Group',\n            },\n            ...filtered,\n            { type: 'Group' }\n          )\n        }\n      } else if (props.remote || isValidOption(item)) {\n        all.push(item)\n      }\n\n      return all\n    }, []) as OptionType[]\n  })\n\n  const filteredOptionsValueMap = computed(() => {\n    const valueMap = new Map()\n\n    filteredOptions.value.forEach((option, index) => {\n      valueMap.set(getValueKey(getValue(option)), { option, index })\n    })\n    return valueMap\n  })\n\n  const optionsAllDisabled = computed(() =>\n    filteredOptions.value.every((option) => getDisabled(option))\n  )\n\n  const selectSize = useFormSize()\n\n  const collapseTagSize = computed(() =>\n    'small' === selectSize.value ? 'small' : 'default'\n  )\n\n  const tagMaxWidth = computed(() => {\n    const select = selectionRef.value\n    const size = collapseTagSize.value || 'default'\n    const paddingLeft = select\n      ? Number.parseInt(getComputedStyle(select).paddingLeft)\n      : 0\n    const paddingRight = select\n      ? Number.parseInt(getComputedStyle(select).paddingRight)\n      : 0\n    return (\n      states.selectWidth - paddingRight - paddingLeft - TAG_BASE_WIDTH[size]\n    )\n  })\n\n  const calculatePopperSize = () => {\n    popperSize.value = selectRef.value?.offsetWidth || 200\n  }\n\n  const inputWrapperStyle = computed(() => {\n    return {\n      width: `${\n        states.calculatedWidth === 0\n          ? MINIMUM_INPUT_WIDTH\n          : Math.ceil(states.calculatedWidth) + MINIMUM_INPUT_WIDTH\n      }px`,\n    } as CSSProperties\n  })\n\n  const shouldShowPlaceholder = computed(() => {\n    if (isArray(props.modelValue)) {\n      return props.modelValue.length === 0 && !states.displayInputValue\n    }\n\n    // when it's not multiple mode, we only determine this flag based on filterable and expanded\n    // when filterable flag is true, which means we have input box on the screen\n    return props.filterable ? states.displayInputValue.length === 0 : true\n  })\n\n  const currentPlaceholder = computed(() => {\n    const _placeholder = props.placeholder || t('el.select.placeholder')\n    return props.multiple || isNil(props.modelValue)\n      ? _placeholder\n      : states.selectedLabel\n  })\n\n  // this obtains the actual popper DOM element.\n  const popperRef = computed(() => popper.value?.popperRef?.contentRef)\n\n  // the index with current value in options\n  const indexRef = computed<number>(() => {\n    if (props.multiple) {\n      const len = (props.modelValue as []).length\n      if (\n        (props.modelValue as Array<any>).length > 0 &&\n        filteredOptionsValueMap.value.has(props.modelValue[len - 1])\n      ) {\n        const { index } = filteredOptionsValueMap.value.get(\n          props.modelValue[len - 1]\n        )\n        return index\n      }\n    } else {\n      if (\n        props.modelValue &&\n        filteredOptionsValueMap.value.has(props.modelValue)\n      ) {\n        const { index } = filteredOptionsValueMap.value.get(props.modelValue)\n        return index\n      }\n    }\n    return -1\n  })\n\n  const dropdownMenuVisible = computed({\n    get() {\n      return expanded.value && emptyText.value !== false\n    },\n    set(val: boolean) {\n      expanded.value = val\n    },\n  })\n\n  const showTagList = computed(() =>\n    states.cachedOptions.slice(0, props.maxCollapseTags)\n  )\n\n  const collapseTagList = computed(() =>\n    states.cachedOptions.slice(props.maxCollapseTags)\n  )\n\n  // hooks\n  const {\n    createNewOption,\n    removeNewOption,\n    selectNewOption,\n    clearAllNewOption,\n  } = useAllowCreate(props, states)\n  const {\n    handleCompositionStart,\n    handleCompositionUpdate,\n    handleCompositionEnd,\n  } = useInput((e) => onInput(e))\n\n  // methods\n  const focusAndUpdatePopup = () => {\n    inputRef.value?.focus?.()\n    popper.value?.updatePopper()\n  }\n\n  const toggleMenu = () => {\n    if (props.automaticDropdown) return\n    if (!selectDisabled.value) {\n      if (states.isComposing) states.softFocus = true\n      return nextTick(() => {\n        expanded.value = !expanded.value\n        inputRef.value?.focus?.()\n      })\n    }\n  }\n\n  const onInputChange = () => {\n    if (props.filterable && states.inputValue !== states.selectedLabel) {\n      states.query = states.selectedLabel\n    }\n    handleQueryChange(states.inputValue)\n    return nextTick(() => {\n      createNewOption(states.inputValue)\n    })\n  }\n\n  const debouncedOnInputChange = lodashDebounce(onInputChange, debounce.value)\n\n  const handleQueryChange = (val: string) => {\n    if (states.previousQuery === val) {\n      return\n    }\n    states.previousQuery = val\n    if (props.filterable && isFunction(props.filterMethod)) {\n      props.filterMethod(val)\n    } else if (\n      props.filterable &&\n      props.remote &&\n      isFunction(props.remoteMethod)\n    ) {\n      props.remoteMethod(val)\n    }\n  }\n\n  const emitChange = (val: any | any[]) => {\n    if (!isEqual(props.modelValue, val)) {\n      emit(CHANGE_EVENT, val)\n    }\n  }\n\n  const update = (val: any) => {\n    emit(UPDATE_MODEL_EVENT, val)\n    emitChange(val)\n    states.previousValue = String(val)\n  }\n\n  const getValueIndex = (arr = [], value: unknown) => {\n    if (!isObject(value)) {\n      return arr.indexOf(value)\n    }\n    const valueKey = props.valueKey\n    let index = -1\n    arr.some((item, i) => {\n      if (get(item, valueKey) === get(value, valueKey)) {\n        index = i\n        return true\n      }\n      return false\n    })\n    return index\n  }\n\n  const getValueKey = (item: unknown) => {\n    return isObject(item) ? get(item, props.valueKey) : item\n  }\n\n  const resetInputHeight = () => {\n    return nextTick(() => {\n      if (!inputRef.value) return\n      const selection = selectionRef.value\n\n      selectRef.value.height = selection.offsetHeight\n      if (expanded.value && emptyText.value !== false) {\n        popper.value?.updatePopper?.()\n      }\n    })\n  }\n\n  const handleResize = () => {\n    resetInputWidth()\n    calculatePopperSize()\n    popper.value?.updatePopper?.()\n    if (props.multiple) {\n      return resetInputHeight()\n    }\n  }\n\n  const resetInputWidth = () => {\n    const select = selectionRef.value\n    if (select) {\n      states.selectWidth = select.getBoundingClientRect().width\n    }\n  }\n\n  const onSelect = (option: Option, idx: number, byClick = true) => {\n    if (props.multiple) {\n      let selectedOptions = (props.modelValue as any[]).slice()\n\n      const index = getValueIndex(selectedOptions, getValue(option))\n      if (index > -1) {\n        selectedOptions = [\n          ...selectedOptions.slice(0, index),\n          ...selectedOptions.slice(index + 1),\n        ]\n        states.cachedOptions.splice(index, 1)\n        removeNewOption(option)\n      } else if (\n        props.multipleLimit <= 0 ||\n        selectedOptions.length < props.multipleLimit\n      ) {\n        selectedOptions = [...selectedOptions, getValue(option)]\n        states.cachedOptions.push(option)\n        selectNewOption(option)\n        updateHoveringIndex(idx)\n      }\n      update(selectedOptions)\n      if (option.created) {\n        states.query = ''\n        handleQueryChange('')\n        states.inputLength = 20\n      }\n      if (props.filterable && !props.reserveKeyword) {\n        inputRef.value.focus?.()\n        onUpdateInputValue('')\n      }\n      if (props.filterable) {\n        states.calculatedWidth =\n          calculatorRef.value.getBoundingClientRect().width\n      }\n      resetInputHeight()\n      setSoftFocus()\n    } else {\n      selectedIndex.value = idx\n      states.selectedLabel = getLabel(option)\n      update(getValue(option))\n      expanded.value = false\n      states.isComposing = false\n      states.isSilentBlur = byClick\n      selectNewOption(option)\n      if (!option.created) {\n        clearAllNewOption()\n      }\n      updateHoveringIndex(idx)\n    }\n  }\n\n  const deleteTag = (event: MouseEvent, option: Option) => {\n    let selectedOptions = (props.modelValue as any[]).slice()\n\n    const index = getValueIndex(selectedOptions, getValue(option))\n\n    if (index > -1 && !selectDisabled.value) {\n      selectedOptions = [\n        ...(props.modelValue as Array<unknown>).slice(0, index),\n        ...(props.modelValue as Array<unknown>).slice(index + 1),\n      ]\n      states.cachedOptions.splice(index, 1)\n      update(selectedOptions)\n      emit('remove-tag', getValue(option))\n      states.softFocus = true\n      removeNewOption(option)\n      return nextTick(focusAndUpdatePopup)\n    }\n    event.stopPropagation()\n  }\n\n  const handleFocus = (event: FocusEvent) => {\n    const focused = states.isComposing\n    states.isComposing = true\n    if (!states.softFocus) {\n      // If already in the focus state, shouldn't trigger event\n      if (!focused) emit('focus', event)\n    } else {\n      states.softFocus = false\n    }\n  }\n\n  const handleBlur = (event: FocusEvent) => {\n    states.softFocus = false\n\n    // reset input value when blurred\n    // https://github.com/ElemeFE/element/pull/10822\n    return nextTick(() => {\n      inputRef.value?.blur?.()\n      if (calculatorRef.value) {\n        states.calculatedWidth =\n          calculatorRef.value.getBoundingClientRect().width\n      }\n      if (states.isSilentBlur) {\n        states.isSilentBlur = false\n      } else {\n        if (states.isComposing) {\n          emit('blur', event)\n        }\n      }\n      states.isComposing = false\n    })\n  }\n\n  // keyboard handlers\n  const handleEsc = () => {\n    if (states.displayInputValue.length > 0) {\n      onUpdateInputValue('')\n    } else {\n      expanded.value = false\n    }\n  }\n\n  const handleDel = (e: KeyboardEvent) => {\n    if (states.displayInputValue.length === 0) {\n      e.preventDefault()\n      const selected = (props.modelValue as Array<any>).slice()\n      selected.pop()\n      removeNewOption(states.cachedOptions.pop())\n      update(selected)\n    }\n  }\n\n  const handleClear = () => {\n    let emptyValue: string | any[]\n    if (isArray(props.modelValue)) {\n      emptyValue = []\n    } else {\n      emptyValue = undefined\n    }\n\n    states.softFocus = true\n    if (props.multiple) {\n      states.cachedOptions = []\n    } else {\n      states.selectedLabel = ''\n    }\n    expanded.value = false\n    update(emptyValue)\n    emit('clear')\n    clearAllNewOption()\n    return nextTick(focusAndUpdatePopup)\n  }\n\n  const onUpdateInputValue = (val: string) => {\n    states.displayInputValue = val\n    states.inputValue = val\n  }\n\n  const onKeyboardNavigate = (\n    direction: 'forward' | 'backward',\n    hoveringIndex: number = undefined\n  ) => {\n    const options = filteredOptions.value\n    if (\n      !['forward', 'backward'].includes(direction) ||\n      selectDisabled.value ||\n      options.length <= 0 ||\n      optionsAllDisabled.value\n    ) {\n      return\n    }\n    if (!expanded.value) {\n      return toggleMenu()\n    }\n    if (hoveringIndex === undefined) {\n      hoveringIndex = states.hoveringIndex\n    }\n    let newIndex = -1\n    if (direction === 'forward') {\n      newIndex = hoveringIndex + 1\n      if (newIndex >= options.length) {\n        // return to the first option\n        newIndex = 0\n      }\n    } else if (direction === 'backward') {\n      newIndex = hoveringIndex - 1\n      if (newIndex < 0 || newIndex >= options.length) {\n        // navigate to the last one\n        newIndex = options.length - 1\n      }\n    }\n    const option = options[newIndex]\n    if (getDisabled(option) || option.type === 'Group') {\n      // prevent dispatching multiple nextTick callbacks.\n      return onKeyboardNavigate(direction, newIndex)\n    } else {\n      updateHoveringIndex(newIndex)\n      scrollToItem(newIndex)\n    }\n  }\n\n  const onKeyboardSelect = () => {\n    if (!expanded.value) {\n      return toggleMenu()\n    } else if (\n      ~states.hoveringIndex &&\n      filteredOptions.value[states.hoveringIndex]\n    ) {\n      onSelect(\n        filteredOptions.value[states.hoveringIndex],\n        states.hoveringIndex,\n        false\n      )\n    }\n  }\n\n  const updateHoveringIndex = (idx: number) => {\n    states.hoveringIndex = idx\n  }\n\n  const resetHoveringIndex = () => {\n    states.hoveringIndex = -1\n  }\n\n  const setSoftFocus = () => {\n    const _input = inputRef.value\n    if (_input) {\n      _input.focus?.()\n    }\n  }\n\n  const onInput = (event) => {\n    const value = event.target.value\n    onUpdateInputValue(value)\n    if (states.displayInputValue.length > 0 && !expanded.value) {\n      expanded.value = true\n    }\n\n    states.calculatedWidth = calculatorRef.value.getBoundingClientRect().width\n    if (props.multiple) {\n      resetInputHeight()\n    }\n    if (props.remote) {\n      debouncedOnInputChange()\n    } else {\n      return onInputChange()\n    }\n  }\n\n  const handleClickOutside = () => {\n    expanded.value = false\n    return handleBlur()\n  }\n\n  const handleMenuEnter = () => {\n    states.inputValue = states.displayInputValue\n    return nextTick(() => {\n      if (~indexRef.value) {\n        updateHoveringIndex(indexRef.value)\n        scrollToItem(states.hoveringIndex)\n      }\n    })\n  }\n\n  const scrollToItem = (index: number) => {\n    menuRef.value.scrollToItem(index)\n  }\n\n  const initStates = () => {\n    resetHoveringIndex()\n    if (props.multiple) {\n      if ((props.modelValue as Array<any>).length > 0) {\n        let initHovering = false\n        states.cachedOptions.length = 0\n        states.previousValue = props.modelValue.toString()\n\n        for (const value of props.modelValue) {\n          const selectValue = getValueKey(value)\n\n          if (filteredOptionsValueMap.value.has(selectValue)) {\n            const { index, option } =\n              filteredOptionsValueMap.value.get(selectValue)\n\n            states.cachedOptions.push(option)\n            if (!initHovering) {\n              updateHoveringIndex(index)\n            }\n            initHovering = true\n          }\n        }\n      } else {\n        states.cachedOptions = []\n        states.previousValue = undefined\n      }\n    } else {\n      if (hasModelValue.value) {\n        states.previousValue = props.modelValue\n        const options = filteredOptions.value\n        const selectedItemIndex = options.findIndex(\n          (option) =>\n            getValueKey(getValue(option)) === getValueKey(props.modelValue)\n        )\n        if (~selectedItemIndex) {\n          states.selectedLabel = getLabel(options[selectedItemIndex])\n          updateHoveringIndex(selectedItemIndex)\n        } else {\n          states.selectedLabel = getValueKey(props.modelValue)\n        }\n      } else {\n        states.selectedLabel = ''\n        states.previousValue = undefined\n      }\n    }\n    clearAllNewOption()\n    calculatePopperSize()\n  }\n\n  // in order to track these individually, we need to turn them into refs instead of watching the entire\n  // reactive object which could cause perf penalty when unnecessary field gets changed the watch method will\n  // be invoked.\n\n  watch(expanded, (val) => {\n    emit('visible-change', val)\n    if (val) {\n      popper.value.update?.()\n      // the purpose of this function is to differ the blur event trigger mechanism\n    } else {\n      states.displayInputValue = ''\n      states.previousQuery = null\n      createNewOption('')\n    }\n  })\n\n  watch(\n    () => props.modelValue,\n    (val, oldVal) => {\n      if (!val || val.toString() !== states.previousValue) {\n        initStates()\n      }\n      if (!isEqual(val, oldVal) && props.validateEvent) {\n        elFormItem?.validate?.('change').catch((err) => debugWarn(err))\n      }\n    },\n    {\n      deep: true,\n    }\n  )\n\n  watch(\n    () => props.options,\n    () => {\n      const input = inputRef.value\n      // filter or remote-search scenarios are not initialized\n      if (!input || (input && document.activeElement !== input)) {\n        initStates()\n      }\n    },\n    {\n      deep: true,\n    }\n  )\n\n  // fix the problem that scrollTop is not reset in filterable mode\n  watch(filteredOptions, () => {\n    return menuRef.value && nextTick(menuRef.value.resetScrollTop)\n  })\n\n  watch(\n    () => dropdownMenuVisible.value,\n    (val) => {\n      if (!val) {\n        resetHoveringIndex()\n      }\n    }\n  )\n\n  onMounted(() => {\n    initStates()\n  })\n  useResizeObserver(selectRef, handleResize)\n\n  return {\n    // data exports\n    collapseTagSize,\n    currentPlaceholder,\n    expanded,\n    emptyText,\n    popupHeight,\n    debounce,\n    filteredOptions,\n    iconComponent,\n    iconReverse,\n    inputWrapperStyle,\n    popperSize,\n    dropdownMenuVisible,\n    hasModelValue,\n    // readonly,\n    shouldShowPlaceholder,\n    selectDisabled,\n    selectSize,\n    showClearBtn,\n    states,\n    tagMaxWidth,\n    nsSelectV2,\n    nsInput,\n\n    // refs items exports\n    calculatorRef,\n    controlRef,\n    inputRef,\n    menuRef,\n    popper,\n    selectRef,\n    selectionRef,\n\n    popperRef,\n\n    validateState,\n    validateIcon,\n    showTagList,\n    collapseTagList,\n\n    // methods exports\n    debouncedOnInputChange,\n    deleteTag,\n    getLabel,\n    getValue,\n    getDisabled,\n    getValueKey,\n    handleBlur,\n    handleClear,\n    handleClickOutside,\n    handleDel,\n    handleEsc,\n    handleFocus,\n    handleMenuEnter,\n    handleResize,\n    toggleMenu,\n    scrollTo: scrollToItem,\n    onInput,\n    onKeyboardNavigate,\n    onKeyboardSelect,\n    onSelect,\n    onHover: updateHoveringIndex,\n    onUpdateInputValue,\n    handleCompositionStart,\n    handleCompositionEnd,\n    handleCompositionUpdate,\n  }\n}\n\nexport default useSelect\n"], "names": ["debounce", "lodashDebounce"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAgBA,MAAM,yBAAyB,GAAG,EAAE,CAAC;AACrC,MAAM,mBAAmB,GAAG,EAAE,CAAC;AAC/B,MAAM,cAAc,GAAG;AACvB,EAAE,MAAM,EAAE,EAAE;AACZ,EAAE,OAAO,EAAE,EAAE;AACb,EAAE,KAAK,EAAE,EAAE;AACX,CAAC,CAAC;AACG,MAAC,SAAS,GAAG,CAAC,KAAK,EAAE,IAAI,KAAK;AACnC,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC;AAC5B,EAAE,MAAM,UAAU,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC;AAC/C,EAAE,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;AACxC,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,WAAW,EAAE,CAAC;AAC/D,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC1E,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC;AAC1B,IAAI,UAAU,EAAE,yBAAyB;AACzC,IAAI,iBAAiB,EAAE,yBAAyB;AAChD,IAAI,eAAe,EAAE,CAAC;AACtB,IAAI,iBAAiB,EAAE,EAAE;AACzB,IAAI,aAAa,EAAE,EAAE;AACrB,IAAI,cAAc,EAAE,EAAE;AACtB,IAAI,YAAY,EAAE,EAAE;AACpB,IAAI,eAAe,EAAE,KAAK;AAC1B,IAAI,kBAAkB,EAAE,EAAE;AAC1B,IAAI,aAAa,EAAE,CAAC,CAAC;AACrB,IAAI,gBAAgB,EAAE,KAAK;AAC3B,IAAI,eAAe,EAAE,KAAK;AAC1B,IAAI,YAAY,EAAE,KAAK;AACvB,IAAI,WAAW,EAAE,KAAK;AACtB,IAAI,WAAW,EAAE,EAAE;AACnB,IAAI,WAAW,EAAE,GAAG;AACpB,IAAI,kBAAkB,EAAE,CAAC;AACzB,IAAI,aAAa,EAAE,IAAI;AACvB,IAAI,aAAa,EAAE,KAAK,CAAC;AACzB,IAAI,KAAK,EAAE,EAAE;AACb,IAAI,aAAa,EAAE,EAAE;AACrB,IAAI,SAAS,EAAE,KAAK;AACpB,IAAI,cAAc,EAAE,KAAK;AACzB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,aAAa,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,EAAE,MAAM,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B,EAAE,MAAM,UAAU,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AAC/B,EAAE,MAAM,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AAC7B,EAAE,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AAC5B,EAAE,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AAC3B,EAAE,MAAM,SAAS,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AAC9B,EAAE,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AACjC,EAAE,MAAM,aAAa,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AAClC,EAAE,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AAC9B,EAAE,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,KAAK,CAAC,QAAQ,KAAK,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;AACvG,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM;AACrC,IAAI,MAAM,WAAW,GAAG,eAAe,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC;AACxE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC;AACnE,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM;AACvC,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AACpC,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM;AACtC,IAAI,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,GAAG,aAAa,CAAC,KAAK,CAAC;AAC3H,IAAI,MAAM,QAAQ,GAAG,KAAK,CAAC,SAAS,IAAI,CAAC,cAAc,CAAC,KAAK,IAAI,MAAM,CAAC,gBAAgB,IAAI,QAAQ,CAAC;AACrG,IAAI,OAAO,QAAQ,CAAC;AACpB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,UAAU,GAAG,EAAE,GAAG,OAAO,CAAC,CAAC;AACxF,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,aAAa,CAAC,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;AACtG,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,CAAC,aAAa,KAAK,EAAE,CAAC,CAAC;AACvG,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,qBAAqB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;AAClF,EAAE,MAAMA,UAAQ,GAAG,QAAQ,CAAC,MAAM,KAAK,CAAC,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;AAC1D,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM;AACnC,IAAI,MAAM,OAAO,GAAG,eAAe,CAAC,KAAK,CAAC;AAC1C,IAAI,IAAI,KAAK,CAAC,OAAO,EAAE;AACvB,MAAM,OAAO,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,mBAAmB,CAAC,CAAC;AACzD,KAAK,MAAM;AACX,MAAM,IAAI,KAAK,CAAC,MAAM,IAAI,MAAM,CAAC,UAAU,KAAK,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;AAC1E,QAAQ,OAAO,KAAK,CAAC;AACrB,MAAM,IAAI,KAAK,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;AACvE,QAAQ,OAAO,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,mBAAmB,CAAC,CAAC;AAC3D,OAAO;AACP,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;AAChC,QAAQ,OAAO,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC,kBAAkB,CAAC,CAAC;AACzD,OAAO;AACP,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM;AACzC,IAAI,MAAM,aAAa,GAAG,CAAC,CAAC,KAAK;AACjC,MAAM,MAAM,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC;AACtC,MAAM,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;AAChE,MAAM,MAAM,mBAAmB,GAAG,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC;AAChF,MAAM,OAAO,mBAAmB,CAAC;AACjC,KAAK,CAAC;AACN,IAAI,IAAI,KAAK,CAAC,OAAO,EAAE;AACvB,MAAM,OAAO,EAAE,CAAC;AAChB,KAAK;AACL,IAAI,OAAO,CAAC,GAAG,KAAK,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK;AAC9E,MAAM,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;AACvC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;AAC5B,QAAQ,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;AACvD,QAAQ,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;AACjC,UAAU,GAAG,CAAC,IAAI,CAAC;AACnB,YAAY,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC;AACjC,YAAY,OAAO,EAAE,IAAI;AACzB,YAAY,IAAI,EAAE,OAAO;AACzB,WAAW,EAAE,GAAG,QAAQ,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;AAC7C,SAAS;AACT,OAAO,MAAM,IAAI,KAAK,CAAC,MAAM,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE;AACtD,QAAQ,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACvB,OAAO;AACP,MAAM,OAAO,GAAG,CAAC;AACjB,KAAK,EAAE,EAAE,CAAC,CAAC;AACX,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,uBAAuB,GAAG,QAAQ,CAAC,MAAM;AACjD,IAAI,MAAM,QAAQ,mBAAmB,IAAI,GAAG,EAAE,CAAC;AAC/C,IAAI,eAAe,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,KAAK;AACrD,MAAM,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;AACrE,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,QAAQ,CAAC;AACpB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,kBAAkB,GAAG,QAAQ,CAAC,MAAM,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC1G,EAAE,MAAM,UAAU,GAAG,WAAW,EAAE,CAAC;AACnC,EAAE,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM,UAAU,CAAC,KAAK,KAAK,OAAO,GAAG,OAAO,GAAG,SAAS,CAAC,CAAC;AAC7F,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM;AACrC,IAAI,MAAM,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC;AACtC,IAAI,MAAM,IAAI,GAAG,eAAe,CAAC,KAAK,IAAI,SAAS,CAAC;AACpD,IAAI,MAAM,WAAW,GAAG,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AAC3F,IAAI,MAAM,YAAY,GAAG,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AAC7F,IAAI,OAAO,MAAM,CAAC,WAAW,GAAG,YAAY,GAAG,WAAW,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;AAClF,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,mBAAmB,GAAG,MAAM;AACpC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,GAAG,SAAS,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,KAAK,GAAG,CAAC;AACzF,GAAG,CAAC;AACJ,EAAE,MAAM,iBAAiB,GAAG,QAAQ,CAAC,MAAM;AAC3C,IAAI,OAAO;AACX,MAAM,KAAK,EAAE,CAAC,EAAE,MAAM,CAAC,eAAe,KAAK,CAAC,GAAG,mBAAmB,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,mBAAmB,CAAC,EAAE,CAAC;AAChI,KAAK,CAAC;AACN,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,qBAAqB,GAAG,QAAQ,CAAC,MAAM;AAC/C,IAAI,IAAI,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;AACnC,MAAM,OAAO,KAAK,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC;AACxE,KAAK;AACL,IAAI,OAAO,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC,iBAAiB,CAAC,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC;AAC3E,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,kBAAkB,GAAG,QAAQ,CAAC,MAAM;AAC5C,IAAI,MAAM,YAAY,GAAG,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,uBAAuB,CAAC,CAAC;AACzE,IAAI,OAAO,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,YAAY,GAAG,MAAM,CAAC,aAAa,CAAC;AAC3F,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM;AACnC,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,OAAO,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC;AACvG,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM;AAClC,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE;AACxB,MAAM,MAAM,GAAG,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC;AAC1C,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,uBAAuB,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE;AACvG,QAAQ,MAAM,EAAE,KAAK,EAAE,GAAG,uBAAuB,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACvF,QAAQ,OAAO,KAAK,CAAC;AACrB,OAAO;AACP,KAAK,MAAM;AACX,MAAM,IAAI,KAAK,CAAC,UAAU,IAAI,uBAAuB,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;AACnF,QAAQ,MAAM,EAAE,KAAK,EAAE,GAAG,uBAAuB,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AAC9E,QAAQ,OAAO,KAAK,CAAC;AACrB,OAAO;AACP,KAAK;AACL,IAAI,OAAO,CAAC,CAAC,CAAC;AACd,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,mBAAmB,GAAG,QAAQ,CAAC;AACvC,IAAI,GAAG,GAAG;AACV,MAAM,OAAO,QAAQ,CAAC,KAAK,IAAI,SAAS,CAAC,KAAK,KAAK,KAAK,CAAC;AACzD,KAAK;AACL,IAAI,GAAG,CAAC,GAAG,EAAE;AACb,MAAM,QAAQ,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3B,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;AAC3F,EAAE,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;AAC5F,EAAE,MAAM;AACR,IAAI,eAAe;AACnB,IAAI,eAAe;AACnB,IAAI,eAAe;AACnB,IAAI,iBAAiB;AACrB,GAAG,GAAG,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACpC,EAAE,MAAM;AACR,IAAI,sBAAsB;AAC1B,IAAI,uBAAuB;AAC3B,IAAI,oBAAoB;AACxB,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AAClC,EAAE,MAAM,mBAAmB,GAAG,MAAM;AACpC,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACnB,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,QAAQ,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC5F,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,YAAY,EAAE,CAAC;AAC7D,GAAG,CAAC;AACJ,EAAE,MAAM,UAAU,GAAG,MAAM;AAC3B,IAAI,IAAI,KAAK,CAAC,iBAAiB;AAC/B,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;AAC/B,MAAM,IAAI,MAAM,CAAC,WAAW;AAC5B,QAAQ,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;AAChC,MAAM,OAAO,QAAQ,CAAC,MAAM;AAC5B,QAAQ,IAAI,EAAE,EAAE,EAAE,CAAC;AACnB,QAAQ,QAAQ,CAAC,KAAK,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC;AACzC,QAAQ,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,QAAQ,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAChG,OAAO,CAAC,CAAC;AACT,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,MAAM;AAC9B,IAAI,IAAI,KAAK,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU,KAAK,MAAM,CAAC,aAAa,EAAE;AACxE,MAAM,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,aAAa,CAAC;AAC1C,KAAK;AACL,IAAI,iBAAiB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AACzC,IAAI,OAAO,QAAQ,CAAC,MAAM;AAC1B,MAAM,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AACzC,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,MAAM,sBAAsB,GAAGC,QAAc,CAAC,aAAa,EAAED,UAAQ,CAAC,KAAK,CAAC,CAAC;AAC/E,EAAE,MAAM,iBAAiB,GAAG,CAAC,GAAG,KAAK;AACrC,IAAI,IAAI,MAAM,CAAC,aAAa,KAAK,GAAG,EAAE;AACtC,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,CAAC,aAAa,GAAG,GAAG,CAAC;AAC/B,IAAI,IAAI,KAAK,CAAC,UAAU,IAAI,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;AAC5D,MAAM,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AAC9B,KAAK,MAAM,IAAI,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,MAAM,IAAI,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;AACnF,MAAM,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AAC9B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,UAAU,GAAG,CAAC,GAAG,KAAK;AAC9B,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,GAAG,CAAC,EAAE;AACzC,MAAM,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;AAC9B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,MAAM,GAAG,CAAC,GAAG,KAAK;AAC1B,IAAI,IAAI,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAC;AAClC,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;AACpB,IAAI,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;AACvC,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,CAAC,GAAG,GAAG,EAAE,EAAE,KAAK,KAAK;AAC7C,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AAC1B,MAAM,OAAO,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAChC,KAAK;AACL,IAAI,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;AACpC,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;AACnB,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK;AAC1B,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE;AACxD,QAAQ,KAAK,GAAG,CAAC,CAAC;AAClB,QAAQ,OAAO,IAAI,CAAC;AACpB,OAAO;AACP,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,CAAC,IAAI,KAAK;AAChC,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;AAC7D,GAAG,CAAC;AACJ,EAAE,MAAM,gBAAgB,GAAG,MAAM;AACjC,IAAI,OAAO,QAAQ,CAAC,MAAM;AAC1B,MAAM,IAAI,EAAE,EAAE,EAAE,CAAC;AACjB,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK;AACzB,QAAQ,OAAO;AACf,MAAM,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC;AAC3C,MAAM,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,YAAY,CAAC;AACtD,MAAM,IAAI,QAAQ,CAAC,KAAK,IAAI,SAAS,CAAC,KAAK,KAAK,KAAK,EAAE;AACvD,QAAQ,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACrG,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,MAAM;AAC7B,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,eAAe,EAAE,CAAC;AACtB,IAAI,mBAAmB,EAAE,CAAC;AAC1B,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACjG,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE;AACxB,MAAM,OAAO,gBAAgB,EAAE,CAAC;AAChC,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,eAAe,GAAG,MAAM;AAChC,IAAI,MAAM,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC;AACtC,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,MAAM,CAAC,WAAW,GAAG,MAAM,CAAC,qBAAqB,EAAE,CAAC,KAAK,CAAC;AAChE,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,QAAQ,GAAG,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,GAAG,IAAI,KAAK;AACpD,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE;AACxB,MAAM,IAAI,eAAe,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;AACrD,MAAM,MAAM,KAAK,GAAG,aAAa,CAAC,eAAe,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;AACrE,MAAM,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;AACtB,QAAQ,eAAe,GAAG;AAC1B,UAAU,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC;AAC5C,UAAU,GAAG,eAAe,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;AAC7C,SAAS,CAAC;AACV,QAAQ,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAC9C,QAAQ,eAAe,CAAC,MAAM,CAAC,CAAC;AAChC,OAAO,MAAM,IAAI,KAAK,CAAC,aAAa,IAAI,CAAC,IAAI,eAAe,CAAC,MAAM,GAAG,KAAK,CAAC,aAAa,EAAE;AAC3F,QAAQ,eAAe,GAAG,CAAC,GAAG,eAAe,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;AACjE,QAAQ,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC1C,QAAQ,eAAe,CAAC,MAAM,CAAC,CAAC;AAChC,QAAQ,mBAAmB,CAAC,GAAG,CAAC,CAAC;AACjC,OAAO;AACP,MAAM,MAAM,CAAC,eAAe,CAAC,CAAC;AAC9B,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;AAC1B,QAAQ,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC;AAC1B,QAAQ,iBAAiB,CAAC,EAAE,CAAC,CAAC;AAC9B,QAAQ,MAAM,CAAC,WAAW,GAAG,EAAE,CAAC;AAChC,OAAO;AACP,MAAM,IAAI,KAAK,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE;AACrD,QAAQ,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,QAAQ,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC1E,QAAQ,kBAAkB,CAAC,EAAE,CAAC,CAAC;AAC/B,OAAO;AACP,MAAM,IAAI,KAAK,CAAC,UAAU,EAAE;AAC5B,QAAQ,MAAM,CAAC,eAAe,GAAG,aAAa,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,KAAK,CAAC;AACnF,OAAO;AACP,MAAM,gBAAgB,EAAE,CAAC;AACzB,MAAM,YAAY,EAAE,CAAC;AACrB,KAAK,MAAM;AACX,MAAM,aAAa,CAAC,KAAK,GAAG,GAAG,CAAC;AAChC,MAAM,MAAM,CAAC,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC9C,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;AAC/B,MAAM,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;AAC7B,MAAM,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC;AACjC,MAAM,MAAM,CAAC,YAAY,GAAG,OAAO,CAAC;AACpC,MAAM,eAAe,CAAC,MAAM,CAAC,CAAC;AAC9B,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;AAC3B,QAAQ,iBAAiB,EAAE,CAAC;AAC5B,OAAO;AACP,MAAM,mBAAmB,CAAC,GAAG,CAAC,CAAC;AAC/B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,CAAC,KAAK,EAAE,MAAM,KAAK;AACvC,IAAI,IAAI,eAAe,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;AACnD,IAAI,MAAM,KAAK,GAAG,aAAa,CAAC,eAAe,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;AACnE,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;AAC7C,MAAM,eAAe,GAAG;AACxB,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC;AAC3C,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;AAC5C,OAAO,CAAC;AACR,MAAM,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAC5C,MAAM,MAAM,CAAC,eAAe,CAAC,CAAC;AAC9B,MAAM,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;AAC3C,MAAM,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;AAC9B,MAAM,eAAe,CAAC,MAAM,CAAC,CAAC;AAC9B,MAAM,OAAO,QAAQ,CAAC,mBAAmB,CAAC,CAAC;AAC3C,KAAK;AACL,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;AAC5B,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,CAAC,KAAK,KAAK;AACjC,IAAI,MAAM,OAAO,GAAG,MAAM,CAAC,WAAW,CAAC;AACvC,IAAI,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC;AAC9B,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;AAC3B,MAAM,IAAI,CAAC,OAAO;AAClB,QAAQ,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAC7B,KAAK,MAAM;AACX,MAAM,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC;AAC/B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,UAAU,GAAG,CAAC,KAAK,KAAK;AAChC,IAAI,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC;AAC7B,IAAI,OAAO,QAAQ,CAAC,MAAM;AAC1B,MAAM,IAAI,EAAE,EAAE,EAAE,CAAC;AACjB,MAAM,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,QAAQ,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC7F,MAAM,IAAI,aAAa,CAAC,KAAK,EAAE;AAC/B,QAAQ,MAAM,CAAC,eAAe,GAAG,aAAa,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,KAAK,CAAC;AACnF,OAAO;AACP,MAAM,IAAI,MAAM,CAAC,YAAY,EAAE;AAC/B,QAAQ,MAAM,CAAC,YAAY,GAAG,KAAK,CAAC;AACpC,OAAO,MAAM;AACb,QAAQ,IAAI,MAAM,CAAC,WAAW,EAAE;AAChC,UAAU,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAC9B,SAAS;AACT,OAAO;AACP,MAAM,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC;AACjC,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,MAAM;AAC1B,IAAI,IAAI,MAAM,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;AAC7C,MAAM,kBAAkB,CAAC,EAAE,CAAC,CAAC;AAC7B,KAAK,MAAM;AACX,MAAM,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;AAC7B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,CAAC,CAAC,KAAK;AAC3B,IAAI,IAAI,MAAM,CAAC,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE;AAC/C,MAAM,CAAC,CAAC,cAAc,EAAE,CAAC;AACzB,MAAM,MAAM,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;AAChD,MAAM,QAAQ,CAAC,GAAG,EAAE,CAAC;AACrB,MAAM,eAAe,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC,CAAC;AAClD,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC;AACvB,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,MAAM;AAC5B,IAAI,IAAI,UAAU,CAAC;AACnB,IAAI,IAAI,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;AACnC,MAAM,UAAU,GAAG,EAAE,CAAC;AACtB,KAAK,MAAM;AACX,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC;AAC1B,KAAK;AACL,IAAI,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;AAC5B,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE;AACxB,MAAM,MAAM,CAAC,aAAa,GAAG,EAAE,CAAC;AAChC,KAAK,MAAM;AACX,MAAM,MAAM,CAAC,aAAa,GAAG,EAAE,CAAC;AAChC,KAAK;AACL,IAAI,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;AAC3B,IAAI,MAAM,CAAC,UAAU,CAAC,CAAC;AACvB,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;AAClB,IAAI,iBAAiB,EAAE,CAAC;AACxB,IAAI,OAAO,QAAQ,CAAC,mBAAmB,CAAC,CAAC;AACzC,GAAG,CAAC;AACJ,EAAE,MAAM,kBAAkB,GAAG,CAAC,GAAG,KAAK;AACtC,IAAI,MAAM,CAAC,iBAAiB,GAAG,GAAG,CAAC;AACnC,IAAI,MAAM,CAAC,UAAU,GAAG,GAAG,CAAC;AAC5B,GAAG,CAAC;AACJ,EAAE,MAAM,kBAAkB,GAAG,CAAC,SAAS,EAAE,aAAa,GAAG,KAAK,CAAC,KAAK;AACpE,IAAI,MAAM,OAAO,GAAG,eAAe,CAAC,KAAK,CAAC;AAC1C,IAAI,IAAI,CAAC,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,cAAc,CAAC,KAAK,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,kBAAkB,CAAC,KAAK,EAAE;AACjI,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;AACzB,MAAM,OAAO,UAAU,EAAE,CAAC;AAC1B,KAAK;AACL,IAAI,IAAI,aAAa,KAAK,KAAK,CAAC,EAAE;AAClC,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;AAC3C,KAAK;AACL,IAAI,IAAI,QAAQ,GAAG,CAAC,CAAC,CAAC;AACtB,IAAI,IAAI,SAAS,KAAK,SAAS,EAAE;AACjC,MAAM,QAAQ,GAAG,aAAa,GAAG,CAAC,CAAC;AACnC,MAAM,IAAI,QAAQ,IAAI,OAAO,CAAC,MAAM,EAAE;AACtC,QAAQ,QAAQ,GAAG,CAAC,CAAC;AACrB,OAAO;AACP,KAAK,MAAM,IAAI,SAAS,KAAK,UAAU,EAAE;AACzC,MAAM,QAAQ,GAAG,aAAa,GAAG,CAAC,CAAC;AACnC,MAAM,IAAI,QAAQ,GAAG,CAAC,IAAI,QAAQ,IAAI,OAAO,CAAC,MAAM,EAAE;AACtD,QAAQ,QAAQ,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;AACtC,OAAO;AACP,KAAK;AACL,IAAI,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;AACrC,IAAI,IAAI,WAAW,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE;AACxD,MAAM,OAAO,kBAAkB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AACrD,KAAK,MAAM;AACX,MAAM,mBAAmB,CAAC,QAAQ,CAAC,CAAC;AACpC,MAAM,YAAY,CAAC,QAAQ,CAAC,CAAC;AAC7B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,gBAAgB,GAAG,MAAM;AACjC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;AACzB,MAAM,OAAO,UAAU,EAAE,CAAC;AAC1B,KAAK,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,IAAI,eAAe,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE;AACrF,MAAM,QAAQ,CAAC,eAAe,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,MAAM,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;AACzF,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,mBAAmB,GAAG,CAAC,GAAG,KAAK;AACvC,IAAI,MAAM,CAAC,aAAa,GAAG,GAAG,CAAC;AAC/B,GAAG,CAAC;AACJ,EAAE,MAAM,kBAAkB,GAAG,MAAM;AACnC,IAAI,MAAM,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;AAC9B,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,MAAM;AAC7B,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC;AAClC,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC7D,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,OAAO,GAAG,CAAC,KAAK,KAAK;AAC7B,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;AACrC,IAAI,kBAAkB,CAAC,KAAK,CAAC,CAAC;AAC9B,IAAI,IAAI,MAAM,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;AAChE,MAAM,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC;AAC5B,KAAK;AACL,IAAI,MAAM,CAAC,eAAe,GAAG,aAAa,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,KAAK,CAAC;AAC/E,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE;AACxB,MAAM,gBAAgB,EAAE,CAAC;AACzB,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,MAAM,EAAE;AACtB,MAAM,sBAAsB,EAAE,CAAC;AAC/B,KAAK,MAAM;AACX,MAAM,OAAO,aAAa,EAAE,CAAC;AAC7B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,kBAAkB,GAAG,MAAM;AACnC,IAAI,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;AAC3B,IAAI,OAAO,UAAU,EAAE,CAAC;AACxB,GAAG,CAAC;AACJ,EAAE,MAAM,eAAe,GAAG,MAAM;AAChC,IAAI,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,iBAAiB,CAAC;AACjD,IAAI,OAAO,QAAQ,CAAC,MAAM;AAC1B,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;AAC3B,QAAQ,mBAAmB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC5C,QAAQ,YAAY,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;AAC3C,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,CAAC,KAAK,KAAK;AAClC,IAAI,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AACtC,GAAG,CAAC;AACJ,EAAE,MAAM,UAAU,GAAG,MAAM;AAC3B,IAAI,kBAAkB,EAAE,CAAC;AACzB,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE;AACxB,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;AACvC,QAAQ,IAAI,YAAY,GAAG,KAAK,CAAC;AACjC,QAAQ,MAAM,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;AACxC,QAAQ,MAAM,CAAC,aAAa,GAAG,KAAK,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;AAC3D,QAAQ,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,UAAU,EAAE;AAC9C,UAAU,MAAM,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;AACjD,UAAU,IAAI,uBAAuB,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;AAC9D,YAAY,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,uBAAuB,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AACrF,YAAY,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC9C,YAAY,IAAI,CAAC,YAAY,EAAE;AAC/B,cAAc,mBAAmB,CAAC,KAAK,CAAC,CAAC;AACzC,aAAa;AACb,YAAY,YAAY,GAAG,IAAI,CAAC;AAChC,WAAW;AACX,SAAS;AACT,OAAO,MAAM;AACb,QAAQ,MAAM,CAAC,aAAa,GAAG,EAAE,CAAC;AAClC,QAAQ,MAAM,CAAC,aAAa,GAAG,KAAK,CAAC,CAAC;AACtC,OAAO;AACP,KAAK,MAAM;AACX,MAAM,IAAI,aAAa,CAAC,KAAK,EAAE;AAC/B,QAAQ,MAAM,CAAC,aAAa,GAAG,KAAK,CAAC,UAAU,CAAC;AAChD,QAAQ,MAAM,OAAO,GAAG,eAAe,CAAC,KAAK,CAAC;AAC9C,QAAQ,MAAM,iBAAiB,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,KAAK,WAAW,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;AACjI,QAAQ,IAAI,CAAC,iBAAiB,EAAE;AAChC,UAAU,MAAM,CAAC,aAAa,GAAG,QAAQ,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC;AACtE,UAAU,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;AACjD,SAAS,MAAM;AACf,UAAU,MAAM,CAAC,aAAa,GAAG,WAAW,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AAC/D,SAAS;AACT,OAAO,MAAM;AACb,QAAQ,MAAM,CAAC,aAAa,GAAG,EAAE,CAAC;AAClC,QAAQ,MAAM,CAAC,aAAa,GAAG,KAAK,CAAC,CAAC;AACtC,OAAO;AACP,KAAK;AACL,IAAI,iBAAiB,EAAE,CAAC;AACxB,IAAI,mBAAmB,EAAE,CAAC;AAC1B,GAAG,CAAC;AACJ,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK;AAC3B,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,IAAI,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;AAChC,IAAI,IAAI,GAAG,EAAE;AACb,MAAM,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK,EAAE,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACvE,KAAK,MAAM;AACX,MAAM,MAAM,CAAC,iBAAiB,GAAG,EAAE,CAAC;AACpC,MAAM,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC;AAClC,MAAM,eAAe,CAAC,EAAE,CAAC,CAAC;AAC1B,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK;AACjD,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC,aAAa,EAAE;AACzD,MAAM,UAAU,EAAE,CAAC;AACnB,KAAK;AACL,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,KAAK,CAAC,aAAa,EAAE;AACtD,MAAM,CAAC,EAAE,GAAG,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,CAAC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/I,KAAK;AACL,GAAG,EAAE;AACL,IAAI,IAAI,EAAE,IAAI;AACd,GAAG,CAAC,CAAC;AACL,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,OAAO,EAAE,MAAM;AACnC,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;AACjC,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,IAAI,QAAQ,CAAC,aAAa,KAAK,KAAK,EAAE;AAC7D,MAAM,UAAU,EAAE,CAAC;AACnB,KAAK;AACL,GAAG,EAAE;AACL,IAAI,IAAI,EAAE,IAAI;AACd,GAAG,CAAC,CAAC;AACL,EAAE,KAAK,CAAC,eAAe,EAAE,MAAM;AAC/B,IAAI,OAAO,OAAO,CAAC,KAAK,IAAI,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AACnE,GAAG,CAAC,CAAC;AACL,EAAE,KAAK,CAAC,MAAM,mBAAmB,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK;AAClD,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,MAAM,kBAAkB,EAAE,CAAC;AAC3B,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,UAAU,EAAE,CAAC;AACjB,GAAG,CAAC,CAAC;AACL,EAAE,iBAAiB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;AAC7C,EAAE,OAAO;AACT,IAAI,eAAe;AACnB,IAAI,kBAAkB;AACtB,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,WAAW;AACf,cAAIA,UAAQ;AACZ,IAAI,eAAe;AACnB,IAAI,aAAa;AACjB,IAAI,WAAW;AACf,IAAI,iBAAiB;AACrB,IAAI,UAAU;AACd,IAAI,mBAAmB;AACvB,IAAI,aAAa;AACjB,IAAI,qBAAqB;AACzB,IAAI,cAAc;AAClB,IAAI,UAAU;AACd,IAAI,YAAY;AAChB,IAAI,MAAM;AACV,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAI,OAAO;AACX,IAAI,aAAa;AACjB,IAAI,UAAU;AACd,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,MAAM;AACV,IAAI,SAAS;AACb,IAAI,YAAY;AAChB,IAAI,SAAS;AACb,IAAI,aAAa;AACjB,IAAI,YAAY;AAChB,IAAI,WAAW;AACf,IAAI,eAAe;AACnB,IAAI,sBAAsB;AAC1B,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAI,WAAW;AACf,IAAI,kBAAkB;AACtB,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,WAAW;AACf,IAAI,eAAe;AACnB,IAAI,YAAY;AAChB,IAAI,UAAU;AACd,IAAI,QAAQ,EAAE,YAAY;AAC1B,IAAI,OAAO;AACX,IAAI,kBAAkB;AACtB,IAAI,gBAAgB;AACpB,IAAI,QAAQ;AACZ,IAAI,OAAO,EAAE,mBAAmB;AAChC,IAAI,kBAAkB;AACtB,IAAI,sBAAsB;AAC1B,IAAI,oBAAoB;AACxB,IAAI,uBAAuB;AAC3B,GAAG,CAAC;AACJ;;;;"}