{"version": 3, "file": "group-item.mjs", "sources": ["../../../../../../packages/components/select-v2/src/group-item.vue"], "sourcesContent": ["<template>\n  <div\n    v-if=\"item.isTitle\"\n    :class=\"ns.be('group', 'title')\"\n    :style=\"[style, { lineHeight: `${height}px` }]\"\n  >\n    {{ item.label }}\n  </div>\n  <div v-else :class=\"ns.be('group', 'split')\" :style=\"style\">\n    <span\n      :class=\"ns.be('group', 'split-dash')\"\n      :style=\"{ top: `${height / 2}px` }\"\n    />\n  </div>\n</template>\n\n<script lang=\"ts\">\n// @ts-nocheck\nimport { defineComponent } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\n\nexport default defineComponent({\n  props: {\n    item: {\n      type: Object,\n      required: true,\n    },\n    style: Object,\n    height: Number,\n  },\n  setup() {\n    const ns = useNamespace('select')\n    return {\n      ns,\n    }\n  },\n})\n</script>\n"], "names": ["_createElementBlock", "_normalizeClass", "_normalizeStyle", "_toDisplayString", "_openBlock", "_createElementVNode"], "mappings": ";;;;;AAqBA,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,KAAO,EAAA;AAAA,IACL,IAAM,EAAA;AAAA,MACJ,IAAM,EAAA,MAAA;AAAA,MACN,QAAU,EAAA,IAAA;AAAA,KACZ;AAAA,IACA,KAAO,EAAA,MAAA;AAAA,IACP,MAAQ,EAAA,MAAA;AAAA,GACV;AAAA,EACA,KAAQ,GAAA;AACN,IAAM,MAAA,EAAA,GAAK,aAAa,QAAQ,CAAA,CAAA;AAChC,IAAO,OAAA;AAAA,MACL,EAAA;AAAA,KACF,CAAA;AAAA,GACF;AACF,CAAC,CAAA,CAAA;;AAlCS,EAAA,OAAA,IAAA,CAAA,IAAA,CAAK,wBADbA,kBAMM,CAAA,KAAA,EAAA;AAAA,IAAA,GAAA,EAAA,CAAA;IAJH,KAAK,EAAAC,cAAA,CAAE,QAAG,EAAE,CAAA,OAAA,EAAA,OAAA,CAAA,CAAA;AAAA,IACZ,KAAK,EAAAC,cAAA,CAAA,CAAG,IAAK,CAAA,KAAA,EAAA,EAAA,UAAA,EAAA,CAAA,EAAmB,IAAM,CAAA,MAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA;AAAA,GAEpC,EAAAC,eAAA,CAAA,IAAA,CAAA,IAAA,CAAK,KAAK,CAAA,EAAA,CAAA,CAAA,KAAAC,SAAA,EAAA,EAEfJ,kBAKM,CAAA,KAAA,EAAA;AAAA,IAAA,GAAA,EAAA,CAAA;IALO,KAAK,EAAAC,cAAA,CAAE,QAAG,EAAE,CAAA,OAAA,EAAA,OAAA,CAAA,CAAA;AAAA,IAAqB,KAAA,EAAKC,eAAE,IAAK,CAAA,KAAA,CAAA;AAAA,GAAA,EAAA;IACxDG,kBAGE,CAAA,MAAA,EAAA;AAAA,MAFC,KAAA,EAAKJ,cAAE,CAAA,IAAA,CAAA,EAAA,CAAG,EAAE,CAAA,OAAA,EAAA,YAAA,CAAA,CAAA;AAAA,MACZ,KAAA,EAAKC,yBAAY,IAAM,CAAA,MAAA,GAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA;AAAA,KAAA,EAAA,IAAA,EAAA,CAAA,CAAA;;;;;;;"}