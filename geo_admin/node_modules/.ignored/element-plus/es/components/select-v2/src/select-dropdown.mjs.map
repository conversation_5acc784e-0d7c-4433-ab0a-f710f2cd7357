{"version": 3, "file": "select-dropdown.mjs", "sources": ["../../../../../../packages/components/select-v2/src/select-dropdown.tsx"], "sourcesContent": ["import {\n  computed,\n  defineComponent,\n  inject,\n  ref,\n  toRaw,\n  unref,\n  watch,\n} from 'vue'\nimport { get } from 'lodash-unified'\nimport { isObject, isUndefined } from '@element-plus/utils'\nimport {\n  DynamicSizeList,\n  FixedSizeList,\n} from '@element-plus/components/virtual-list'\nimport { useNamespace } from '@element-plus/hooks'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport GroupItem from './group-item.vue'\nimport OptionItem from './option-item.vue'\nimport { useProps } from './useProps'\n\nimport { selectV2InjectionKey } from './token'\n\nimport type { ItemProps } from '@element-plus/components/virtual-list'\nimport type { Option, OptionItemProps } from './select.types'\n\nexport default defineComponent({\n  name: 'ElSelectDropdown',\n\n  props: {\n    data: {\n      type: Array,\n      required: true,\n    },\n    hoveringIndex: Number,\n    width: Number,\n  },\n  setup(props, { slots, expose }) {\n    const select = inject(selectV2InjectionKey)!\n    const ns = useNamespace('select')\n    const { getLabel, getValue, getDisabled } = useProps(select.props)\n\n    const cachedHeights = ref<Array<number>>([])\n\n    const listRef = ref()\n\n    const size = computed(() => props.data.length)\n    watch(\n      () => size.value,\n      () => {\n        select.popper.value.updatePopper?.()\n      }\n    )\n\n    const isSized = computed(() =>\n      isUndefined(select.props.estimatedOptionHeight)\n    )\n    const listProps = computed(() => {\n      if (isSized.value) {\n        return {\n          itemSize: select.props.itemHeight,\n        }\n      }\n\n      return {\n        estimatedSize: select.props.estimatedOptionHeight,\n        itemSize: (idx: number) => cachedHeights.value[idx],\n      }\n    })\n\n    const contains = (arr: Array<any> = [], target: any) => {\n      const {\n        props: { valueKey },\n      } = select\n\n      if (!isObject(target)) {\n        return arr.includes(target)\n      }\n\n      return (\n        arr &&\n        arr.some((item) => {\n          return toRaw(get(item, valueKey)) === get(target, valueKey)\n        })\n      )\n    }\n    const isEqual = (selected: unknown, target: unknown) => {\n      if (!isObject(target)) {\n        return selected === target\n      } else {\n        const { valueKey } = select.props\n        return get(selected, valueKey) === get(target, valueKey)\n      }\n    }\n\n    const isItemSelected = (modelValue: any[] | any, target: Option) => {\n      if (select.props.multiple) {\n        return contains(modelValue, getValue(target))\n      }\n      return isEqual(modelValue, getValue(target))\n    }\n\n    const isItemDisabled = (modelValue: any[] | any, selected: boolean) => {\n      const { disabled, multiple, multipleLimit } = select.props\n      return (\n        disabled ||\n        (!selected &&\n          (multiple\n            ? multipleLimit > 0 && modelValue.length >= multipleLimit\n            : false))\n      )\n    }\n\n    const isItemHovering = (target: number) => props.hoveringIndex === target\n\n    const scrollToItem = (index: number) => {\n      const list = listRef.value as any\n      if (list) {\n        list.scrollToItem(index)\n      }\n    }\n\n    const resetScrollTop = () => {\n      const list = listRef.value as any\n      if (list) {\n        list.resetScrollTop()\n      }\n    }\n\n    expose({\n      listRef,\n      isSized,\n\n      isItemDisabled,\n      isItemHovering,\n      isItemSelected,\n      scrollToItem,\n      resetScrollTop,\n    })\n\n    const Item = (itemProps: ItemProps<any>) => {\n      const { index, data, style } = itemProps\n      const sized = unref(isSized)\n      const { itemSize, estimatedSize } = unref(listProps)\n      const { modelValue } = select.props\n      const { onSelect, onHover } = select\n      const item = data[index]\n      if (item.type === 'Group') {\n        return (\n          <GroupItem\n            item={item}\n            style={style}\n            height={(sized ? itemSize : estimatedSize) as number}\n          />\n        )\n      }\n\n      const isSelected = isItemSelected(modelValue, item)\n      const isDisabled = isItemDisabled(modelValue, isSelected)\n      const isHovering = isItemHovering(index)\n      return (\n        <OptionItem\n          {...itemProps}\n          selected={isSelected}\n          disabled={getDisabled(item) || isDisabled}\n          created={!!item.created}\n          hovering={isHovering}\n          item={item}\n          onSelect={onSelect}\n          onHover={onHover}\n        >\n          {{\n            default: (props: OptionItemProps) =>\n              slots.default?.(props) || <span>{getLabel(item)}</span>,\n          }}\n        </OptionItem>\n      )\n    }\n\n    // computed\n    const { onKeyboardNavigate, onKeyboardSelect } = select\n\n    const onForward = () => {\n      onKeyboardNavigate('forward')\n    }\n\n    const onBackward = () => {\n      onKeyboardNavigate('backward')\n    }\n\n    const onEscOrTab = () => {\n      select.expanded = false\n    }\n\n    const onKeydown = (e: KeyboardEvent) => {\n      const { code } = e\n      const { tab, esc, down, up, enter } = EVENT_CODE\n      if (code !== tab) {\n        e.preventDefault()\n        e.stopPropagation()\n      }\n\n      switch (code) {\n        case tab:\n        case esc: {\n          onEscOrTab()\n          break\n        }\n        case down: {\n          onForward()\n          break\n        }\n        case up: {\n          onBackward()\n          break\n        }\n        case enter: {\n          onKeyboardSelect()\n          break\n        }\n      }\n    }\n\n    return () => {\n      const { data, width } = props\n      const { height, multiple, scrollbarAlwaysOn } = select.props\n\n      if (data.length === 0) {\n        return (\n          <div\n            class={ns.b('dropdown')}\n            style={{\n              width: `${width}px`,\n            }}\n          >\n            {slots.empty?.()}\n          </div>\n        )\n      }\n\n      const List = unref(isSized) ? FixedSizeList : DynamicSizeList\n\n      return (\n        <div class={[ns.b('dropdown'), ns.is('multiple', multiple)]}>\n          <List\n            ref={listRef}\n            {...unref(listProps)}\n            className={ns.be('dropdown', 'list')}\n            scrollbarAlwaysOn={scrollbarAlwaysOn}\n            data={data}\n            height={height}\n            width={width}\n            total={data.length}\n            // @ts-ignore - dts problem\n            onKeydown={onKeydown}\n          >\n            {{\n              default: (props: ItemProps<any>) => <Item {...props} />,\n            }}\n          </List>\n        </div>\n      )\n    }\n  },\n})\n"], "names": ["defineComponent", "name", "props", "data", "type", "Array", "required", "hoveringIndex", "Number", "width", "slots", "expose", "select", "ns", "get<PERSON><PERSON><PERSON>", "getValue", "getDisabled", "cachedHeights", "ref", "listRef", "size", "computed", "watch", "popper", "value", "isSized", "isUndefined", "estimatedOptionHeight", "listProps", "itemSize", "itemHeight", "estimatedSize", "contains", "valueKey", "isObject", "target", "arr", "item", "isEqual", "selected", "get", "isItemSelected", "modelValue", "isItemDisabled", "multipleLimit", "isItemHovering", "scrollToItem", "resetScrollTop", "list", "<PERSON><PERSON>", "itemProps", "style", "_createVNode", "unref", "_mergeProps", "onSelect", "onHover", "sized", "default", "onKeyboardNavigate", "onKeyboardSelect", "onForward", "onBackward", "onEscOrTab", "expanded", "onKeydown", "code", "e", "tab", "enter", "EVENT_CODE", "esc"], "mappings": ";;;;;;;;;;;;;;;;;AA0BA,mBAAeA,eAAe,CAAC;AAC7BC,EAAAA,IAAI,EAAE,kBADuB;AAG7BC,EAAAA,KAAK,EAAE;AACLC,IAAAA,IAAI,EAAE;AACJC,MAAAA,IAAI,EAAEC,KADF;AAEJC,MAAAA,QAAQ,EAAE,IAAA;KAHP;AAKLC,IAAAA,aAAa,EAAEC,MALV;AAMLC,IAAAA,KAAK,EAAED,MAAAA;GAToB;;IAWxB;IAAUE,MAAF;AAASC,GAAAA,EAAAA;AAAT,IAAmB,MAAA,MAAA,GAAA,MAAA,CAAA,oBAAA,CAAA,CAAA;AAC9B,IAAA,MAAMC,EAAM,GAAA;AACZ,IAAA,MAAMC;MACA,QAAA;MAAEC,QAAF;MAAYC,WAAZ;AAAsBC,KAAAA,GAAAA,QAAAA,CAAAA,MAAAA,CAAAA,KAAAA,CAAAA,CAAAA;AAAtB,IAAA,MAA8C,aAAO,MAAP,CAApD,EAAA,CAAA,CAAA;AAEA,IAAA,MAAMC,OAAa,GAAA,GAAA,EAAA,CAAGC;IAEtB,MAAMC,IAAAA,GAAO,QAAb,CAAA,MAAA,KAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;IAEA,KAAMC,CAAAA,MAAOC,IAAAA,CAAAA,KAAS,EAAA;AACtBC,MAAAA,IACE,EAAMF,EAAAA,EAAAA,CAAAA;AAEJR,MAAAA,CAAAA,EAAAA,GAAM,CAACW,EAAAA,GAAAA,MAAOC,CAAd,MAAA,CAAA,KAAA,EAAA,YAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;AACD,KAJE,CAAL,CAAA;AAOA,IAAA,MAAMC,OAAO,GAAGJ,QAAQ,CAAC,MACvBK,WAAW,CAACd,MAAM,CAACV,KAAP,CAAayB,qBAAd,CADW,CAAxB,CAAA;AAGA,IAAA,MAAMC,SAAS,GAAGP,QAAQ,CAAC,MAAM;MAC/B,IAAII,OAAO,CAACD,KAAZ,EAAmB;QACjB,OAAO;AACLK,UAAAA,QAAQ,EAAEjB,MAAM,CAACV,KAAP,CAAa4B,UAAAA;SADzB,CAAA;AAGD,OAAA;;QAEM,aAAA,EAAA,MAAA,CAAA,KAAA,CAAA,qBAAA;AACLC,QAAAA,QAAAA,EAAAA,CAAAA,GAAenB,KAAAA,aADV,CAAA,KAAA,CAAA,GAAA,CAAA;AAELiB,OAAAA,CAAAA;MAFK,CAAP;AAID,IAAA,MAXD,QAAA,GAAA,CAAA,GAAA,GAAA,EAAA,EAAA,MAAA,KAAA;;QAaMG,KAAAA,EAAAA;UACE,QAAA;AACJ9B,SAAAA;AAAS+B,OAAAA,GAAAA,MAAAA,CAAAA;AAAF,MAAA,IAAA,CAAA,QAAA,CAAA,MAAA,CAAA,EAAA;AADH,QAAA,OAAN,GAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA;;AAIA,MAAA,OAAKC,GAAAA,IAASC,GAAAA,CAAAA,IAAS,CAAA,CAAA,IAAA,KAAA;AACrB,QAAA,OAAOC,KAAA,CAAA,GAAA,CAAA,cAAP,CAAA,CAAA,KAAA,GAAA,CAAA,MAAA,EAAA,QAAA,CAAA,CAAA;AACD,OAAA,CAAA,CAAA;;AAED,IAAA,MAAA,OAEEA,GAAAA,CAAAA,QAAUC,QAAS,KAAA;AACjB,MAAA,IAAA,CAAA,QAAY,CAAA,SAAI;AACjB,QAJH,OAAA,QAAA,KAAA,MAAA,CAAA;OATF,MAAA;;AAgBA,UAAMC,QAAU;AACd,SAAA,GAAKJ,MAAAA,CAAQ,KAACC,CAAAA;QACZ,OAAOI,GAAAA,CAAAA,UAAP,QAAA,CAAA,KAAA,GAAA,CAAA,MAAA,EAAA,QAAA,CAAA,CAAA;AACD,OAFD;;AAGUN,IAAAA,MAAAA,cAAAA,GAAAA,CAAAA,UAAAA,EAAAA,MAAAA,KAAAA;UAAarB,MAAAA,CAAAA,KAArB,CAAA,QAAA,EAAA;AACA,QAAA,OAAO4B,QAAG,CAAA,UAAA,EAAA,QAAyBA,CAAAA,MAAIL,CAAAA,CAAD,CAASF;AAChD,OAAA;MANH,OAAA,OAAA,CAAA,UAAA,EAAA,QAAA,CAAA,MAAA,CAAA,CAAA,CAAA;;AASA,IAAA,MAAMQ,cAAc,GAAG,CAACC,UAAD,EAA0BP,QAAmB,KAAA;AAClE,MAAA,MAAU;QACR,QAAOH;AACR,QAAA,QAAA;;OACMM,GAAAA,MAAAA,CAAAA,KAAO,CAAA;MAJhB,OAAA,QAAA,IAAA,CAAA,QAAA,KAAA,QAAA,GAAA,aAAA,GAAA,CAAA,IAAA,UAAA,CAAA,MAAA,IAAA,aAAA,GAAA,KAAA,CAAA,CAAA;;AAOA,IAAA,MAAMK,cAAc,GAAG,CAACD,MAAD,KAAA,mBAAgD,KAAA,MAAA,CAAA;UAC/D,YAAA,GAAA,CAAA,KAAA,KAAA;YAAA,IAAA,GAAA,OAAA,CAAA,KAAA,CAAA;UAAA,IAAA,EAAA;AAAsBE,QAAAA,IAAAA,CAAAA,YAAAA,CAAAA,KAAAA,CAAAA,CAAAA;OAAkBhC;AAC9C,KAAA,CAAA;IAOD,MATD,cAAA,GAAA,MAAA;;MAWMiC,IAAAA,IAAAA,EAAAA;;OAEAC;AACJ,KAAA,CAAA;;AACA,MAAA;aACOA;AACN,MAAA,cAAA;MAJH,cAAA;;MAOMC,YAAAA;AACJ,MAAA,cAAoB;;AACpB,IAAA,MAAIC,IAAJ,GAAU,CAAA,SAAA,KAAA;AACRA,MAAAA,MAAI;AACL,QAAA,KAAA;QAJH,IAAA;;AAOArC,OAAAA,GAAM,SAAC,CAAA;MACLQ,MADK,KAAA,GAAA,KAAA,CAAA,OAAA,CAAA,CAAA;MAELM,MAFK;QAAA,QAAA;QAAA,aAAA;OAAA,GAAA,KAAA,CAAA,SAAA,CAAA,CAAA;MAOLqB,MAPK;AAQLC,QAAAA,UAAAA;AARK,OAAP,GAAA,MAAA,CAAA,KAAA,CAAA;;QAWME,QAAQC;QACN,OAAA;UAAA,MAAA,CAAA;YAAA,IAAA,GAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAAeC,MAAAA,IAAAA,IAAAA,CAAAA,IAAAA,KAAAA,OAAAA,EAAAA;AAAf,QAAA,OAANC,WAAA,CAAA,SAAA,EAAA;AACA,UAAA,MAAW,EAAA,IAAQ;UACb,OAAA,EAAA,KAAA;UAAA,QAAA,EAAA,KAAA,GAAA,QAAA,GAAA,aAAA;AAAYrB,SAAAA,EAAAA,IAAAA,CAAAA,CAAAA;OAAkBsB;MACpC,MAAM,UAAA,GAAA,cAAA,CAAA,UAAA,EAAA,IAAA,CAAA,CAAA;AAAEX,MAAAA,MAAAA,UAAAA,GAAAA,cAAAA,CAAAA,UAAAA,EAAAA,UAAAA,CAAAA,CAAAA;MAAF,MAAiB9B,UAAvB,GAAA,cAAA,CAAA,KAAA,CAAA,CAAA;MACA,OAAMwC,WAAA,CAAA,UAAA,EAAAE,UAAA,CAAA,SAAA,EAAA;QAAEC,UAAF,EAAA,UAAA;AAAYC,QAAAA,UAAAA,EAAAA,WAAAA,CAAAA,IAAAA,CAAAA,IAAAA,UAAAA;AAAZ,QAAA,SAAN,EAAA,CAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,UAAU,EAAO,UAAjB;;AACA,QAAA,UAAI,EAAA;AACF,QAAA,SAAA,EAAA,OAAA;AAAA,OAAA,CAAA,EAAA;AAAA,QAAA,OAAA,EAAA,CAAA,MAAA,KAAA;UAAA,IAIaC,EAAAA,CAAAA;AAJb,UAAA,OAAA,CAAA,CAAA,EAAA,GAAA,KAAA,CAAA,OAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,EAAA,MAAA,CAAA,KAAAL,WAAA,CAAA,MAAA,EAAA,IAAA,EAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;AAOD,SAAA;;AAED,KAAA,CAAA;AACA,IAAA,MAAA;AACA,MAAA,kBAAgB;AAChB,MAAA,gBAAA;AAAA,KAAA,GAAA,MAAA,CAAA;AAAA,IAAA,MAAA,SAAA,GAIyB,MAAA;AAJzB,MAAA,kBAKmB,CAAA,SALnB,CAAA,CAAA;AAAA,KAAA,CAAA;AAAA,IAAA,MAAA,UAAA,GAAA,MAAA;AAAA,MAAA,kBAAA,CAAA,UAAA,CAAA,CAAA;;AAAA,IAAA,MAAA,UAAA,GAAA,MAAA;AAYMM,MAAAA,MAAAA,CAAAA,QAAUxD,GACRQ,KAAAA,CAAAA;AAbR,KAAA,CAAA;AAiBD,IAAA,MAED,SAAA,GAAA,CAAA,CAAA,KAAA;;;OACM,GAAA,CAAA,CAAA;MAAEiD,MAAF;AAAsBC,QAAAA,GAAAA;AAAtB,QAA2ChD,GAAjD;;QAEMiD,EAAAA;QACc,KAAA;OADpB,GAAA,UAAA,CAAA;;QAIMC,CAAAA,CAAAA,cAAmB,EAAA,CAAA;QACL,CAAA,CAAA,eAAC;OADrB;;QAIMC,KAAAA,GAAAA,CAAAA;QACE,KAACC,GAAP,EAAA;UADF,UAAA,EAAA,CAAA;;SAIMC;QACE,KAAA,IAAA,EAAA;AAAEC,UAAAA,SAAAA,EAAAA,CAAAA;AAAF,UAAWC,MAAjB;SACM;QAAEC,KAAF,EAAA,EAAA;UAAA,UAAA,EAAA,CAAA;UAAA,MAAA;SAAA;AAAsBC,QAAAA,KAAAA,KAAAA,EAAAA;AAAtB,UAAgCC,gBAAtC,EAAA,CAAA;;SACIJ;AACFC,OAAAA;AACAA,KAAAA,CAAAA;AACD,IAAA,OAAA,MAAA;;AAED,MAAA,MAAA;AACE,QAAA,IAAA;AACA,QAAA,KAAKI;AAAK,OAAA,GAAA,KAAA,CAAA;YACRR;AACA,QAAA,MAAA;AACD,QAAA,QAAA;;AACD,OAAA,GAAA,MAAA,CAAA,KAAA,CAAA;AAAW,MAAA,IAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;eACAX,WAAA,CAAA,KAAA,EAAA;AACT,UAAA,OAAA,EAAA,EAAA,CAAA,CAAA,CAAA,UAAA,CAAA;AACD,UAAA,OAAA,EAAA;;AACD,WAAA;AAAS,SAAA,EAAA,CAAA,CAAA,EAAA,GAAA,KAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;;AAEP,MAAA,MAAA,IAAA,GAAA,KAAA,CAAA,OAAA,CAAA,GAAA,aAAA,GAAA,eAAA,CAAA;AACD,MAAA,OAAAA,WAAA,CAAA,KAAA,EAAA;;AACD,OAAA,EAAA,CAAAA,WAAA,CAAA,IAAA,EAAAE,UAAA,CAAA;AAAY,QAAA,KAAA,EAAA,OAAA;cACM,CAAA,SAAA,CAAA,EAAA;AAChB,QAAA,WAAA,EAAA,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,MAAA,CAAA;AACD,QAAA,mBAAA,EAAA,iBAAA;AAjBH,QAAA,MAAA,EAAA,IAAA;QARF,QAAA,EAAA,MAAA;;AA6BA,QAAA,OAAa,EAAA,IAAA,CAAA,MAAA;QACL,WAAA,EAAA,SAAA;QAAEnD,EAAF;AAAQM,QAAAA,OAAAA,EAAAA,CAAAA,MAAAA,KAAAA,WAAAA,CAAAA,IAAAA,EAAAA,MAAAA,EAAAA,IAAAA,CAAAA;AAAR,OAAA,CAAA,CAAA,CAAkBP,CAAxB;MACA;;;;;;"}