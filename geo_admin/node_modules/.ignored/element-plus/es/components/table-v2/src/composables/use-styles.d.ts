import type { CSSProperties } from 'vue';
import type { TableV2Props } from '../table';
import type { UseColumnsReturn } from './use-columns';
import type { UseDataReturn } from './use-data';
declare type UseStyleProps = {
    columnsTotalWidth: UseColumnsReturn['columnsTotalWidth'];
    data: UseDataReturn['data'];
    fixedColumnsOnLeft: UseColumnsReturn['fixedColumnsOnLeft'];
    fixedColumnsOnRight: UseColumnsReturn['fixedColumnsOnRight'];
};
export declare const useStyles: (props: TableV2Props, { columnsTotalWidth, data, fixedColumnsOnLeft, fixedColumnsOnRight, }: UseStyleProps) => {
    bodyWidth: import("vue").ComputedRef<number>;
    fixedTableHeight: import("vue").ComputedRef<number>;
    mainTableHeight: import("vue").ComputedRef<number>;
    leftTableWidth: import("vue").ComputedRef<number>;
    rightTableWidth: import("vue").ComputedRef<number>;
    headerWidth: import("vue").ComputedRef<number>;
    rowsHeight: import("vue").ComputedRef<number>;
    windowHeight: import("vue").ComputedRef<number>;
    footerHeight: import("vue").ComputedRef<CSSProperties>;
    emptyStyle: import("vue").ComputedRef<CSSProperties>;
    rootStyle: import("vue").ComputedRef<CSSProperties>;
    headerHeight: import("vue").ComputedRef<number>;
};
export declare type UseStyleReturn = ReturnType<typeof useStyles>;
export {};
