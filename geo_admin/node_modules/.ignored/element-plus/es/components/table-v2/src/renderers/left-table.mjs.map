{"version": 3, "file": "left-table.mjs", "sources": ["../../../../../../../packages/components/table-v2/src/renderers/left-table.tsx"], "sourcesContent": ["import Table from '../table-grid'\n\nimport type { FunctionalComponent, Ref } from 'vue'\nimport type { TableV2GridProps } from '../grid'\nimport type { TableGridInstance } from '../table-grid'\n\ntype LeftTableProps = TableV2GridProps & {\n  leftTableRef: Ref<TableGridInstance | undefined>\n}\n\nconst LeftTable: FunctionalComponent<LeftTableProps> = (props, { slots }) => {\n  if (!props.columns.length) return\n\n  const { leftTableRef, ...rest } = props\n\n  return (\n    <Table ref={leftTableRef} {...rest}>\n      {slots}\n    </Table>\n  )\n}\n\nexport default LeftTable\n"], "names": ["LeftTable", "slots", "leftTableRef", "rest", "_createVNode", "Table", "_mergeProps"], "mappings": ";;;;;;;;AAUA,CAAA,KAAMA;AAA2DC,EAAAA,IAAAA,CAAAA,KAAAA,CAAAA,OAAAA,CAAAA,MAAAA;AAAF,IAAc,OAAA;AAC3E,EAAA,MAAU;IAEJ,YAAA;IAAEC,GAAF,IAAA;MAAmBC,KAAAA,CAAAA;AAAnB,EAAA,OAANC,WAAA,CAAAC,SAAA,EAAAC,UAAA,CAAA;AAEA,IAAA,KAAA,EAAA,YAAA;KACcJ,IAAAA,CAAAA,EAAAA,OAAAA,CAAAA,KAAAA,CAAAA,GAAAA,KAAAA,GAAAA;AADd,IAAA,OAAA,EAAA,MAAA,CAEKD,KAFL,CAAA;AAAA,GAAA,CAAA,CAAA;AAAA;;;;"}