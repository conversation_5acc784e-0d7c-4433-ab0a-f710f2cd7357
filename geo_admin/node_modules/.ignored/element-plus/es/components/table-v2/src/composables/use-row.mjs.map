{"version": 3, "file": "use-row.mjs", "sources": ["../../../../../../../packages/components/table-v2/src/composables/use-row.ts"], "sourcesContent": ["import { computed, getCurrentInstance, ref, shallowRef, unref } from 'vue'\nimport { debounce } from 'lodash-unified'\nimport { isNumber } from '@element-plus/utils'\nimport { FixedDir } from '../constants'\n\nimport type { Ref } from 'vue'\nimport type { TableV2Props } from '../table'\nimport type {\n  RowExpandParams,\n  RowHeightChangedParams,\n  RowHoverParams,\n} from '../row'\nimport type { FixedDirection, KeyType } from '../types'\nimport type { onRowRenderedParams } from '../grid'\nimport type { TableGridInstance } from '../table-grid'\n\ntype Heights = Record<KeyType, number>\ntype GridInstanceRef = Ref<TableGridInstance | undefined>\n\ntype UseRowProps = {\n  mainTableRef: GridInstanceRef\n  leftTableRef: GridInstanceRef\n  rightTableRef: GridInstanceRef\n}\n\nexport const useRow = (\n  props: TableV2Props,\n  { mainTableRef, leftTableRef, rightTableRef }: UseRowProps\n) => {\n  const vm = getCurrentInstance()!\n  const { emit } = vm\n  const isResetting = shallowRef(false)\n  const hoveringRowKey = shallowRef<KeyType | null>(null)\n  const expandedRowKeys = ref<KeyType[]>(props.defaultExpandedRowKeys || [])\n  const lastRenderedRowIndex = ref(-1)\n  const resetIndex = shallowRef<number | null>(null)\n  const rowHeights = ref<Heights>({})\n  const pendingRowHeights = ref<Heights>({})\n  const leftTableHeights = shallowRef<Heights>({})\n  const mainTableHeights = shallowRef<Heights>({})\n  const rightTableHeights = shallowRef<Heights>({})\n  const isDynamic = computed(() => isNumber(props.estimatedRowHeight))\n\n  function onRowsRendered(params: onRowRenderedParams) {\n    props.onRowsRendered?.(params)\n\n    if (params.rowCacheEnd > unref(lastRenderedRowIndex)) {\n      lastRenderedRowIndex.value = params.rowCacheEnd\n    }\n  }\n\n  function onRowHovered({ hovered, rowKey }: RowHoverParams) {\n    hoveringRowKey.value = hovered ? rowKey : null\n  }\n\n  function onRowExpanded({\n    expanded,\n    rowData,\n    rowIndex,\n    rowKey,\n  }: RowExpandParams) {\n    const _expandedRowKeys = [...unref(expandedRowKeys)]\n    const currentKeyIndex = _expandedRowKeys.indexOf(rowKey)\n    if (expanded) {\n      if (currentKeyIndex === -1) _expandedRowKeys.push(rowKey)\n    } else {\n      if (currentKeyIndex > -1) _expandedRowKeys.splice(currentKeyIndex, 1)\n    }\n    expandedRowKeys.value = _expandedRowKeys\n\n    emit('update:expandedRowKeys', _expandedRowKeys)\n    props.onRowExpand?.({\n      expanded,\n      rowData,\n      rowIndex,\n      rowKey,\n    })\n    // If this is not controlled, then use this to notify changes\n    props.onExpandedRowsChange?.(_expandedRowKeys)\n  }\n\n  const flushingRowHeights = debounce(() => {\n    isResetting.value = true\n    rowHeights.value = { ...unref(rowHeights), ...unref(pendingRowHeights) }\n    resetAfterIndex(unref(resetIndex)!, false)\n    pendingRowHeights.value = {}\n    // force update\n    resetIndex.value = null\n    mainTableRef.value?.forceUpdate()\n    leftTableRef.value?.forceUpdate()\n    rightTableRef.value?.forceUpdate()\n    vm.proxy?.$forceUpdate()\n    isResetting.value = false\n  }, 0)\n\n  function resetAfterIndex(index: number, forceUpdate = false) {\n    if (!unref(isDynamic)) return\n    ;[mainTableRef, leftTableRef, rightTableRef].forEach((tableRef) => {\n      const table = unref(tableRef)\n      if (table) table.resetAfterRowIndex(index, forceUpdate)\n    })\n  }\n\n  function resetHeights(rowKey: KeyType, height: number, rowIdx: number) {\n    const resetIdx = unref(resetIndex)\n    if (resetIdx === null) {\n      resetIndex.value = rowIdx\n    } else {\n      if (resetIdx > rowIdx) {\n        resetIndex.value = rowIdx\n      }\n    }\n\n    pendingRowHeights.value[rowKey] = height\n  }\n\n  function onRowHeightChange(\n    { rowKey, height, rowIndex }: RowHeightChangedParams,\n    fixedDir: FixedDirection\n  ) {\n    if (!fixedDir) {\n      mainTableHeights.value[rowKey] = height\n    } else {\n      if (fixedDir === FixedDir.RIGHT) {\n        rightTableHeights.value[rowKey] = height\n      } else {\n        leftTableHeights.value[rowKey] = height\n      }\n    }\n\n    const maximumHeight = Math.max(\n      ...[leftTableHeights, rightTableHeights, mainTableHeights].map(\n        (records) => records.value[rowKey] || 0\n      )\n    )\n\n    if (unref(rowHeights)[rowKey] !== maximumHeight) {\n      resetHeights(rowKey, maximumHeight, rowIndex)\n      flushingRowHeights()\n    }\n  }\n\n  return {\n    hoveringRowKey,\n    expandedRowKeys,\n    lastRenderedRowIndex,\n    isDynamic,\n    isResetting,\n    rowHeights,\n\n    resetAfterIndex,\n    onRowExpanded,\n    onRowHovered,\n    onRowsRendered,\n    onRowHeightChange,\n  }\n}\n\nexport type UseRowReturn = ReturnType<typeof useRow>\n"], "names": [], "mappings": ";;;;;;AAIY,MAAC,MAAM,GAAG,CAAC,KAAK,EAAE,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,KAAK;AAChF,EAAE,MAAM,EAAE,GAAG,kBAAkB,EAAE,CAAC;AAClC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;AACtB,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;AACxC,EAAE,MAAM,cAAc,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;AAC1C,EAAE,MAAM,eAAe,GAAG,GAAG,CAAC,KAAK,CAAC,sBAAsB,IAAI,EAAE,CAAC,CAAC;AAClE,EAAE,MAAM,oBAAoB,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,EAAE,MAAM,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;AACtC,EAAE,MAAM,UAAU,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;AAC7B,EAAE,MAAM,iBAAiB,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;AACpC,EAAE,MAAM,gBAAgB,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC;AAC1C,EAAE,MAAM,gBAAgB,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC;AAC1C,EAAE,MAAM,iBAAiB,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC;AAC3C,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,QAAQ,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC;AACvE,EAAE,SAAS,cAAc,CAAC,MAAM,EAAE;AAClC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,cAAc,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC1E,IAAI,IAAI,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC,oBAAoB,CAAC,EAAE;AAC1D,MAAM,oBAAoB,CAAC,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC;AACtD,KAAK;AACL,GAAG;AACH,EAAE,SAAS,YAAY,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE;AAC7C,IAAI,cAAc,CAAC,KAAK,GAAG,OAAO,GAAG,MAAM,GAAG,IAAI,CAAC;AACnD,GAAG;AACH,EAAE,SAAS,aAAa,CAAC;AACzB,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,MAAM;AACV,GAAG,EAAE;AACL,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,MAAM,gBAAgB,GAAG,CAAC,GAAG,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;AACzD,IAAI,MAAM,eAAe,GAAG,gBAAgB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC7D,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,IAAI,eAAe,KAAK,CAAC,CAAC;AAChC,QAAQ,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACtC,KAAK,MAAM;AACX,MAAM,IAAI,eAAe,GAAG,CAAC,CAAC;AAC9B,QAAQ,gBAAgB,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;AACpD,KAAK;AACL,IAAI,eAAe,CAAC,KAAK,GAAG,gBAAgB,CAAC;AAC7C,IAAI,IAAI,CAAC,wBAAwB,EAAE,gBAAgB,CAAC,CAAC;AACrD,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,WAAW,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE;AAC/D,MAAM,QAAQ;AACd,MAAM,OAAO;AACb,MAAM,QAAQ;AACd,MAAM,MAAM;AACZ,KAAK,CAAC,CAAC;AACP,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,oBAAoB,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;AAC1F,GAAG;AACH,EAAE,MAAM,kBAAkB,GAAG,QAAQ,CAAC,MAAM;AAC5C,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACvB,IAAI,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC;AAC7B,IAAI,UAAU,CAAC,KAAK,GAAG,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,EAAE,GAAG,KAAK,CAAC,iBAAiB,CAAC,EAAE,CAAC;AAC7E,IAAI,eAAe,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC,CAAC;AAC9C,IAAI,iBAAiB,CAAC,KAAK,GAAG,EAAE,CAAC;AACjC,IAAI,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC;AAC5B,IAAI,CAAC,EAAE,GAAG,YAAY,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE,CAAC;AAClE,IAAI,CAAC,EAAE,GAAG,YAAY,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE,CAAC;AAClE,IAAI,CAAC,EAAE,GAAG,aAAa,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE,CAAC;AACnE,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,YAAY,EAAE,CAAC;AACzD,IAAI,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC;AAC9B,GAAG,EAAE,CAAC,CAAC,CAAC;AACR,EAAE,SAAS,eAAe,CAAC,KAAK,EAAE,WAAW,GAAG,KAAK,EAAE;AACvD,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;AACzB,MAAM,OAAO;AACb,IAAI,CAAC,YAAY,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAK;AACtE,MAAM,MAAM,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;AACpC,MAAM,IAAI,KAAK;AACf,QAAQ,KAAK,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AACrD,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,SAAS,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;AAChD,IAAI,MAAM,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;AACvC,IAAI,IAAI,QAAQ,KAAK,IAAI,EAAE;AAC3B,MAAM,UAAU,CAAC,KAAK,GAAG,MAAM,CAAC;AAChC,KAAK,MAAM;AACX,MAAM,IAAI,QAAQ,GAAG,MAAM,EAAE;AAC7B,QAAQ,UAAU,CAAC,KAAK,GAAG,MAAM,CAAC;AAClC,OAAO;AACP,KAAK;AACL,IAAI,iBAAiB,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;AAC7C,GAAG;AACH,EAAE,SAAS,iBAAiB,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,QAAQ,EAAE;AACrE,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,gBAAgB,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;AAC9C,KAAK,MAAM;AACX,MAAM,IAAI,QAAQ,KAAK,QAAQ,CAAC,KAAK,EAAE;AACvC,QAAQ,iBAAiB,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;AACjD,OAAO,MAAM;AACb,QAAQ,gBAAgB,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;AAChD,OAAO;AACP,KAAK;AACL,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,gBAAgB,EAAE,iBAAiB,EAAE,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC5I,IAAI,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,KAAK,aAAa,EAAE;AACrD,MAAM,YAAY,CAAC,MAAM,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;AACpD,MAAM,kBAAkB,EAAE,CAAC;AAC3B,KAAK;AACL,GAAG;AACH,EAAE,OAAO;AACT,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAI,oBAAoB;AACxB,IAAI,SAAS;AACb,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAI,eAAe;AACnB,IAAI,aAAa;AACjB,IAAI,YAAY;AAChB,IAAI,cAAc;AAClB,IAAI,iBAAiB;AACrB,GAAG,CAAC;AACJ;;;;"}