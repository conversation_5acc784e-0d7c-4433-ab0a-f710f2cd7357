{"version": 3, "file": "table-v2.mjs", "sources": ["../../../../../../packages/components/table-v2/src/table-v2.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { defineComponent, provide, unref } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useTable } from './use-table'\nimport { TableV2InjectionKey } from './tokens'\nimport { tableV2Props } from './table'\n// renderers\nimport MainTable from './renderers/main-table'\nimport LeftTable from './renderers/left-table'\nimport RightTable from './renderers/right-table'\nimport Row from './renderers/row'\nimport Cell from './renderers/cell'\nimport Header from './renderers/header'\nimport HeaderCell from './renderers/header-cell'\nimport Footer from './renderers/footer'\nimport Empty from './renderers/empty'\nimport Overlay from './renderers/overlay'\n\nimport type { TableGridRowSlotParams } from './table-grid'\nimport type { ScrollStrategy } from './composables/use-scrollbar'\nimport type {\n  TableV2HeaderRendererParams,\n  TableV2HeaderRowCellRendererParams,\n  TableV2RowCellRenderParam,\n} from './components'\n\nconst COMPONENT_NAME = 'ElTableV2'\n\nconst TableV2 = defineComponent({\n  name: COMPONENT_NAME,\n  props: tableV2Props,\n  setup(props, { slots, expose }) {\n    const ns = useNamespace('table-v2')\n\n    const {\n      columnsStyles,\n      fixedColumnsOnLeft,\n      fixedColumnsOnRight,\n      mainColumns,\n      mainTableHeight,\n      fixedTableHeight,\n      leftTableWidth,\n      rightTableWidth,\n      data,\n      depthMap,\n      expandedRowKeys,\n      hasFixedColumns,\n      hoveringRowKey,\n      mainTableRef,\n      leftTableRef,\n      rightTableRef,\n      isDynamic,\n      isResetting,\n      isScrolling,\n\n      bodyWidth,\n      emptyStyle,\n      rootStyle,\n      headerWidth,\n      footerHeight,\n\n      showEmpty,\n\n      // exposes\n      scrollTo,\n      scrollToLeft,\n      scrollToTop,\n      scrollToRow,\n\n      getRowHeight,\n      onColumnSorted,\n      onRowHeightChange,\n      onRowHovered,\n      onRowExpanded,\n      onRowsRendered,\n      onScroll,\n      onVerticalScroll,\n    } = useTable(props)\n\n    expose({\n      /**\n       * @description scroll to a given position\n       * @params params {{ scrollLeft?: number, scrollTop?: number }} where to scroll to.\n       */\n      scrollTo,\n      /**\n       * @description scroll to a given position horizontally\n       * @params scrollLeft {Number} where to scroll to.\n       */\n      scrollToLeft,\n      /**\n       * @description scroll to a given position vertically\n       * @params scrollTop { Number } where to scroll to.\n       */\n      scrollToTop,\n      /**\n       * @description scroll to a given row\n       * @params row {Number} which row to scroll to\n       * @params @optional strategy {ScrollStrategy} use what strategy to scroll to\n       */\n      scrollToRow,\n    })\n\n    provide(TableV2InjectionKey, {\n      ns,\n      isResetting,\n      hoveringRowKey,\n      isScrolling,\n    })\n\n    return () => {\n      const {\n        cache,\n        cellProps,\n        estimatedRowHeight,\n        expandColumnKey,\n        fixedData,\n        headerHeight,\n        headerClass,\n        headerProps,\n        headerCellProps,\n        sortBy,\n        sortState,\n        rowHeight,\n        rowClass,\n        rowEventHandlers,\n        rowKey,\n        rowProps,\n        scrollbarAlwaysOn,\n        indentSize,\n        iconSize,\n        useIsScrolling,\n        vScrollbarSize,\n        width,\n      } = props\n\n      const _data = unref(data)\n\n      const mainTableProps = {\n        cache,\n        class: ns.e('main'),\n        columns: unref(mainColumns),\n        data: _data,\n        fixedData,\n        estimatedRowHeight,\n        bodyWidth: unref(bodyWidth) + vScrollbarSize,\n        headerHeight,\n        headerWidth: unref(headerWidth),\n        height: unref(mainTableHeight),\n        mainTableRef,\n        rowKey,\n        rowHeight,\n        scrollbarAlwaysOn,\n        scrollbarStartGap: 2,\n        scrollbarEndGap: vScrollbarSize,\n        useIsScrolling,\n        width,\n        getRowHeight,\n        onRowsRendered,\n        onScroll,\n      }\n\n      const leftColumnsWidth = unref(leftTableWidth)\n      const _fixedTableHeight = unref(fixedTableHeight)\n\n      const leftTableProps = {\n        cache,\n        class: ns.e('left'),\n        columns: unref(fixedColumnsOnLeft),\n        data: _data,\n        estimatedRowHeight,\n        leftTableRef,\n        rowHeight,\n        bodyWidth: leftColumnsWidth,\n        headerWidth: leftColumnsWidth,\n        headerHeight,\n        height: _fixedTableHeight,\n        rowKey,\n        scrollbarAlwaysOn,\n        scrollbarStartGap: 2,\n        scrollbarEndGap: vScrollbarSize,\n        useIsScrolling,\n        width: leftColumnsWidth,\n        getRowHeight,\n        onScroll: onVerticalScroll,\n      }\n\n      const rightColumnsWidth = unref(rightTableWidth)\n      const rightColumnsWidthWithScrollbar = rightColumnsWidth + vScrollbarSize\n\n      const rightTableProps = {\n        cache,\n        class: ns.e('right'),\n        columns: unref(fixedColumnsOnRight),\n        data: _data,\n        estimatedRowHeight,\n        rightTableRef,\n        rowHeight,\n        bodyWidth: rightColumnsWidthWithScrollbar,\n        headerWidth: rightColumnsWidthWithScrollbar,\n        headerHeight,\n        height: _fixedTableHeight,\n        rowKey,\n        scrollbarAlwaysOn,\n        scrollbarStartGap: 2,\n        scrollbarEndGap: vScrollbarSize,\n        width: rightColumnsWidthWithScrollbar,\n        style: `--${unref(\n          ns.namespace\n        )}-table-scrollbar-size: ${vScrollbarSize}px`,\n        useIsScrolling,\n        getRowHeight,\n        onScroll: onVerticalScroll,\n      }\n      const _columnsStyles = unref(columnsStyles)\n\n      const tableRowProps = {\n        ns,\n        depthMap: unref(depthMap),\n        columnsStyles: _columnsStyles,\n        expandColumnKey,\n        expandedRowKeys: unref(expandedRowKeys),\n        estimatedRowHeight,\n        hasFixedColumns: unref(hasFixedColumns),\n        hoveringRowKey: unref(hoveringRowKey),\n        rowProps,\n        rowClass,\n        rowKey,\n        rowEventHandlers,\n        onRowHovered,\n        onRowExpanded,\n        onRowHeightChange,\n      }\n\n      const tableCellProps = {\n        cellProps,\n        expandColumnKey,\n        indentSize,\n        iconSize,\n        rowKey,\n        expandedRowKeys: unref(expandedRowKeys),\n        ns,\n      }\n\n      const tableHeaderProps = {\n        ns,\n        headerClass,\n        headerProps,\n        columnsStyles: _columnsStyles,\n      }\n\n      const tableHeaderCellProps = {\n        ns,\n\n        sortBy,\n        sortState,\n        headerCellProps,\n        onColumnSorted,\n      }\n\n      const tableSlots = {\n        row: (props: TableGridRowSlotParams) => (\n          <Row {...props} {...tableRowProps}>\n            {{\n              row: slots.row,\n              cell: (props: TableV2RowCellRenderParam) =>\n                slots.cell ? (\n                  <Cell\n                    {...props}\n                    {...tableCellProps}\n                    style={_columnsStyles[props.column.key]}\n                  >\n                    {slots.cell(props)}\n                  </Cell>\n                ) : (\n                  <Cell\n                    {...props}\n                    {...tableCellProps}\n                    style={_columnsStyles[props.column.key]}\n                  />\n                ),\n            }}\n          </Row>\n        ),\n        header: (props: TableV2HeaderRendererParams) => (\n          <Header {...props} {...tableHeaderProps}>\n            {{\n              header: slots.header,\n              cell: (props: TableV2HeaderRowCellRendererParams) =>\n                slots['header-cell'] ? (\n                  <HeaderCell\n                    {...props}\n                    {...tableHeaderCellProps}\n                    style={_columnsStyles[props.column.key]}\n                  >\n                    {slots['header-cell'](props)}\n                  </HeaderCell>\n                ) : (\n                  <HeaderCell\n                    {...props}\n                    {...tableHeaderCellProps}\n                    style={_columnsStyles[props.column.key]}\n                  />\n                ),\n            }}\n          </Header>\n        ),\n      }\n\n      const rootKls = [\n        props.class,\n        ns.b(),\n        ns.e('root'),\n        {\n          [ns.is('dynamic')]: unref(isDynamic),\n        },\n      ]\n\n      const footerProps = {\n        class: ns.e('footer'),\n        style: unref(footerHeight),\n      }\n\n      return (\n        <div class={rootKls} style={unref(rootStyle)}>\n          <MainTable {...mainTableProps}>{tableSlots}</MainTable>\n          <LeftTable {...leftTableProps}>{tableSlots}</LeftTable>\n          <RightTable {...rightTableProps}>{tableSlots}</RightTable>\n          {slots.footer && (\n            <Footer {...footerProps}>{{ default: slots.footer }}</Footer>\n          )}\n          {unref(showEmpty) && (\n            <Empty class={ns.e('empty')} style={unref(emptyStyle)}>\n              {{ default: slots.empty }}\n            </Empty>\n          )}\n          {slots.overlay && (\n            <Overlay class={ns.e('overlay')}>\n              {{ default: slots.overlay }}\n            </Overlay>\n          )}\n        </div>\n      )\n    }\n  },\n})\n\nexport default TableV2\n\nexport type TableV2Instance = InstanceType<typeof TableV2> & {\n  /**\n   * @description scroll to a given position\n   * @params params {{ scrollLeft?: number, scrollTop?: number }} where to scroll to.\n   */\n  scrollTo: (param: { scrollLeft?: number; scrollTop?: number }) => void\n  /**\n   * @description scroll to a given position horizontally\n   * @params scrollLeft {Number} where to scroll to.\n   */\n  scrollToLeft: (scrollLeft: number) => void\n  /**\n   * @description scroll to a given position vertically\n   * @params scrollTop { Number } where to scroll to.\n   */\n  scrollToTop: (scrollTop: number) => void\n  /**\n   * @description scroll to a given row\n   * @params row {Number} which row to scroll to\n   * @params strategy {ScrollStrategy} use what strategy to scroll to\n   */\n  scrollToRow(row: number, strategy?: ScrollStrategy): void\n}\n"], "names": ["Empty", "Overlay", "_isVNode", "COMPONENT_NAME", "TableV2", "name", "props", "expose", "columnsStyles", "fixedColumnsOnLeft", "fixedColumnsOnRight", "mainColumns", "mainTableHeight", "fixedTableHeight", "leftTableWidth", "rightTableWidth", "data", "depthMap", "expandedRowKeys", "hasFixedColumns", "hoveringRowKey", "mainTableRef", "leftTableRef", "rightTableRef", "isDynamic", "isResetting", "isScrolling", "bodyWidth", "emptyStyle", "rootStyle", "headerWidth", "footerHeight", "showEmpty", "scrollTo", "scrollToLeft", "scrollToTop", "scrollToRow", "getRowHeight", "onColumnSorted", "onRowExpanded", "onRowsRendered", "onScroll", "onVerticalScroll", "useTable", "cache", "cellProps", "estimatedRowHeight", "expandColumnKey", "fixedData", "headerHeight", "headerClass", "headerProps", "headerCellProps", "sortBy", "sortState", "rowHeight", "rowClass", "rowEventHandlers", "<PERSON><PERSON><PERSON>", "rowProps", "scrollbarAlwaysOn", "indentSize", "iconSize", "useIsScrolling", "vScrollbarSize", "width", "class", "columns", "unref", "height", "scrollbarStartGap", "scrollbarEndGap", "_data", "style", "tableRowProps", "ns", "_createVNode", "Cell", "_mergeProps", "onRowHeightChange", "Header", "<PERSON><PERSON><PERSON><PERSON>", "_columnsStyles", "row", "cell", "slots", "header"], "mappings": ";;;;;;;;;;;;;;;;;AAeA,SAAOA,SAAW,EAAlB;AACA,EAAOC,OAAAA,aAAP,UAAA,IAAA,MAAA,CAAA,SAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA,CAAA,KAAA,iBAAA,IAAA,CAAAC,OAAA,CAAA,CAAA,CAAA,CAAA;;;;;;AAUA,EAAMC,KAAAA,CAAAA,KAAAA,EAAAA;AAEN,IAAMC,KAAAA;AACJC,IAAAA,MAD8B;AAE9BC,GAAAA,EAAAA;;IACK;MAAQ,aAAA;AAASC,MAAAA,kBAAAA;AAAT,MAAmB,mBAAA;AAC9B,MAAA,WAAuB;MAEjB,eAAA;MACJC,gBADI;MAEJC,cAFI;MAGJC,eAHI;MAIJC,IAJI;MAKJC,QALI;MAMJC,eANI;MAOJC,eAPI;MAQJC,cARI;MASJC,YATI;MAUJC,YAVI;MAWJC,aAXI;MAYJC,SAZI;MAaJC,WAbI;MAcJC,WAdI;MAeJC,SAfI;MAgBJC,UAhBI;MAiBJC,SAjBI;MAkBJC,WAlBI;MAmBJC,YAnBI;MAqBJC,SArBI;MAsBJC,QAtBI;MAuBJC,YAvBI;MAwBJC,WAxBI;MAyBJC,WAzBI;MA2BJC,YA3BI;AA6BJ,MAAA,cAAA;MACAC,iBA9BI;MA+BJC,YA/BI;MAgCJC,aAhCI;MAiCJC,cAjCI;MAmCJC,QAnCI;MAoCJC,gBApCI;QAAA,QAAA,CAAA,KAAA,CAAA,CAAA;UAAA,CAAA;MAuCJC,QAvCI;MAwCJC,YAxCI;MAyCJC,WAzCI;AA0CJC,MAAAA,WAAAA;KACEC,CAAAA,CAAAA;AAEJpC,IAAAA,OAAO,CAAA,mBAAA,EAAA;AACL,MAAA,EAAA;AACN,MAAA,WAAA;AACA,MAAA,cAAA;AACA,MAAA,WAAA;MACM0B,CALK;;AAML,MAAA,MAAA;AACN,QAAA,KAAA;AACA,QAAA,SAAA;AACA,QAAA,kBAAA;QATW,eAAA;;AAWL,QAAA,YAAA;AACN,QAAA,WAAA;AACA,QAAA,WAAA;AACA,QAAA,eAAA;QAdW,MAAA;;AAgBL,QAAA,SAAA;AACN,QAAA,QAAA;AACA,QAAA,gBAAA;AACA,QAAA,MAAA;AACA,QAAA,QAAA;AACMG,QAAAA,iBAAAA;AArBK,QAAP,UAAA;QAwBO;QAAsB,cAAA;QAAA,cAAA;QAAA,KAAA;AAI3BV,OAAAA,GAAAA,KAAAA,CAAAA;AAJ2B,MAA7B,MAAA,KAAA,GAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AAOA,MAAA,MAAa,cAAA,GAAA;QACL,KAAA;QACJkB,KADI,EAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA;QAEJC,OAFI,EAAA,KAAA,CAAA,WAAA,CAAA;QAGJC,IAHI,EAAA,KAAA;QAIJC,SAJI;QAKJC,kBALI;QAMJC,SANI,EAAA,KAAA,CAAA,SAAA,CAAA,GAAA,cAAA;QAOJC,YAPI;QAQJC,WARI,EAAA,KAAA,CAAA,WAAA,CAAA;QASJC,MATI,EAAA,KAAA,CAAA,eAAA,CAAA;QAUJC,YAVI;QAWJC,MAXI;QAYJC,SAZI;QAaJC,iBAbI;QAcJC,iBAdI,EAAA,CAAA;QAeJC,eAfI,EAAA,cAAA;QAgBJC,cAhBI;QAiBJC,KAjBI;QAkBJC,YAlBI;QAmBJC,cAnBI;QAoBJC,QApBI;QAqBJC;AACAC,MAAAA,MAAAA,gBAAAA,GAAAA,KAAAA,CAAAA,cAAAA,CAAAA,CAAAA;AAtBI,MAAA,MAAN,iBAAA,GAAA,KAAA,CAAA,gBAAA,CAAA,CAAA;;AAyBA,QAAA,KAAW;;AAEX,QAAA,iCAAuB,CAAA;QACrBrB,IADqB,EAAA,KAAA;AAErBsB,QAAAA,kBAAO;AACPC,QAAAA,YAASC;AACTpD,QAAAA,SAJqB;QAKrBgC,SALqB,EAAA,gBAAA;QAMrBF,WANqB,EAAA,gBAAA;AAOrBnB,QAAAA,YAAgB;QAChBsB,MARqB,EAAA,iBAAA;AASrBnB,QAAAA,MAAAA;AACAuC,QAAAA,iBAAczD;QACdS,iBAXqB,EAAA,CAAA;QAYrBqC,eAZqB,EAAA,cAAA;QAarBH,cAbqB;QAcrBK,KAdqB,EAAA,gBAAA;AAerBU,QAAAA,YAAAA;AACAC,QAAAA,QAAAA,EAAAA,gBAhBqB;QAiBrBR;YAjBqB,iBAAA,GAAA,KAAA,CAAA,eAAA,CAAA,CAAA;YAAA,8BAAA,GAAA,iBAAA,GAAA,cAAA,CAAA;YAAA,eAAA,GAAA;AAqBrBtB,QAAAA,KAAAA;QArBF,KAAA,EAAA,EAAA,CAAA,CAAA,CAAA,OAAA,CAAA;AAwBA,QAAA,OAAsB,EAAA,KAAA,CAAA;;AACtB,QAAA,kBAAuB;;AAEvB,QAAA;QACEG,SADqB,EAAA,8BAAA;AAErBsB,QAAAA,WAAO,gCAFc;AAGrBC,QAAAA,YAASC;AACTpD,QAAAA,MAAMwD,EAJe,iBAAA;QAKrB1B,MALqB;QAMrBxB,iBANqB;QAOrBiC,iBAPqB,EAAA,CAAA;AAQrB5B,QAAAA,eARqB,EAAA,cAAA;AASrBG,QAAAA,KAAAA,EAAAA,8BATqB;QAUrBmB,KAVqB,EAAA,CAAA,EAAA,EAAA,KAAA,CAAA,EAAA,CAAA,SAAA,CAAA,CAAA,uBAAA,EAAA,cAAA,CAAA,EAAA,CAAA;AAWrBoB,QAAAA,cAXqB;QAYrBX,YAZqB;QAarBE,QAbqB,EAAA,gBAAA;AAcrBU,OAAAA,CAAAA;AACAC,MAAAA,MAAAA,cAfqB,GAAA,KAAA,CAAA,aAAA,CAAA,CAAA;YAAA,aAAA,GAAA;AAiBrBN,QAAAA,EAAAA;QACA5B,QAlBqB,EAAA,KAAA,CAAA,QAAA,CAAA;AAmBrBI,QAAAA,aAAUC,EAAAA,cAAAA;QAnBZ,eAAA;AAsBA,QAAA,eAAuB,EAAA,KAAA,CAAA;AACvB,QAAA,kBAAoC;AAEpC,QAAA,sBAAwB,CAAA,eAAA,CAAA;QACtBE,cADsB,EAAA,KAAA,CAAA,cAAA,CAAA;AAEtBsB,QAAAA,QAAS;AACTC,QAAAA,QAASC;AACTpD,QAAAA,MAAMwD;QACN1B,gBALsB;QAMtBvB,YANsB;QAOtBgC,aAPsB;AAQtB5B,QAAAA,iBARsB;AAStBG,OAAAA,CAAAA;YATsB,cAAA,GAAA;AAWtBuC,QAAAA,SAXsB;QAYtBX,eAZsB;QAatBE,UAbsB;AActBU,QAAAA,QAAAA;AACAC,QAAAA,MAAAA;AACAN,QAAAA,eAhBsB,EAAA,KAAA,CAAA,eAAA,CAAA;QAiBtBQ,EAAK;QAGLV;YApBsB,gBAAA,GAAA;AAsBtBtB,QAAAA,EAAAA;QAtBF,WAAA;;AAwBA,QAAA,aAAoB,EAAA;;AAEpB,MAAA,MAAMiC,oBAAgB,GAAA;QACpBC,EADoB;AAEpB1D,QAAAA,MAAAA;AACAT,QAAAA,SAAAA;QACAuC,eAJoB;AAKpB7B,QAAAA,cAAAA;QACA4B;AACA3B,MAAAA,MAAAA,UAAAA,GAAiBiD;AACjBhD,QAAAA,GAAAA,EAAAA,CAAAA,MAAAA,KAAgBgD,WAAMhD,CAAAA,WAAAA,EAAAA,UARF,CAAA,MAAA,EAAA,aAAA,CAAA,EAAA;UAAA,GAAA,EAAA,KAAA,CAAA,GAAA;UAAA,IAAA,EAAA,CAAA,MAAA,KAAA;YAAA,IAAA,KAAA,CAAA;YAAA,OAAA,KAAA,CAAA,IAAA,GAAAwD,WAAA,CAAAC,YAAA,EAAAC,UAAA,CAAA,MAAA,EAAA,cAAA,EAAA;cAAA,OAAA,EAAA,cAAA,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA;aAAA,CAAA,EAAA,OAAA,CAAA,KAAA,GAAA,KAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,GAAA,KAAA,GAAA;AAepBC,cAAAA,OAAAA,EAAAA,MAAAA,CAAAA,KAAAA,CAAAA;aAfF,CAAA,GAAAH,WAAA,CAAAC,YAAA,EAAAC,UAAA,CAAA,MAAA,EAAA,cAAA,EAAA;AAkBA,qCAAuB,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA;aAAA,CAAA,EAAA,IAAA,CAAA,CAAA;WAAA;SAAA,CAAA;QAIrBhB,MAJqB,EAAA,CAAA,MAAA,KAAAc,WAAA,CAAAI,cAAA,EAAAF,UAAA,CAAA,MAAA,EAAA,gBAAA,CAAA,EAAA;UAAA,MAAA,EAAA,KAAA,CAAA,MAAA;AAMrB5D,UAAAA,IAAAA,EAAAA,CAAAA,MAAe,KAAEkD;AACjBO,YAAAA,IAAAA,MAAAA,CAAAA;YAPF,OAAA,KAAA,CAAA,aAAA,CAAA,GAAAC,WAAA,CAAAK,kBAAA,EAAAH,UAAA,CAAA,MAAA,EAAA,oBAAA,EAAA;AAUA,qCAAyB,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA;aAAA,CAAA,EAAA,OAAA,CAAA,MAAA,GAAA,KAAA,CAAA,aAAA,CAAA,CAAA,MAAA,CAAA,CAAA,GAAA,MAAA,GAAA;cAAA,OAAA,EAAA,MAAA,CAAA,MAAA,CAAA;aAAA,CAAA,GAAAF,WAAA,CAAAK,kBAAA,EAAAH,UAAA,CAAA,MAAA,EAAA,oBAAA,EAAA;AAIvBtE,cAAAA,OAAa,EAAE0E,cAAAA,CAAAA,MAAAA,CAAAA,MAAAA,CAAAA,GAAAA,CAAAA;aAJjB,CAAA,EAAA,IAAA,CAAA,CAAA;AAOA,WAAA;SAA6B,CAAA;QAG3B7B;YAH2B,OAAA,GAAA,CAAA,KAAA,CAAA,KAAA,EAAA,EAAA,CAAA,CAAA,EAAA,EAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,EAAA;QAK3BD,CAL2B,EAAA,CAAA,EAAA,CAAA,SAAA,CAAA,GAAA,KAAA,CAAA,SAAA,CAAA;AAM3Bd,OAAAA,CAAAA,CAAAA;MAN2B,MAA7B,WAAA,GAAA;AASA,QAAA,oBAAmB,CAAA;AACjB6C,QAAAA,KAAM7E,EAAAA,KACKA,CAAAA,YAAAA,CAAAA;;AAGL8E,MAAAA,OAAAA,WAAM,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EAAA,OAAA;;AAAA,OAAA,EAAA,CAAAR,qBAGItE,EAAAA,cAAAA,EAAAA,OAAAA,CAAAA,UAFR,IAAA,UAAA,GAAA;AAAA,QAAA,OAAA,EAAA,MAIW4E;AAJX,OAAA,CAAA,EAAAN,WAAA,CAAA,SAMKS,EAAK,cAAL,EANL,OAAA,CAAA,UAAA,CAAA,GAAA,UAAA,GAAA;AAAA,QAAA,OAAA,EAAA,MAAA,CAAA,UAAA,CAAA;qBAUQ/E,CAAAA,WAAAA,EAAAA,eAAAA,EAAAA,OAVR,CAAA,UAAA,CAAA,GAAA,UAAA,GAAA;AAAA,QAAA,OAAA,EAAA,MAYW4E;eAbP,CAAA,MAAA,IAAAN,WAAA,CAAA,MAAA,EAAA,WAAA,EAAA;AAAA,QAAA,OAAA,EAAA,KAAA,CAAA,MAAA;QAJP,EADY,KAAA,CAAA,SAAA,CAAA,IAAAA,WAAA,CAAA5E,QAAA,EAAA;AAwBjBsF,QAAAA,OAAShF,EAAAA,EAAAA,CAAAA,CAAD,CACMA,OAAAA,CAAAA;eAEF,EAAA,KAAO,CAAA,UAHX,CAAA;AAIF8E,OAAAA,EAAAA;AAAM,QAAA,OAAA,EAAA,KAAA,CAAA,KAAA;;AAAA,QAAA,OAAA,EAAA,EACJC,YAAM,CAAA;AAAN,OAAA,EAAA;AAAA,QAAA,OAAA,EAAA,KAAA,CAAA,OAAA;AAAA,OAAA,CAAA,CAAA,CAAA,CAAA;;AAAA,GAAA;;;;;"}