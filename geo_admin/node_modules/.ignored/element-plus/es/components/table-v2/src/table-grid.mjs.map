{"version": 3, "file": "table-grid.mjs", "sources": ["../../../../../../packages/components/table-v2/src/table-grid.tsx"], "sourcesContent": ["import { computed, defineComponent, inject, ref, unref } from 'vue'\nimport {\n  DynamicSizeGrid,\n  FixedSizeGrid,\n} from '@element-plus/components/virtual-list'\nimport { isNumber, isObject } from '@element-plus/utils'\nimport { Header } from './components'\nimport { TableV2InjectionKey } from './tokens'\nimport { tableV2GridProps } from './grid'\nimport { sum } from './utils'\n\nimport type { UnwrapRef } from 'vue'\nimport type {\n  DynamicSizeGridInstance,\n  GridDefaultSlotParams,\n  GridItemKeyGetter,\n  GridItemRenderedEvtParams,\n  GridScrollOptions,\n  ResetAfterIndex,\n  Alignment as ScrollStrategy,\n} from '@element-plus/components/virtual-list'\nimport type { TableV2HeaderInstance } from './components'\nimport type { TableV2GridProps } from './grid'\n\nconst COMPONENT_NAME = 'ElTableV2Grid'\n\nconst useTableGrid = (props: TableV2GridProps) => {\n  const headerRef = ref<TableV2HeaderInstance>()\n  const bodyRef = ref<DynamicSizeGridInstance>()\n\n  const totalHeight = computed(() => {\n    const { data, rowHeight, estimatedRowHeight } = props\n\n    if (estimatedRowHeight) {\n      return\n    }\n\n    return data.length * (rowHeight as number)\n  })\n\n  const fixedRowHeight = computed(() => {\n    const { fixedData, rowHeight } = props\n\n    return (fixedData?.length || 0) * (rowHeight as number)\n  })\n\n  const headerHeight = computed(() => sum(props.headerHeight))\n\n  const gridHeight = computed(() => {\n    const { height } = props\n    return Math.max(0, height - unref(headerHeight) - unref(fixedRowHeight))\n  })\n\n  const hasHeader = computed(() => {\n    return unref(headerHeight) + unref(fixedRowHeight) > 0\n  })\n\n  const itemKey: GridItemKeyGetter = ({ data, rowIndex }) =>\n    data[rowIndex][props.rowKey]\n\n  function onItemRendered({\n    rowCacheStart,\n    rowCacheEnd,\n    rowVisibleStart,\n    rowVisibleEnd,\n  }: GridItemRenderedEvtParams) {\n    props.onRowsRendered?.({\n      rowCacheStart,\n      rowCacheEnd,\n      rowVisibleStart,\n      rowVisibleEnd,\n    })\n  }\n\n  function resetAfterRowIndex(index: number, forceUpdate: boolean) {\n    bodyRef.value?.resetAfterRowIndex(index, forceUpdate)\n  }\n\n  function scrollTo(x: number, y: number): void\n  function scrollTo(options: GridScrollOptions): void\n  function scrollTo(leftOrOptions: number | GridScrollOptions, top?: number) {\n    const header$ = unref(headerRef)\n    const body$ = unref(bodyRef)\n\n    if (!header$ || !body$) return\n\n    if (isObject(leftOrOptions)) {\n      header$.scrollToLeft(leftOrOptions.scrollLeft)\n      body$.scrollTo(leftOrOptions)\n    } else {\n      header$.scrollToLeft(leftOrOptions)\n      body$.scrollTo({\n        scrollLeft: leftOrOptions,\n        scrollTop: top,\n      })\n    }\n  }\n\n  function scrollToTop(scrollTop: number) {\n    unref(bodyRef)?.scrollTo({\n      scrollTop,\n    })\n  }\n\n  function scrollToRow(row: number, strategy: ScrollStrategy) {\n    unref(bodyRef)?.scrollToItem(row, 1, strategy)\n  }\n\n  function forceUpdate() {\n    unref(bodyRef)?.$forceUpdate()\n    unref(headerRef)?.$forceUpdate()\n  }\n\n  return {\n    bodyRef,\n    forceUpdate,\n    fixedRowHeight,\n    gridHeight,\n    hasHeader,\n    headerHeight,\n    headerRef,\n    totalHeight,\n\n    itemKey,\n    onItemRendered,\n    resetAfterRowIndex,\n    scrollTo,\n    scrollToTop,\n    scrollToRow,\n  }\n}\n\nconst TableGrid = defineComponent({\n  name: COMPONENT_NAME,\n  props: tableV2GridProps,\n  setup(props, { slots, expose }) {\n    const { ns } = inject(TableV2InjectionKey)!\n\n    const {\n      bodyRef,\n      fixedRowHeight,\n      gridHeight,\n      hasHeader,\n      headerRef,\n      headerHeight,\n      totalHeight,\n\n      forceUpdate,\n      itemKey,\n      onItemRendered,\n      resetAfterRowIndex,\n      scrollTo,\n      scrollToTop,\n      scrollToRow,\n    } = useTableGrid(props)\n\n    expose({\n      forceUpdate,\n      /**\n       * @description fetch total height\n       */\n      totalHeight,\n      /**\n       * @description scroll to a position\n       */\n      scrollTo,\n      /**\n       * @description scroll vertically to position y\n       */\n      scrollToTop,\n      /**\n       * @description scroll to a given row\n       * @params row {Number} which row to scroll to\n       * @params strategy {ScrollStrategy} use what strategy to scroll to\n       */\n      scrollToRow,\n      /**\n       * @description reset rendered state after row index\n       */\n      resetAfterRowIndex,\n    })\n\n    const getColumnWidth = () => props.bodyWidth\n\n    return () => {\n      const {\n        cache,\n        columns,\n        data,\n        fixedData,\n        useIsScrolling,\n        scrollbarAlwaysOn,\n        scrollbarEndGap,\n        scrollbarStartGap,\n        style,\n        rowHeight,\n        bodyWidth,\n        estimatedRowHeight,\n        headerWidth,\n        height,\n        width,\n\n        getRowHeight,\n        onScroll,\n      } = props\n\n      const isDynamicRowEnabled = isNumber(estimatedRowHeight)\n      const Grid = isDynamicRowEnabled ? DynamicSizeGrid : FixedSizeGrid\n      const _headerHeight = unref(headerHeight)\n\n      return (\n        <div role=\"table\" class={[ns.e('table'), props.class]} style={style}>\n          <Grid\n            ref={bodyRef}\n            // special attrs\n            data={data}\n            useIsScrolling={useIsScrolling}\n            itemKey={itemKey}\n            // column attrs\n            columnCache={0}\n            columnWidth={isDynamicRowEnabled ? getColumnWidth : bodyWidth}\n            totalColumn={1}\n            // row attrs\n            totalRow={data.length}\n            rowCache={cache}\n            rowHeight={isDynamicRowEnabled ? getRowHeight : rowHeight}\n            // DOM attrs\n            width={width}\n            height={unref(gridHeight)}\n            class={ns.e('body')}\n            role=\"rowgroup\"\n            scrollbarStartGap={scrollbarStartGap}\n            scrollbarEndGap={scrollbarEndGap}\n            scrollbarAlwaysOn={scrollbarAlwaysOn}\n            // handlers\n            onScroll={onScroll}\n            onItemRendered={onItemRendered}\n            perfMode={false}\n          >\n            {{\n              default: (params: GridDefaultSlotParams) => {\n                const rowData = data[params.rowIndex]\n                return slots.row?.({\n                  ...params,\n                  columns,\n                  rowData,\n                })\n              },\n            }}\n          </Grid>\n          {unref(hasHeader) && (\n            <Header\n              ref={headerRef}\n              class={ns.e('header-wrapper')}\n              columns={columns}\n              headerData={data}\n              headerHeight={props.headerHeight}\n              fixedHeaderData={fixedData}\n              rowWidth={headerWidth}\n              rowHeight={rowHeight}\n              width={width}\n              height={Math.min(_headerHeight + unref(fixedRowHeight), height)}\n            >\n              {{\n                dynamic: slots.header,\n                fixed: slots.row,\n              }}\n            </Header>\n          )}\n        </div>\n      )\n    }\n  },\n})\n\nexport default TableGrid\n\nexport type TableGridRowSlotParams = {\n  columns: TableV2GridProps['columns']\n  rowData: any\n} & GridDefaultSlotParams\n\nexport type TableGridInstance = InstanceType<typeof TableGrid> &\n  UnwrapRef<{\n    forceUpdate: () => void\n    /**\n     * @description fetch total height\n     */\n    totalHeight: number\n\n    /**\n     * @description scrollTo a position\n     * @param { number | ScrollToOptions } arg1\n     * @param { number } arg2\n     */\n    scrollTo(leftOrOptions: number | GridScrollOptions, top?: number): void\n\n    /**\n     * @description scroll vertically to position y\n     */\n    scrollToTop(scrollTop: number): void\n    /**\n     * @description scroll to a given row\n     * @params row {Number} which row to scroll to\n     * @params @optional strategy {ScrollStrategy} use what strategy to scroll to\n     */\n    scrollToRow(row: number, strategy: ScrollStrategy): void\n    /**\n     * @description reset rendered state after row index\n     * @param { number } rowIndex\n     * @param { boolean } forceUpdate\n     */\n    resetAfterRowIndex: ResetAfterIndex\n  }>\n"], "names": ["COMPONENT_NAME", "useTableGrid", "props", "headerRef", "ref", "bodyRef", "data", "rowHeight", "estimatedRowHeight", "headerHeight", "gridHeight", "height", "Math", "max", "unref", "<PERSON><PERSON><PERSON><PERSON>", "itemKey", "rowIndex", "rowCacheStart", "rowCacheEnd", "rowVisibleEnd", "forceUpdate", "header$", "scrollToLeft", "leftOrOptions", "scrollLeft", "scrollTop", "scrollToTop", "row", "scrollToItem", "fixedRowHeight", "totalHeight", "scrollToRow", "TableGrid", "name", "expose", "ns", "inject", "TableV2InjectionKey", "onItemRendered", "resetAfterRowIndex", "scrollTo", "cache", "columns", "fixedData", "useIsScrolling", "scrollbarAlwaysOn", "scrollbarEndGap", "scrollbarStartGap", "style", "bodyWidth", "headerWidth", "getRowHeight", "onScroll", "Grid", "isDynamicRowEnabled", "_headerHeight", "_createVNode", "Header"], "mappings": ";;;;;;;;;;;;;AAwBA,MAAMA,cAAc,GAAG,eAAvB,CAAA;;AAEA,EAAMC,MAAAA,SAAAA,GAAgBC,GAAAA,EAAAA,CAAD;EACnB,MAAMC,OAAAA,GAAYC,GAAAA,EAAG,CAArB;EACA,MAAMC,WAAUD,GAAhB,QAAA,CAAA,MAAA;AAEA,IAAA,MAAiB;MACT,IAAA;MAAEE,SAAF;MAAQC,kBAAR;AAAmBC,KAAAA,GAAAA,KAAAA,CAAAA;AAAnB,IAAA,IAA0CN,kBAAhD,EAAA;;AAEA,KAAA;AACE,IAAA,OAAA,IAAA,CAAA,MAAA,GAAA,SAAA,CAAA;AACD,GAAA,CAAA,CAAA;;AAED,IAAA,MAAA;AACD,MARD,SAAA;AAUA,MAAA,SAAoB;KACZ,GAAA,KAAA,CAAA;WAAA,CAAA,CAAA,SAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,SAAA,CAAA,MAAA,KAAA,CAAA,IAAA,SAAA,CAAA;AAAaK,GAAAA,CAAAA,CAAAA;AAAb,EAAA,MAA2BL,YAAjC,GAAA,QAAA,CAAA,MAAA,GAAA,CAAA,KAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAEA,EAAA,MAAA,UAAiB,GAAA,QAAT,CAAqB;AAC9B,IAJD,MAAA;MAMMO,MAAAA;AAEN,KAAA,GAAMC,KAAU,CAAA;IACd,OAAM,IAAA,CAAA,GAAA,CAAA,CAAA,EAAA,MAAA,GAAA,KAAA,CAAA,YAAA,CAAA,GAAA,KAAA,CAAA,cAAA,CAAA,CAAA,CAAA;AAAEC,GAAAA,CAAAA,CAAAA;AAAF,EAAA,MAAaT,SAAnB,GAAA,QAAA,CAAA,MAAA;AACA,IAAA,OAAOU,KAAKC,CAAAA,YAAa,CAAA,GAAQ,KAACJ,eAAf,CAA+BK,KAAK;AACxD,GAH0B,CAA3B,CAAA;AAKA,EAAA,MAAMC,OAAS,GAAA,CAAA;IACb,IAAOD;AACR,IAFD,QAAA;;EAIA,SAAME,cAA8B,CAAA;IAAEV,aAAF;AAAQW,IAAAA,WAAAA;IAAT,eAC5BA;;AAEP,GAAA,EAAA;IACEC,IADsB,EAAA,CAAA;IAEtBC,CAFsB,EAAA,GAAA,KAAA,CAAA,cAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,EAAA;MAAA,aAAA;AAItBC,MAAAA,WAAAA;AAJsB,MAKM,eAAA;MACvB;MACHF,CADqB;;WAAA,kBAAA,CAAA,KAAA,EAAA,YAAA,EAAA;AAIrBE,IAAAA,IAAAA,EAAAA,CAAAA;IAJqB,CAAvB,EAAA,GAAA,OAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,KAAA,EAAA,YAAA,CAAA,CAAA;AAMD,GAAA;;AAED,IAAA,MAAA,OAAA,GAAA,KAAA,CAAA,SAAA,CAAA,CAA2CC;AACzChB,IAAAA,MAAAA,KAAA,GAAA;AACD,IAAA,IAAA,CAAA,OAAA,IAAA,CAAA,KAAA;;AAID,IAAA,IAAA,QAAA,CAAA,aAAA,CAAA,EAAA;AACE,MAAA,OAAa,CAAA,YAASF,cAAtB,CAAA,UAAA,CAAA,CAAA;AACA,MAAA,KAAW,CAAA,QAAQ,CAAA,aAAnB,CAAA,CAAA;AAEA,KAAA,MAAKmB;;AAEL,MAAA,KAAY,CAAA,QAAA,CAAA;AACVA,QAAAA,UAAQC,EAAAA,aAAaC;QAChB,SAAL,EAAA,GAAA;AACD,OAAM,CAAA,CAAA;;;AAGHC,EAAAA,SAAAA,WADa,CAAA,SAAA,EAAA;AAEbC,IAAAA,IAAAA,EAAAA,CAAAA;OAFF,GAAA,KAAA,CAAA,OAAA,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,QAAA,CAAA;AAID,MAAA,SAAA;AACF,KAAA,CAAA,CAAA;;EAED,SAASC,WAAT,CAAqBD,GAAAA,EAAAA,QAAmB,EAAA;AACtCZ,IAAAA,IAAAA,EAAMT,CAAAA;AACJqB,IAAAA,CAAAA,EAAAA,GAAAA,KAAAA,CAAAA,OAAAA,CAAAA,KAAAA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,EAAAA,CAAAA,YAAAA,CAAAA,GAAAA,EAAAA,CAAAA,EAAAA,QAAAA,CAAAA,CAAAA;;AAEH,EAAA,SAAA,WAAA,GAAA;;AAED,IAAA,CAAA,EAAA,GAAA,KAAA,CAAA,OAAqBE,CAArB,YAAA,KAA4D,CAAA,GAAA,EAAA,CAAA,YAAA,EAAA,CAAA;IAC1Dd,CAAK,EAAA,GAACT,KAAD,CAAA,SAAWwB,CAAAA,KAAAA,IAAhB,GAAkC,KAAlC,CAAA,GAAA,EAAA,CAAA,YAAA,EAAA,CAAA;AACD,GAAA;;AAED,IAAA,OAASR;AACPP,IAAAA,WAAMT;AACNS,IAAAA,cAAMX;AACP,IAAA,UAAA;;IAEM,YAAA;IACLE,SADK;IAELgB,WAFK;IAGLS,OAHK;IAILpB,cAJK;IAKLK,kBALK;IAMLN,QANK;IAOLN,WAPK;IAQL4B,WARK;IAULf;;AAVK,MAAA,SAAA,GAAA,eAAA,CAAA;MAAA,EAAA,cAAA;OAAA,EAAA,gBAAA;AAeLgB,EAAAA,KAAAA,CAAAA,KAAAA,EAAAA;IAfF,KAAA;AAiBD,IAxGD,MAAA;;AA0GA,IAAMC,MAAAA;AACJC,MAAI,EAAElC;AACNE,KAAAA,SAFgC,CAAA,mBAAA,CAAA,CAAA;;MAG3B,OAAA;MAAQ,cAAA;AAASiC,MAAAA,UAAAA;AAAT,MAAmB,SAAA;MACxB,SAAA;AAAEC,MAAAA,YAAAA;MAAOC,WAAOC;MAEhB,WAAA;MACJjC,OADI;MAEJyB,cAFI;MAGJpB,kBAHI;MAIJK,QAJI;MAKJZ,WALI;MAMJM,WANI;QAAA,YAAA,CAAA,KAAA,CAAA,CAAA;UAAA,CAAA;MAUJO,WAVI;MAWJuB,WAXI;MAYJC,QAZI;MAaJC,WAbI;MAcJd,WAdI;AAeJK,MAAAA,kBAAAA;KACE/B,CAAAA,CAAAA;AAEJkC,IAAAA,MAAM,cAAC,GAAA,MAAA,KAAA,CAAA,SAAA,CAAA;WAAA,MAAA;;AAEL,QAAA,KAAA;AACN,QAAA,OAAA;AACA,QAAA,IAAA;QAJW,SAAA;;AAML,QAAA,iBAAA;AACN,QAAA,eAAA;AACA,QAAA,iBAAA;QARW,KAAA;;AAUL,QAAA,SAAA;AACN,QAAA,kBAAA;AACA,QAAA,WAAA;QAZW,MAAA;;AAcL,QAAA,YAAA;AACN,QAAA,QAAA;AACA,OAAA,GAAA,KAAA,CAAA;AACA,MAAA,MAAA,mBAAA,GAAA,QAAA,CAAA,kBAAA,CAAA,CAAA;AACA,MAAA,MAAA,IAAA,GAAA,mBAAA,GAAA,eAAA,GAAA,aAAA,CAAA;MACMH,MAnBK,aAAA,GAAA,KAAA,CAAA,YAAA,CAAA,CAAA;;AAoBL,QAAA,MAAA,EAAA,OAAA;AACN,QAAA,OAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,OAAA,CAAA,EAAA,KAAA,CAAA,KAAA,CAAA;AACA,QAAA,OAAA,EAAA,KAAA;AACMQ,OAAAA,EAAAA,CAAAA,WAAAA,CAAAA,IAAAA,EAAAA;AAvBK,QAAP,KAAA,EAAA,OAAA;;AA0BA,QAAA,gBAAoB,EAAA,cAApB;;AAEA,QAAA,aAAa,EAAA,CAAA;QACL,aAAA,EAAA,mBAAA,GAAA,cAAA,GAAA,SAAA;QACJE,aADI,EAAA,CAAA;QAEJC,UAFI,EAAA,IAAA,CAAA,MAAA;QAGJrC,UAHI,EAAA,KAAA;QAIJsC,WAJI,EAAA,mBAAA,GAAA,YAAA,GAAA,SAAA;QAKJC,OALI,EAAA,KAAA;QAMJC,QANI,EAAA,KAAA,CAAA,UAAA,CAAA;QAOJC,OAPI,EAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA;QAQJC,MARI,EAAA,UAAA;QASJC,mBATI,EAAA,iBAAA;QAUJ1C,iBAVI,EAAA,eAAA;QAWJ2C,mBAXI,EAAA,iBAAA;QAYJ1C,UAZI,EAAA,QAAA;QAaJ2C,gBAbI,EAAA,cAAA;QAcJxC,UAdI,EAAA,KAAA;SAAA;QAiBJyC,OAjBI,EAAA,CAAA,MAAA,KAAA;AAkBJC,UAAAA,IAAAA,EAAAA,CAAAA;AAlBI,UAmBFnD,MAnBJ,OAAA,GAAA,IAAA,CAAA,MAAA,CAAA,QAAA,CAAA,CAAA;AAqBA,UAAA,OAAyB,CAAA,EAAA,GAAA,KAAA,CAAA,GAAA,KAAW,IAAA,oBAAA,KAApC,EAAA;AACA,YAAMoD,SAAOC;;AACb,YAAMC,OAAa;;AAEnB,SAAA;AAAA,OAAA,CAAA,EAAA,KAAA,CACY,SADZ,CAAA,IAAAC,WAAA,CAAAC,aAAA,EAAA;QAAA,KAC2B,EAAA,SAAC;QAD5B,OACgET,EAAAA,EAAAA,CAAAA,CAAAA,CAAAA,gBAAAA,CAAAA;AADhE,QAAA,SAAA,EAAA,OAAA;AAAA,QAAA,YAAA,EAAA,IAAA;AAAA,QAAA,cAAA,EAAA,KAAA,CAAA,YAAA;AAAA,QAAA,iBAAA,EAAA,SAAA;AAAA,QAAA,UAAA,EAAA,WAAA;AAAA,QAAA,WAAA,EAAA,SAAA;AAAA,QAAA,OAAA,EAAA,KAAA;AAAA,QAAA,QAAA,EAAA,IAAA,CAWmB,GAXnB,CAAA,aAAA,GAAA,KAAA,CAAA,cAAA,CAAA,EAAA,MAAA,CAAA;SAagB3C;AAbhB,QAAA,OAAA,EAAA,KAAA,CAAA,MAAA;AAAA,QAAA,KAAA,EAAA,KAAA,CAeiBiD,GAAmB;AAfpC,OAAA,CAAA,CAAA,CAAA,CAAA;;AAAA,GAAA;AAAA,CAAA;;;;"}