{"version": 3, "file": "row.mjs", "sources": ["../../../../../../../packages/components/table-v2/src/renderers/row.tsx"], "sourcesContent": ["import { Row } from '../components'\nimport { tryCall } from '../utils'\n\nimport type { FunctionalComponent, UnwrapNestedRefs } from 'vue'\nimport type { UseNamespaceReturn } from '@element-plus/hooks'\nimport type { UseTableReturn } from '../use-table'\nimport type { TableV2Props } from '../table'\nimport type { TableGridRowSlotParams } from '../table-grid'\n\ntype RowRendererProps = TableGridRowSlotParams &\n  Pick<\n    TableV2Props,\n    | 'expandColumnKey'\n    | 'estimatedRowHeight'\n    | 'rowProps'\n    | 'rowClass'\n    | 'rowKey'\n    | 'rowEventHandlers'\n  > &\n  UnwrapNestedRefs<\n    Pick<\n      UseTableReturn,\n      | 'depthMap'\n      | 'expandedRowKeys'\n      | 'hasFixedColumns'\n      | 'hoveringRowKey'\n      | 'onRowHovered'\n      | 'onRowExpanded'\n      | 'columnsStyles'\n    >\n  > & {\n    ns: UseNamespaceReturn\n  }\n\nconst RowRenderer: FunctionalComponent<RowRendererProps> = (\n  props,\n  { slots }\n) => {\n  const {\n    columns,\n    columnsStyles,\n    depthMap,\n    expandColumnKey,\n    expandedRowKeys,\n    estimatedRowHeight,\n    hasFixedColumns,\n    hoveringRowKey,\n    rowData,\n    rowIndex,\n    style,\n    isScrolling,\n    rowProps,\n    rowClass,\n    rowKey,\n    rowEventHandlers,\n    ns,\n    onRowHovered,\n    onRowExpanded,\n  } = props\n\n  const rowKls = tryCall(rowClass, { columns, rowData, rowIndex }, '')\n  const additionalProps = tryCall(rowProps, {\n    columns,\n    rowData,\n    rowIndex,\n  })\n  const _rowKey = rowData[rowKey]\n  const depth = depthMap[_rowKey] || 0\n  const canExpand = Boolean(expandColumnKey)\n  const isFixedRow = rowIndex < 0\n  const kls = [\n    ns.e('row'),\n    rowKls,\n    {\n      [ns.e(`row-depth-${depth}`)]: canExpand && rowIndex >= 0,\n      [ns.is('expanded')]: canExpand && expandedRowKeys.includes(_rowKey),\n      [ns.is('hovered')]: !isScrolling && _rowKey === hoveringRowKey,\n      [ns.is('fixed')]: !depth && isFixedRow,\n      [ns.is('customized')]: Boolean(slots.row),\n    },\n  ]\n\n  const onRowHover = hasFixedColumns ? onRowHovered : undefined\n\n  const _rowProps = {\n    ...additionalProps,\n    columns,\n    columnsStyles,\n    class: kls,\n    depth,\n    expandColumnKey,\n    estimatedRowHeight: isFixedRow ? undefined : estimatedRowHeight,\n    isScrolling,\n    rowIndex,\n    rowData,\n    rowKey: _rowKey,\n    rowEventHandlers,\n    style,\n  }\n\n  return (\n    <Row {..._rowProps} onRowHover={onRowHover} onRowExpand={onRowExpanded}>\n      {slots}\n    </Row>\n  )\n}\n\nexport default RowRenderer\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "slots", "columns", "columnsStyles", "depthMap", "expandColumnKey", "expandedRowKeys", "estimatedRowHeight", "hasFixedColumns", "hoveringRowKey", "rowData", "rowIndex", "style", "isScrolling", "rowProps", "rowClass", "<PERSON><PERSON><PERSON>", "rowEventHandlers", "ns", "onRowExpanded", "_row<PERSON>ey", "depth", "canExpand", "Boolean", "isFixedRow", "kls", "e", "is", "class", "_createVNode", "Row", "_mergeProps"], "mappings": ";;;;;;;;;;AAkCA,CAAA,KAAMA;AAEFC,EAAAA,MAAAA;AAAF,IACG,OAAA;IACG,aAAA;IACJC,QADI;IAEJC,eAFI;IAGJC,eAHI;IAIJC,kBAJI;IAKJC,eALI;IAMJC,cANI;IAOJC,OAPI;IAQJC,QARI;IASJC,KATI;IAUJC,WAVI;IAWJC,QAXI;IAYJC,QAZI;IAaJC,MAbI;IAcJC,gBAdI;IAeJC,EAfI;IAgBJC,YAhBI;IAiBJC,aAjBI;MAAA,KAAA,CAAA;AAmBJC,EAAAA,MAAAA,MAAAA,GAAAA,OAAAA,CAAAA,QAAAA,EAAAA;AAnBI,IAAA,OAAN;AAsBA,IAAA,OAAY;IAAuBjB,QAAF;KAAA,EAAA,CAAA,CAAA;AAAoBS,EAAAA,MAAAA,eAAAA,GAAAA,OAAAA,CAAAA,QAAAA,EAAAA;IAA/B,OAAtB;AACA,IAAA,OAAqB;IACnBT,QADwC;IAExCQ,CAFwC;AAGxCC,EAAAA,MAAAA,OAAAA,GAAAA,OAAAA,CAAAA,MAAAA,CAAAA,CAAAA;AAHwC,EAAA,MAA1C,KAAA,GAAA,QAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA;AAKA,EAAA,MAAMS,SAAO,GAAU,uBAAvB,CAAA,CAAA;AACA,EAAA,MAAMC,UAAQjB,GAAAA,QAAQ,GAAA,CAAR;AACd,EAAA,MAAMkB,GAAS,GAAA,CAAA,EAAA,CAAA,CAAA,CAAGC,KAAO,CAAA,EAAClB;AAC1B,IAAA,CAAA,EAAA,CAAMmB,CAAU,CAAA,CAAA,UAAGb,EAAQ,KAAA,CAAA,CAAA,CAAA,GAA3B,SAAA,IAAA,QAAA,IAAA,CAAA;IACMc,CAAAA,EAAAA,CAAAA,EAAAA,CAAG,UACP,CAAA,GAAA,SADU,IAGV,eAAA,CAAA,QAAA,CAAA,OAAA,CAAA;AACE,IAAA,CAACP,EAAE,CAACQ,EAAG,CAAYL,SAAAA,CAAAA,GAAAA,CAAAA,WAAWC,IAAAA,OAAaX,KAAAA,cAD7C;AAEE,IAAA,CAACO,EAAE,CAACS,EAAH,CAAM,WAAP,CAAA,KAA8B,IAAA,UAAmB;AACjD,IAAA,CAACT,EAAE,CAACS,EAAH,CAAM,YAAa,CAAA,GAAA,OAAA,CAAA,KAAgBP,CAAO,GAAA,CAAA;IAC3C,CAACF;QACGS,UAAG,GAAA,eAA6B,GAAA,YAAN,GAAA,KAAA,CAAA,CAAA;AALhC,EAAA,MAHF,SAAA,GAAA;AAYA,IAAA,GAAA,eAAmBnB;AAEnB,IAAA,OAAe;IAEbN,aAFgB;IAGhBC,KAHgB,EAAA,GAAA;AAIhByB,IAAAA,KAAK;IACLP,eALgB;IAMhBhB,kBANgB,EAAA,UAAA,GAAA,KAAA,CAAA,GAAA,kBAAA;AAOhBE,IAAAA,WAAAA;IACAM,QARgB;IAShBF,OATgB;IAUhBD,MAVgB,EAAA,OAAA;AAWhBM,IAAAA,gBAXgB;IAYhBC,KAZgB;AAahBL,GAAAA,CAAAA;EAbgB,OAAlBiB,WAAA,CAAAC,UAAA,EAAAC,UAAA,CAAA,SAAA,EAAA;AAgBA,IAAA,YAAA,EAAA,UAAA;AAAA,IAAA,aAAA,EAAA,aAAA;IAAA,EAC2DZ,OAAAA,CAAAA,KAAAA,CAAAA,GAAAA,KAAAA,GAAAA;IACtDlB,OAAAA,EAAAA,MAFL,MAAA,CAAA;AAAA,GAAA,CAAA,CAAA;AAAA;;;;"}