{"version": 3, "file": "tokens.mjs", "sources": ["../../../../../../packages/components/table-v2/src/tokens.ts"], "sourcesContent": ["import type { Injection<PERSON><PERSON>, Ref } from 'vue'\nimport type { UseNamespaceReturn } from '@element-plus/hooks'\nimport type { KeyType } from './types'\n\nexport type TableV2Context = {\n  isScrolling: Ref<boolean>\n  hoveringRowKey: Ref<null | KeyType>\n  isResetting: Ref<boolean>\n  ns: UseNamespaceReturn\n}\n\nexport const TableV2InjectionKey: InjectionKey<TableV2Context> =\n  Symbol('tableV2')\n"], "names": [], "mappings": "AAAY,MAAC,mBAAmB,GAAG,MAAM,CAAC,SAAS;;;;"}