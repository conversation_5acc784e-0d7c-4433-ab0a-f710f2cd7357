{"version": 3, "file": "transfer2.mjs", "sources": ["../../../../../../packages/components/transfer/src/transfer.vue"], "sourcesContent": ["<template>\n  <div :class=\"ns.b()\">\n    <transfer-panel\n      ref=\"leftPanel\"\n      :data=\"sourceData\"\n      :option-render=\"optionRender\"\n      :placeholder=\"panelFilterPlaceholder\"\n      :title=\"leftPanelTitle\"\n      :filterable=\"filterable\"\n      :format=\"format\"\n      :filter-method=\"filterMethod\"\n      :default-checked=\"leftDefaultChecked\"\n      :props=\"props.props\"\n      @checked-change=\"onSourceCheckedChange\"\n    >\n      <slot name=\"left-footer\" />\n    </transfer-panel>\n    <div :class=\"ns.e('buttons')\">\n      <el-button\n        type=\"primary\"\n        :class=\"[ns.e('button'), ns.is('with-texts', hasButtonTexts)]\"\n        :disabled=\"isEmpty(checkedState.rightChecked)\"\n        @click=\"addToLeft\"\n      >\n        <el-icon><arrow-left /></el-icon>\n        <span v-if=\"!isUndefined(buttonTexts[0])\">{{ buttonTexts[0] }}</span>\n      </el-button>\n      <el-button\n        type=\"primary\"\n        :class=\"[ns.e('button'), ns.is('with-texts', hasButtonTexts)]\"\n        :disabled=\"isEmpty(checkedState.leftChecked)\"\n        @click=\"addToRight\"\n      >\n        <span v-if=\"!isUndefined(buttonTexts[1])\">{{ buttonTexts[1] }}</span>\n        <el-icon><arrow-right /></el-icon>\n      </el-button>\n    </div>\n    <transfer-panel\n      ref=\"rightPanel\"\n      :data=\"targetData\"\n      :option-render=\"optionRender\"\n      :placeholder=\"panelFilterPlaceholder\"\n      :filterable=\"filterable\"\n      :format=\"format\"\n      :filter-method=\"filterMethod\"\n      :title=\"rightPanelTitle\"\n      :default-checked=\"rightDefaultChecked\"\n      :props=\"props.props\"\n      @checked-change=\"onTargetCheckedChange\"\n    >\n      <slot name=\"right-footer\" />\n    </transfer-panel>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, h, reactive, ref, useSlots, watch } from 'vue'\nimport { debugWarn, isEmpty, isUndefined } from '@element-plus/utils'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { ElButton } from '@element-plus/components/button'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { useFormItem } from '@element-plus/components/form'\nimport { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'\nimport { transferEmits, transferProps } from './transfer'\nimport {\n  useCheckedChange,\n  useComputedData,\n  useMove,\n  usePropsAlias,\n} from './composables'\nimport TransferPanel from './transfer-panel.vue'\n\nimport type {\n  TransferCheckedState,\n  TransferDataItem,\n  TransferDirection,\n} from './transfer'\nimport type { TransferPanelInstance } from './transfer-panel'\n\ndefineOptions({\n  name: 'ElTransfer',\n})\n\nconst props = defineProps(transferProps)\nconst emit = defineEmits(transferEmits)\nconst slots = useSlots()\n\nconst { t } = useLocale()\nconst ns = useNamespace('transfer')\nconst { formItem } = useFormItem()\n\nconst checkedState = reactive<TransferCheckedState>({\n  leftChecked: [],\n  rightChecked: [],\n})\n\nconst propsAlias = usePropsAlias(props)\n\nconst { sourceData, targetData } = useComputedData(props)\n\nconst { onSourceCheckedChange, onTargetCheckedChange } = useCheckedChange(\n  checkedState,\n  emit\n)\n\nconst { addToLeft, addToRight } = useMove(props, checkedState, emit)\n\nconst leftPanel = ref<TransferPanelInstance>()\nconst rightPanel = ref<TransferPanelInstance>()\n\nconst clearQuery = (which: TransferDirection) => {\n  switch (which) {\n    case 'left':\n      leftPanel.value!.query = ''\n      break\n    case 'right':\n      rightPanel.value!.query = ''\n      break\n  }\n}\n\nconst hasButtonTexts = computed(() => props.buttonTexts.length === 2)\n\nconst leftPanelTitle = computed(\n  () => props.titles[0] || t('el.transfer.titles.0')\n)\n\nconst rightPanelTitle = computed(\n  () => props.titles[1] || t('el.transfer.titles.1')\n)\n\nconst panelFilterPlaceholder = computed(\n  () => props.filterPlaceholder || t('el.transfer.filterPlaceholder')\n)\n\nwatch(\n  () => props.modelValue,\n  () => {\n    if (props.validateEvent) {\n      formItem?.validate?.('change').catch((err) => debugWarn(err))\n    }\n  }\n)\n\nconst optionRender = computed(() => (option: TransferDataItem) => {\n  if (props.renderContent) return props.renderContent(h, option)\n\n  if (slots.default) return slots.default({ option })\n\n  return h(\n    'span',\n    option[propsAlias.value.label] || option[propsAlias.value.key]\n  )\n})\n\ndefineExpose({\n  /** @description clear the filter keyword of a certain panel */\n  clearQuery,\n  /** @description left panel ref */\n  leftPanel,\n  /** @description left panel ref */\n  rightPanel,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;mCA+Ec,CAAA;AAAA,EACZ,IAAM,EAAA,YAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAIA,IAAA,MAAM,QAAQ,QAAS,EAAA,CAAA;AAEvB,IAAM,MAAA,EAAE,MAAM,SAAU,EAAA,CAAA;AACxB,IAAM,MAAA,EAAA,GAAK,aAAa,UAAU,CAAA,CAAA;AAClC,IAAM,MAAA,EAAE,aAAa,WAAY,EAAA,CAAA;AAEjC,IAAA,MAAM,eAAe,QAA+B,CAAA;AAAA,MAClD,aAAa,EAAC;AAAA,MACd,cAAc,EAAC;AAAA,KAChB,CAAA,CAAA;AAED,IAAM,MAAA,UAAA,GAAa,cAAc,KAAK,CAAA,CAAA;AAEtC,IAAA,MAAM,EAAE,UAAA,EAAY,UAAe,EAAA,GAAA,eAAA,CAAgB,KAAK,CAAA,CAAA;AAExD,IAAA,MAAM,EAAE,qBAAA,EAAuB,qBAA0B,EAAA,GAAA,gBAAA,CACvD,cACA,IACF,CAAA,CAAA;AAEA,IAAA,MAAM,EAAE,SAAW,EAAA,UAAA,EAAA,GAAe,OAAQ,CAAA,KAAA,EAAO,cAAc,IAAI,CAAA,CAAA;AAEnE,IAAA,MAAM,YAAY,GAA2B,EAAA,CAAA;AAC7C,IAAA,MAAM,aAAa,GAA2B,EAAA,CAAA;AAE9C,IAAM,MAAA,UAAA,GAAa,CAAC,KAA6B,KAAA;AAC/C,MAAQ,QAAA,KAAA;AAAA,QACD,KAAA,MAAA;AACH,UAAA,SAAA,CAAU,MAAO,KAAQ,GAAA,EAAA,CAAA;AACzB,UAAA,MAAA;AAAA,QACG,KAAA,OAAA;AACH,UAAA,UAAA,CAAW,MAAO,KAAQ,GAAA,EAAA,CAAA;AAC1B,UAAA,MAAA;AAAA,OAAA;AAAA,KAEN,CAAA;AAEA,IAAA,MAAM,iBAAiB,QAAS,CAAA,MAAM,KAAM,CAAA,WAAA,CAAY,WAAW,CAAC,CAAA,CAAA;AAEpE,IAAM,MAAA,cAAA,GAAiB,SACrB,MAAM,KAAA,CAAM,OAAO,CAAM,CAAA,IAAA,CAAA,CAAE,sBAAsB,CACnD,CAAA,CAAA;AAEA,IAAM,MAAA,eAAA,GAAkB,SACtB,MAAM,KAAA,CAAM,OAAO,CAAM,CAAA,IAAA,CAAA,CAAE,sBAAsB,CACnD,CAAA,CAAA;AAEA,IAAA,MAAM,yBAAyB,QAC7B,CAAA,MAAM,MAAM,iBAAqB,IAAA,CAAA,CAAE,+BAA+B,CACpE,CAAA,CAAA;AAEA,IACE,KAAA,CAAA,MAAM,KAAM,CAAA,UAAA,EACZ,MAAM;AACJ,MAAA,IAAI;AACF,MAAU,IAAA,KAAA,CAAA,aAAW;AAAuC,QAC9D,CAAA,EAAA,GAAA,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,QAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,QAAA,EAAA,QAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA,SAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAAA,OAEJ;AAEA,KAAA,CAAA,CAAA;AACE,IAAA,MAAI,YAAM,GAAA,QAAA,CAAA,MAAA,CAAA,MAAA,KAAA;AAAe,MAAO,IAAA,KAAA,CAAA,aAAoB;AAEpD,QAAA,OAAU,KAAA,CAAA,aAAA,CAAA,CAAA,EAAA,MAAA,CAAA,CAAA;AAAS,MAAA,IAAA,KAAO,CAAM,OAAA;AAEhC,QAAO,OACL,aACO,CAAA,EAAA,MAAA,EAAA,CAAA,CAAW;AACpB,MACD,OAAA,CAAA,CAAA,MAAA,EAAA,MAAA,CAAA,UAAA,CAAA,KAAA,CAAA,KAAA,CAAA,IAAA,MAAA,CAAA,UAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAED,KAAa,CAAA,CAAA;AAAA,IAEX,MAAA,CAAA;AAAA,MAEA,UAAA;AAAA,MAEA,SAAA;AAAA,MACD,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}