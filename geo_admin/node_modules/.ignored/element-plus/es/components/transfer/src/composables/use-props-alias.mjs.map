{"version": 3, "file": "use-props-alias.mjs", "sources": ["../../../../../../../packages/components/transfer/src/composables/use-props-alias.ts"], "sourcesContent": ["import { computed } from 'vue'\n\nimport type { TransferPropsAlias } from '../transfer'\n\nexport const usePropsAlias = (props: { props: TransferPropsAlias }) => {\n  const initProps: Required<TransferPropsAlias> = {\n    label: 'label',\n    key: 'key',\n    disabled: 'disabled',\n  }\n\n  return computed(() => ({\n    ...initProps,\n    ...props.props,\n  }))\n}\n"], "names": [], "mappings": ";;AACY,MAAC,aAAa,GAAG,CAAC,KAAK,KAAK;AACxC,EAAE,MAAM,SAAS,GAAG;AACpB,IAAI,KAAK,EAAE,OAAO;AAClB,IAAI,GAAG,EAAE,KAAK;AACd,IAAI,QAAQ,EAAE,UAAU;AACxB,GAAG,CAAC;AACJ,EAAE,OAAO,QAAQ,CAAC,OAAO;AACzB,IAAI,GAAG,SAAS;AAChB,IAAI,GAAG,KAAK,CAAC,KAAK;AAClB,GAAG,CAAC,CAAC,CAAC;AACN;;;;"}