{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/transfer/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport Transfer from './src/transfer.vue'\n\nexport const ElTransfer = withInstall(Transfer)\nexport default ElTransfer\n\nexport * from './src/transfer'\n"], "names": [], "mappings": ";;;;;AAEY,MAAC,UAAU,GAAG,WAAW,CAAC,QAAQ;;;;"}