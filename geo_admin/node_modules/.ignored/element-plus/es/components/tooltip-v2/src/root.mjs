import '../../../utils/index.mjs';
import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';

const tooltipV2RootProps = buildProps({
  delayDuration: {
    type: Number,
    default: 300
  },
  defaultOpen: Boolean,
  open: {
    type: <PERSON>olean,
    default: void 0
  },
  onOpenChange: {
    type: definePropType(Function)
  },
  "onUpdate:open": {
    type: definePropType(Function)
  }
});

export { tooltipV2RootProps };
//# sourceMappingURL=root.mjs.map
