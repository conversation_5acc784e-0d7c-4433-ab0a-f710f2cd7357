import type { CSSProperties } from 'vue';
declare const _default: import("vue").DefineComponent<{
    side: {
        readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("./common").TooltipV2Sides & {}) | (() => import("./common").TooltipV2Sides) | ((new (...args: any[]) => import("./common").TooltipV2Sides & {}) | (() => import("./common").TooltipV2Sides))[], import("./common").TooltipV2Sides, unknown>>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    width: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 10, boolean>;
    height: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 10, boolean>;
    style: import("../../../utils").EpPropFinalized<(new (...args: any[]) => CSSProperties) | (() => CSSProperties | null) | ((new (...args: any[]) => CSSProperties) | (() => CSSProperties | null))[], unknown, unknown, null, boolean>;
}, {
    props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
        side: {
            readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("./common").TooltipV2Sides & {}) | (() => import("./common").TooltipV2Sides) | ((new (...args: any[]) => import("./common").TooltipV2Sides & {}) | (() => import("./common").TooltipV2Sides))[], import("./common").TooltipV2Sides, unknown>>;
            readonly required: true;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        width: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 10, boolean>;
        height: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 10, boolean>;
        style: import("../../../utils").EpPropFinalized<(new (...args: any[]) => CSSProperties) | (() => CSSProperties | null) | ((new (...args: any[]) => CSSProperties) | (() => CSSProperties | null))[], unknown, unknown, null, boolean>;
    }>> & {
        [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
    }>>;
    ns: {
        namespace: import("vue").ComputedRef<string>;
        b: (blockSuffix?: string) => string;
        e: (element?: string | undefined) => string;
        m: (modifier?: string | undefined) => string;
        be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
        em: (element?: string | undefined, modifier?: string | undefined) => string;
        bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
        bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
        is: {
            (name: string, state: boolean | undefined): string;
            (name: string): string;
        };
        cssVar: (object: Record<string, string>) => Record<string, string>;
        cssVarName: (name: string) => string;
        cssVarBlock: (object: Record<string, string>) => Record<string, string>;
        cssVarBlockName: (name: string) => string;
    };
    arrowRef: import("vue").Ref<HTMLElement | null>;
    arrowStyle: import("vue").ComputedRef<CSSProperties>;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    side: {
        readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => import("./common").TooltipV2Sides & {}) | (() => import("./common").TooltipV2Sides) | ((new (...args: any[]) => import("./common").TooltipV2Sides & {}) | (() => import("./common").TooltipV2Sides))[], import("./common").TooltipV2Sides, unknown>>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    width: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 10, boolean>;
    height: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 10, boolean>;
    style: import("../../../utils").EpPropFinalized<(new (...args: any[]) => CSSProperties) | (() => CSSProperties | null) | ((new (...args: any[]) => CSSProperties) | (() => CSSProperties | null))[], unknown, unknown, null, boolean>;
}>>, {
    height: number;
    width: number;
    style: import("../../../utils").EpPropMergeType<(new (...args: any[]) => CSSProperties) | (() => CSSProperties | null) | ((new (...args: any[]) => CSSProperties) | (() => CSSProperties | null))[], unknown, unknown>;
}>;
export default _default;
