{"version": 3, "file": "content2.mjs", "sources": ["../../../../../../packages/components/tooltip-v2/src/content.vue"], "sourcesContent": ["<template>\n  <div ref=\"contentRef\" :style=\"contentStyle\" data-tooltip-v2-root>\n    <div v-if=\"!nowrap\" :data-side=\"side\" :class=\"contentClass\">\n      <slot :content-style=\"contentStyle\" :content-class=\"contentClass\" />\n      <el-visually-hidden :id=\"contentId\" role=\"tooltip\">\n        <template v-if=\"ariaLabel\">\n          {{ ariaLabel }}\n        </template>\n        <slot v-else />\n      </el-visually-hidden>\n      <slot name=\"arrow\" :style=\"arrowStyle\" :side=\"side\" />\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { computed, inject, onMounted, provide, ref, unref, watch } from 'vue'\nimport { offset } from '@floating-ui/dom'\nimport {\n  arrowMiddleware,\n  useFloating,\n  useNamespace,\n  useZIndex,\n} from '@element-plus/hooks'\nimport ElVisuallyHidden from '@element-plus/components/visual-hidden'\nimport { tooltipV2ContentKey, tooltipV2RootKey } from './constants'\nimport { tooltipV2ContentProps } from './content'\nimport { tooltipV2CommonProps } from './common'\n\nimport type { CSSProperties } from 'vue'\nimport type { Middleware } from '@floating-ui/dom'\n\ndefineOptions({\n  name: 'ElTooltipV2Content',\n})\n\nconst props = defineProps({ ...tooltipV2ContentProps, ...tooltipV2CommonProps })\n\nconst { triggerRef, contentId } = inject(tooltipV2RootKey)!\n\nconst placement = ref(props.placement)\nconst strategy = ref(props.strategy)\nconst arrowRef = ref<HTMLElement | null>(null)\n\nconst { referenceRef, contentRef, middlewareData, x, y, update } = useFloating({\n  placement,\n  strategy,\n  middleware: computed(() => {\n    const middleware: Middleware[] = [offset(props.offset)]\n\n    if (props.showArrow) {\n      middleware.push(\n        arrowMiddleware({\n          arrowRef,\n        })\n      )\n    }\n\n    return middleware\n  }),\n})\n\nconst zIndex = useZIndex().nextZIndex()\n\nconst ns = useNamespace('tooltip-v2')\n\nconst side = computed(() => {\n  return placement.value.split('-')[0]\n})\n\nconst contentStyle = computed<CSSProperties>(() => {\n  return {\n    position: unref(strategy),\n    top: `${unref(y) || 0}px`,\n    left: `${unref(x) || 0}px`,\n    zIndex,\n  }\n})\n\nconst arrowStyle = computed<CSSProperties>(() => {\n  if (!props.showArrow) return {}\n\n  const { arrow } = unref(middlewareData)\n\n  return {\n    [`--${ns.namespace.value}-tooltip-v2-arrow-x`]: `${arrow?.x}px` || '',\n    [`--${ns.namespace.value}-tooltip-v2-arrow-y`]: `${arrow?.y}px` || '',\n  }\n})\n\nconst contentClass = computed(() => [\n  ns.e('content'),\n  ns.is('dark', props.effect === 'dark'),\n  ns.is(unref(strategy)),\n  props.contentClass,\n])\n\nwatch(arrowRef, () => update())\n\nwatch(\n  () => props.placement,\n  (val) => (placement.value = val)\n)\n\nonMounted(() => {\n  watch(\n    () => props.reference || triggerRef.value,\n    (el) => {\n      referenceRef.value = el || undefined\n    },\n    {\n      immediate: true,\n    }\n  )\n})\n\nprovide(tooltipV2ContentKey, { arrowRef })\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;mCAgCc,CAAA;AAAA,EACZ,IAAM,EAAA,oBAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAIA,IAAA,MAAM,EAAE,UAAA,EAAY,SAAc,EAAA,GAAA,MAAA,CAAO,gBAAgB,CAAA,CAAA;AAEzD,IAAM,MAAA,SAAA,GAAY,GAAI,CAAA,KAAA,CAAM,SAAS,CAAA,CAAA;AACrC,IAAM,MAAA,QAAA,GAAW,GAAI,CAAA,KAAA,CAAM,QAAQ,CAAA,CAAA;AACnC,IAAM,MAAA,QAAA,GAAW,IAAwB,IAAI,CAAA,CAAA;AAE7C,IAAA,MAAM,EAAE,YAAc,EAAA,UAAA,EAAY,gBAAgB,CAAG,EAAA,CAAA,EAAG,WAAW,WAAY,CAAA;AAAA,MAC7E,SAAA;AAAA,MACA,QAAA;AAAA,MACA,UAAA,EAAY,SAAS,MAAM;AACzB,QAAA,MAAM,UAA2B,GAAA,CAAC,MAAO,CAAA,KAAA,CAAM,MAAM,CAAC,CAAA,CAAA;AAEtD,QAAA,IAAI,MAAM,SAAW,EAAA;AACnB,UAAA,UAAA,CAAW,KACT,eAAgB,CAAA;AAAA,YACd,QAAA;AAAA,WACD,CACH,CAAA,CAAA;AAAA,SACF;AAEA,QAAO,OAAA,UAAA,CAAA;AAAA,OACR,CAAA;AAAA,KACF,CAAA,CAAA;AAED,IAAM,MAAA,MAAA,GAAS,SAAU,EAAA,CAAE,UAAW,EAAA,CAAA;AAEtC,IAAM,MAAA,EAAA,GAAK,aAAa,YAAY,CAAA,CAAA;AAEpC,IAAM,MAAA,IAAA,GAAO,SAAS,MAAM;AAC1B,MAAA,OAAO,SAAU,CAAA,KAAA,CAAM,KAAM,CAAA,GAAG,CAAE,CAAA,CAAA,CAAA,CAAA;AAAA,KACnC,CAAA,CAAA;AAED,IAAM,MAAA,YAAA,GAAe,SAAwB,MAAM;AACjD,MAAO,OAAA;AAAA,QACL,QAAA,EAAU,MAAM,QAAQ,CAAA;AAAA,QACxB,GAAK,EAAA,CAAA,EAAG,KAAM,CAAA,CAAC,CAAK,IAAA,CAAA,CAAA,EAAA,CAAA;AAAA,QACpB,IAAM,EAAA,CAAA,EAAG,KAAM,CAAA,CAAC,CAAK,IAAA,CAAA,CAAA,EAAA,CAAA;AAAA,QACrB,MAAA;AAAA,OACF,CAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,UAAA,GAAa,SAAwB,MAAM;AAC/C,MAAA,IAAI,CAAC,KAAM,CAAA,SAAA;AAAW,QAAA,OAAO,EAAC,CAAA;AAE9B,MAAM,MAAA,EAAE,KAAU,EAAA,GAAA,KAAA,CAAM,cAAc,CAAA,CAAA;AAEtC,MAAO,OAAA;AAAA,QACL,CAAC,CAAK,EAAA,EAAA,EAAA,CAAG,UAAU,KAA6B,CAAA,mBAAA,CAAA,GAAA,CAAA,EAAG,SAAgB,IAAA,GAAA,KAAA,CAAA,GAAA,KAAA,CAAA,CAAA,CAAA,EAAA,CAAA,IAAA,EAAA;AAAA,QACnE,CAAC,CAAK,EAAA,EAAA,EAAA,CAAG,UAAU,KAA6B,CAAA,mBAAA,CAAA,GAAA,CAAA,EAAG,SAAgB,IAAA,GAAA,KAAA,CAAA,GAAA,KAAA,CAAA,CAAA,CAAA,EAAA,CAAA,IAAA,EAAA;AAAA,OACrE,CAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,YAAA,GAAe,SAAS,MAAM;AAAA,MAClC,EAAA,CAAG,EAAE,SAAS,CAAA;AAAA,MACd,EAAG,CAAA,EAAA,CAAG,MAAQ,EAAA,KAAA,CAAM,WAAW,MAAM,CAAA;AAAA,MACrC,EAAG,CAAA,EAAA,CAAG,KAAM,CAAA,QAAQ,CAAC,CAAA;AAAA,MACrB,KAAM,CAAA,YAAA;AAAA,KACP,CAAA,CAAA;AAED,IAAM,KAAA,CAAA,QAAA,EAAU,MAAM,MAAA,EAAQ,CAAA,CAAA;AAE9B,IAAA,KAAA,CACE,MAAM,KAAM,CAAA,SAAA,EACZ,CAAC,GAAS,KAAA,SAAA,CAAU,QAAQ,GAC9B,CAAA,CAAA;AAEA,IAAA,SAAA,CAAU,MAAM;AACd,MAAA,KAAA,CACE,MAAM,KAAM,CAAA,SAAA,IAAa,UAAW,CAAA,KAAA,EACpC,CAAC,EAAO,KAAA;AACN,QAAA,YAAA,CAAa,QAAQ,EAAM,IAAA,KAAA,CAAA,CAAA;AAAA,OAE7B,EAAA;AAAA,QACE,SAAW,EAAA,IAAA;AAAA,OAEf,CAAA,CAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAQ,OAAA,CAAA,mBAAA,EAAqB,EAAE,QAAA,EAAU,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}