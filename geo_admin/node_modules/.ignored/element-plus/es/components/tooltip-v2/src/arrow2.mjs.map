{"version": 3, "file": "arrow2.mjs", "sources": ["../../../../../../packages/components/tooltip-v2/src/arrow.vue"], "sourcesContent": ["<template>\n  <span ref=\"arrowRef\" :style=\"arrowStyle\" :class=\"ns.e('arrow')\" />\n</template>\n\n<script setup lang=\"ts\">\nimport { computed, inject } from 'vue'\nimport { tooltipV2ContentKey, tooltipV2RootKey } from './constants'\nimport { tooltipV2ArrowProps, tooltipV2ArrowSpecialProps } from './arrow'\n\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElTooltipV2Arrow',\n})\n\nconst props = defineProps({\n  ...tooltipV2ArrowProps,\n  ...tooltipV2ArrowSpecialProps,\n})\n\nconst { ns } = inject(tooltipV2RootKey)!\nconst { arrowRef } = inject(tooltipV2ContentKey)!\n\nconst arrowStyle = computed<CSSProperties>(() => {\n  const { style, width, height } = props\n  const namespace = ns.namespace.value\n\n  return {\n    [`--${namespace}-tooltip-v2-arrow-width`]: `${width}px`,\n    [`--${namespace}-tooltip-v2-arrow-height`]: `${height}px`,\n    [`--${namespace}-tooltip-v2-arrow-border-width`]: `${width / 2}px`,\n    [`--${namespace}-tooltip-v2-arrow-cover-width`]: width / 2 - 1,\n    ...(style || {}),\n  }\n})\n</script>\n"], "names": [], "mappings": ";;;;;mCAWc,CAAA;AAAA,EACZ,IAAM,EAAA,kBAAA;AACR,CAAA,CAAA,CAAA;;;;;;;;;AAOA,IAAM,MAAA,EAAE,EAAO,EAAA,GAAA,MAAA,CAAO,gBAAgB,CAAA,CAAA;AACtC,IAAM,MAAA,EAAE,QAAa,EAAA,GAAA,MAAA,CAAO,mBAAmB,CAAA,CAAA;AAE/C,IAAM,MAAA,UAAA,GAAa,SAAwB,MAAM;AAC/C,MAAM,MAAA,EAAE,KAAO,EAAA,KAAA,EAAO,MAAW,EAAA,GAAA,KAAA,CAAA;AACjC,MAAM,MAAA,SAAA,GAAY,GAAG,SAAU,CAAA,KAAA,CAAA;AAE/B,MAAO,OAAA;AAAA,QACL,CAAC,CAAK,EAAA,EAAA,SAAA,CAAA,uBAAA,CAAA,GAAqC,CAAG,EAAA,KAAA,CAAA,EAAA,CAAA;AAAA,QAC9C,CAAC,CAAK,EAAA,EAAA,SAAA,CAAA,wBAAA,CAAA,GAAsC,CAAG,EAAA,MAAA,CAAA,EAAA,CAAA;AAAA,QAC/C,CAAC,CAAA,EAAA,EAAK,SAA4C,CAAA,8BAAA,CAAA,GAAA,CAAA,EAAG,KAAQ,GAAA,CAAA,CAAA,EAAA,CAAA;AAAA,QAC7D,CAAC,CAAA,EAAA,EAAK,SAA2C,CAAA,6BAAA,CAAA,GAAA,KAAA,GAAQ,CAAI,GAAA,CAAA;AAAA,QAC7D,GAAI,SAAS,EAAC;AAAA,OAChB,CAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;;;;;;;"}