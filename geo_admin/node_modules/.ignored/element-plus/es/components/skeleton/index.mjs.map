{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/skeleton/index.ts"], "sourcesContent": ["import { with<PERSON>nstall, with<PERSON>oopInstall } from '@element-plus/utils'\n\nimport Skeleton from './src/skeleton.vue'\nimport SkeletonItem from './src/skeleton-item.vue'\n\nexport const ElSkeleton = withInstall(Skeleton, {\n  SkeletonItem,\n})\nexport const ElSkeletonItem = withNoopInstall(SkeletonItem)\nexport default ElSkeleton\n\nexport * from './src/skeleton'\nexport * from './src/skeleton-item'\n"], "names": [], "mappings": ";;;;;;;AAGY,MAAC,UAAU,GAAG,WAAW,CAAC,QAAQ,EAAE;AAChD,EAAE,YAAY;AACd,CAAC,EAAE;AACS,MAAC,cAAc,GAAG,eAAe,CAAC,YAAY;;;;"}