{"version": 3, "file": "tree-node.mjs", "sources": ["../../../../../../packages/components/tree-v2/src/tree-node.vue"], "sourcesContent": ["<template>\n  <div\n    ref=\"node$\"\n    :class=\"[\n      ns.b('node'),\n      ns.is('expanded', expanded),\n      ns.is('current', current),\n      ns.is('focusable', !disabled),\n      ns.is('checked', !disabled && checked),\n    ]\"\n    role=\"treeitem\"\n    tabindex=\"-1\"\n    :aria-expanded=\"expanded\"\n    :aria-disabled=\"disabled\"\n    :aria-checked=\"checked\"\n    :data-key=\"node?.key\"\n    @click.stop=\"handleClick\"\n    @contextmenu=\"handleContextMenu\"\n  >\n    <div\n      :class=\"ns.be('node', 'content')\"\n      :style=\"{\n        paddingLeft: `${(node.level - 1) * indent}px`,\n        height: itemSize + 'px',\n      }\"\n    >\n      <el-icon\n        v-if=\"icon\"\n        :class=\"[\n          ns.is('leaf', !!node?.isLeaf),\n          ns.is('hidden', hiddenExpandIcon),\n          {\n            expanded: !node?.isLeaf && expanded,\n          },\n          ns.be('node', 'expand-icon'),\n        ]\"\n        @click.stop=\"handleExpandIconClick\"\n      >\n        <component :is=\"icon\" />\n      </el-icon>\n      <el-checkbox\n        v-if=\"showCheckbox\"\n        :model-value=\"checked\"\n        :indeterminate=\"indeterminate\"\n        :disabled=\"disabled\"\n        @change=\"handleCheckChange\"\n        @click.stop\n      />\n      <el-node-content :node=\"node\" />\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject } from 'vue'\nimport ElIcon from '@element-plus/components/icon'\nimport { CaretRight } from '@element-plus/icons-vue'\nimport ElCheckbox from '@element-plus/components/checkbox'\nimport { useNamespace } from '@element-plus/hooks'\nimport ElNodeContent from './tree-node-content'\nimport {\n  NODE_CONTEXTMENU,\n  ROOT_TREE_INJECTION_KEY,\n  treeNodeEmits,\n  treeNodeProps,\n} from './virtual-tree'\nimport type { CheckboxValueType } from '@element-plus/components/checkbox'\n\ndefineOptions({\n  name: 'ElTreeNode',\n})\n\nconst props = defineProps(treeNodeProps)\nconst emit = defineEmits(treeNodeEmits)\n\nconst tree = inject(ROOT_TREE_INJECTION_KEY)\nconst ns = useNamespace('tree')\n\nconst indent = computed(() => {\n  return tree?.props.indent ?? 16\n})\n\nconst icon = computed(() => {\n  return tree?.props.icon ?? CaretRight\n})\n\nconst handleClick = (e: MouseEvent) => {\n  emit('click', props.node, e)\n}\nconst handleExpandIconClick = () => {\n  emit('toggle', props.node)\n}\nconst handleCheckChange = (value: CheckboxValueType) => {\n  emit('check', props.node, value)\n}\nconst handleContextMenu = (event: Event) => {\n  if (tree?.instance?.vnode?.props?.['onNodeContextmenu']) {\n    event.stopPropagation()\n    event.preventDefault()\n  }\n  tree?.ctx.emit(NODE_CONTEXTMENU, event, props.node?.data, props.node)\n}\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;mCAoEc,CAAA;AAAA,EACZ,IAAM,EAAA,YAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA,IAAA,GAAO,OAAO,uBAAuB,CAAA,CAAA;AAC3C,IAAM,MAAA,EAAA,GAAK,aAAa,MAAM,CAAA,CAAA;AAE9B,IAAM,MAAA,MAAA,GAAS,SAAS,MAAM;AAC5B,MAAO,IAAA,EAAA,CAAA;AAAsB,MAC9B,OAAA,CAAA,EAAA,GAAA,IAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,IAAA,CAAA,KAAA,CAAA,MAAA,KAAA,IAAA,GAAA,EAAA,GAAA,EAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAO,MAAA,IAAA,GAAA,QAAY,CAAQ,MAAA;AAAA,MAC5B,IAAA,EAAA,CAAA;AAED,MAAM,OAAA,CAAA,EAAA,GAAA,IAAA,IAAiC,IAAA,GAAA,KAAA,CAAA,GAAA,IAAA,CAAA,KAAA,CAAA,IAAA,KAAA,IAAA,GAAA,EAAA,GAAA,UAAA,CAAA;AACrC,KAAK,CAAA,CAAA;AAAsB,IAC7B,MAAA,WAAA,GAAA,CAAA,CAAA,KAAA;AACA,MAAA,IAAM,wBAAwB,CAAM;AAClC,KAAK,CAAA;AAAoB,IAC3B,MAAA,qBAAA,GAAA,MAAA;AACA,MAAM,IAAA,CAAA,QAAA,EAAA,KAAA,CAAA,IAAqB,CAA6B,CAAA;AACtD,KAAK,CAAA;AAA0B,IACjC,MAAA,iBAAA,GAAA,CAAA,KAAA,KAAA;AACA,MAAM,IAAA,CAAA,OAAA,EAAA,KAAA,CAAA,IAAoB,EAAkB,KAAA,CAAA,CAAA;AAC1C,KAAA,CAAA;AACE,IAAA,MAAA,iBAAsB,GAAA,CAAA,KAAA,KAAA;AACtB,MAAA,IAAA,EAAA,EAAM,EAAe,EAAA,EAAA,EAAA,EAAA,CAAA;AAAA,MACvB,IAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,IAAA,CAAA,QAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA;AACA,QAAM,KAAA,CAAA,eAA2B,EAAA,CAAA;AAAmC,QACtE,KAAA,CAAA,cAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}