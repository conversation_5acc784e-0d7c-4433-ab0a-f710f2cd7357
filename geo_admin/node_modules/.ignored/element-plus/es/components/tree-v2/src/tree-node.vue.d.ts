import type { CheckboxValueType } from 'element-plus/es/components/checkbox';
declare const _default: import("vue").DefineComponent<{
    readonly node: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./types").TreeNode) | (() => import("./types").TreeNode) | ((new (...args: any[]) => import("./types").TreeNode) | (() => import("./types").TreeNode))[], unknown, unknown, () => import("../../../utils").Mutable<{
        readonly key: -1;
        readonly level: -1;
        readonly data: {};
    }>, boolean>;
    readonly expanded: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly checked: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly indeterminate: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly showCheckbox: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly disabled: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly current: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly hiddenExpandIcon: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly itemSize: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, number, boolean>;
}, {
    props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
        readonly node: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./types").TreeNode) | (() => import("./types").TreeNode) | ((new (...args: any[]) => import("./types").TreeNode) | (() => import("./types").TreeNode))[], unknown, unknown, () => import("../../../utils").Mutable<{
            readonly key: -1;
            readonly level: -1;
            readonly data: {};
        }>, boolean>;
        readonly expanded: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly checked: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly indeterminate: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly showCheckbox: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly disabled: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly current: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly hiddenExpandIcon: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly itemSize: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, number, boolean>;
    }>> & {
        onClick?: ((node: import("./types").TreeNode, e: MouseEvent) => any) | undefined;
        onToggle?: ((node: import("./types").TreeNode) => any) | undefined;
        onCheck?: ((node: import("./types").TreeNode, checked: CheckboxValueType) => any) | undefined;
    }>>;
    emit: ((event: "click", node: import("./types").TreeNode, e: MouseEvent) => void) & ((event: "check", node: import("./types").TreeNode, checked: CheckboxValueType) => void) & ((event: "toggle", node: import("./types").TreeNode) => void);
    tree: import("./types").TreeContext | undefined;
    ns: {
        namespace: import("vue").ComputedRef<string>;
        b: (blockSuffix?: string) => string;
        e: (element?: string | undefined) => string;
        m: (modifier?: string | undefined) => string;
        be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
        em: (element?: string | undefined, modifier?: string | undefined) => string;
        bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
        bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
        is: {
            (name: string, state: boolean | undefined): string;
            (name: string): string;
        };
        cssVar: (object: Record<string, string>) => Record<string, string>;
        cssVarName: (name: string) => string;
        cssVarBlock: (object: Record<string, string>) => Record<string, string>;
        cssVarBlockName: (name: string) => string;
    };
    indent: import("vue").ComputedRef<number>;
    icon: import("vue").ComputedRef<any>;
    handleClick: (e: MouseEvent) => void;
    handleExpandIconClick: () => void;
    handleCheckChange: (value: CheckboxValueType) => void;
    handleContextMenu: (event: Event) => void;
    ElIcon: import("../../../utils").SFCWithInstall<import("vue").DefineComponent<{
        readonly size: {
            readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly color: {
            readonly type: import("vue").PropType<string>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
    }, {
        props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
            readonly size: {
                readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly color: {
                readonly type: import("vue").PropType<string>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
        }>> & {
            [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
        }>>;
        ns: {
            namespace: import("vue").ComputedRef<string>;
            b: (blockSuffix?: string) => string;
            e: (element?: string | undefined) => string;
            m: (modifier?: string | undefined) => string;
            be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
            em: (element?: string | undefined, modifier?: string | undefined) => string;
            bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
            bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        style: import("vue").ComputedRef<import("vue").CSSProperties>;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
        readonly size: {
            readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly color: {
            readonly type: import("vue").PropType<string>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
    }>>, {}>> & Record<string, any>;
    ElCheckbox: import("../../../utils").SFCWithInstall<import("vue").DefineComponent<{
        modelValue: {
            type: (NumberConstructor | BooleanConstructor | StringConstructor)[];
            default: undefined;
        };
        label: {
            type: (ObjectConstructor | NumberConstructor | BooleanConstructor | StringConstructor)[];
            default: undefined;
        };
        indeterminate: BooleanConstructor;
        disabled: BooleanConstructor;
        checked: BooleanConstructor;
        name: {
            type: StringConstructor;
            default: undefined;
        };
        trueLabel: {
            type: (NumberConstructor | StringConstructor)[];
            default: undefined;
        };
        falseLabel: {
            type: (NumberConstructor | StringConstructor)[];
            default: undefined;
        };
        id: {
            type: StringConstructor;
            default: undefined;
        };
        controls: {
            type: StringConstructor;
            default: undefined;
        };
        border: BooleanConstructor;
        size: {
            readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", never>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        tabindex: (NumberConstructor | StringConstructor)[];
        validateEvent: {
            type: BooleanConstructor;
            default: boolean;
        };
    }, {
        props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
            modelValue: {
                type: (NumberConstructor | BooleanConstructor | StringConstructor)[];
                default: undefined;
            };
            label: {
                type: (ObjectConstructor | NumberConstructor | BooleanConstructor | StringConstructor)[];
                default: undefined;
            };
            indeterminate: BooleanConstructor;
            disabled: BooleanConstructor;
            checked: BooleanConstructor;
            name: {
                type: StringConstructor;
                default: undefined;
            };
            trueLabel: {
                type: (NumberConstructor | StringConstructor)[];
                default: undefined;
            };
            falseLabel: {
                type: (NumberConstructor | StringConstructor)[];
                default: undefined;
            };
            id: {
                type: StringConstructor;
                default: undefined;
            };
            controls: {
                type: StringConstructor;
                default: undefined;
            };
            border: BooleanConstructor;
            size: {
                readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", never>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            tabindex: (NumberConstructor | StringConstructor)[];
            validateEvent: {
                type: BooleanConstructor;
                default: boolean;
            };
        }>> & {
            onChange?: ((val: CheckboxValueType) => any) | undefined;
            "onUpdate:modelValue"?: ((val: CheckboxValueType) => any) | undefined;
        }>>;
        slots: Readonly<{
            [name: string]: import("vue").Slot | undefined;
        }>;
        inputId: import("vue").Ref<string | undefined>;
        isLabeledByFormItem: import("vue").ComputedRef<boolean>;
        isChecked: import("vue").ComputedRef<boolean>;
        isDisabled: import("vue").ComputedRef<boolean>;
        isFocused: import("vue").Ref<boolean>;
        checkboxSize: import("vue").ComputedRef<"" | "default" | "small" | "large">;
        hasOwnLabel: import("vue").ComputedRef<boolean>;
        model: import("vue").WritableComputedRef<any>;
        handleChange: (e: Event) => void;
        onClickRoot: (e: MouseEvent) => Promise<void>;
        ns: {
            namespace: import("vue").ComputedRef<string>;
            b: (blockSuffix?: string) => string;
            e: (element?: string | undefined) => string;
            m: (modifier?: string | undefined) => string;
            be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
            em: (element?: string | undefined, modifier?: string | undefined) => string;
            bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
            bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        compKls: import("vue").ComputedRef<string[]>;
        spanKls: import("vue").ComputedRef<string[]>;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
        "update:modelValue": (val: CheckboxValueType) => boolean;
        change: (val: CheckboxValueType) => boolean;
    }, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
        modelValue: {
            type: (NumberConstructor | BooleanConstructor | StringConstructor)[];
            default: undefined;
        };
        label: {
            type: (ObjectConstructor | NumberConstructor | BooleanConstructor | StringConstructor)[];
            default: undefined;
        };
        indeterminate: BooleanConstructor;
        disabled: BooleanConstructor;
        checked: BooleanConstructor;
        name: {
            type: StringConstructor;
            default: undefined;
        };
        trueLabel: {
            type: (NumberConstructor | StringConstructor)[];
            default: undefined;
        };
        falseLabel: {
            type: (NumberConstructor | StringConstructor)[];
            default: undefined;
        };
        id: {
            type: StringConstructor;
            default: undefined;
        };
        controls: {
            type: StringConstructor;
            default: undefined;
        };
        border: BooleanConstructor;
        size: {
            readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", never>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        tabindex: (NumberConstructor | StringConstructor)[];
        validateEvent: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>> & {
        onChange?: ((val: CheckboxValueType) => any) | undefined;
        "onUpdate:modelValue"?: ((val: CheckboxValueType) => any) | undefined;
    }, {
        modelValue: string | number | boolean;
        label: string | number | boolean | Record<string, any>;
        id: string;
        disabled: boolean;
        name: string;
        validateEvent: boolean;
        border: boolean;
        indeterminate: boolean;
        checked: boolean;
        trueLabel: string | number;
        falseLabel: string | number;
        controls: string;
    }>> & {
        CheckboxButton: import("vue").DefineComponent<{
            modelValue: {
                type: (NumberConstructor | BooleanConstructor | StringConstructor)[];
                default: undefined;
            };
            label: {
                type: (ObjectConstructor | NumberConstructor | BooleanConstructor | StringConstructor)[];
                default: undefined;
            };
            indeterminate: BooleanConstructor;
            disabled: BooleanConstructor;
            checked: BooleanConstructor;
            name: {
                type: StringConstructor;
                default: undefined;
            };
            trueLabel: {
                type: (NumberConstructor | StringConstructor)[];
                default: undefined;
            };
            falseLabel: {
                type: (NumberConstructor | StringConstructor)[];
                default: undefined;
            };
            id: {
                type: StringConstructor;
                default: undefined;
            };
            controls: {
                type: StringConstructor;
                default: undefined;
            };
            border: BooleanConstructor;
            size: {
                readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", never>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            tabindex: (NumberConstructor | StringConstructor)[];
            validateEvent: {
                type: BooleanConstructor;
                default: boolean;
            };
        }, {
            props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
                modelValue: {
                    type: (NumberConstructor | BooleanConstructor | StringConstructor)[];
                    default: undefined;
                };
                label: {
                    type: (ObjectConstructor | NumberConstructor | BooleanConstructor | StringConstructor)[];
                    default: undefined;
                };
                indeterminate: BooleanConstructor;
                disabled: BooleanConstructor;
                checked: BooleanConstructor;
                name: {
                    type: StringConstructor;
                    default: undefined;
                };
                trueLabel: {
                    type: (NumberConstructor | StringConstructor)[];
                    default: undefined;
                };
                falseLabel: {
                    type: (NumberConstructor | StringConstructor)[];
                    default: undefined;
                };
                id: {
                    type: StringConstructor;
                    default: undefined;
                };
                controls: {
                    type: StringConstructor;
                    default: undefined;
                };
                border: BooleanConstructor;
                size: {
                    readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", never>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                tabindex: (NumberConstructor | StringConstructor)[];
                validateEvent: {
                    type: BooleanConstructor;
                    default: boolean;
                };
            }>> & {
                onChange?: ((val: CheckboxValueType) => any) | undefined;
                "onUpdate:modelValue"?: ((val: CheckboxValueType) => any) | undefined;
            }>>;
            slots: Readonly<{
                [name: string]: import("vue").Slot | undefined;
            }>;
            isFocused: import("vue").Ref<boolean>;
            isChecked: import("vue").ComputedRef<boolean>;
            isDisabled: import("vue").ComputedRef<boolean>;
            checkboxButtonSize: import("vue").ComputedRef<"" | "default" | "small" | "large">;
            model: import("vue").WritableComputedRef<any>;
            handleChange: (e: Event) => void;
            checkboxGroup: ({
                modelValue?: import("vue").WritableComputedRef<any> | undefined;
                changeEvent?: ((...args: any) => any) | undefined;
            } & import("vue").ToRefs<Pick<import("element-plus/es/components/checkbox").CheckboxGroupProps, "fill" | "size" | "disabled" | "validateEvent" | "max" | "min" | "textColor">>) | undefined;
            ns: {
                namespace: import("vue").ComputedRef<string>;
                b: (blockSuffix?: string) => string;
                e: (element?: string | undefined) => string;
                m: (modifier?: string | undefined) => string;
                be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                em: (element?: string | undefined, modifier?: string | undefined) => string;
                bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                is: {
                    (name: string, state: boolean | undefined): string;
                    (name: string): string;
                };
                cssVar: (object: Record<string, string>) => Record<string, string>;
                cssVarName: (name: string) => string;
                cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                cssVarBlockName: (name: string) => string;
            };
            activeStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
            labelKls: import("vue").ComputedRef<string[]>;
        }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
            "update:modelValue": (val: CheckboxValueType) => boolean;
            change: (val: CheckboxValueType) => boolean;
        }, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
            modelValue: {
                type: (NumberConstructor | BooleanConstructor | StringConstructor)[];
                default: undefined;
            };
            label: {
                type: (ObjectConstructor | NumberConstructor | BooleanConstructor | StringConstructor)[];
                default: undefined;
            };
            indeterminate: BooleanConstructor;
            disabled: BooleanConstructor;
            checked: BooleanConstructor;
            name: {
                type: StringConstructor;
                default: undefined;
            };
            trueLabel: {
                type: (NumberConstructor | StringConstructor)[];
                default: undefined;
            };
            falseLabel: {
                type: (NumberConstructor | StringConstructor)[];
                default: undefined;
            };
            id: {
                type: StringConstructor;
                default: undefined;
            };
            controls: {
                type: StringConstructor;
                default: undefined;
            };
            border: BooleanConstructor;
            size: {
                readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", never>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            tabindex: (NumberConstructor | StringConstructor)[];
            validateEvent: {
                type: BooleanConstructor;
                default: boolean;
            };
        }>> & {
            onChange?: ((val: CheckboxValueType) => any) | undefined;
            "onUpdate:modelValue"?: ((val: CheckboxValueType) => any) | undefined;
        }, {
            modelValue: string | number | boolean;
            label: string | number | boolean | Record<string, any>;
            id: string;
            disabled: boolean;
            name: string;
            validateEvent: boolean;
            border: boolean;
            indeterminate: boolean;
            checked: boolean;
            trueLabel: string | number;
            falseLabel: string | number;
            controls: string;
        }>;
        CheckboxGroup: import("vue").DefineComponent<{
            readonly modelValue: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("element-plus/es/components/checkbox").CheckboxGroupValueType) | (() => import("element-plus/es/components/checkbox").CheckboxGroupValueType) | ((new (...args: any[]) => import("element-plus/es/components/checkbox").CheckboxGroupValueType) | (() => import("element-plus/es/components/checkbox").CheckboxGroupValueType))[], unknown, unknown, () => never[], boolean>;
            readonly disabled: BooleanConstructor;
            readonly min: NumberConstructor;
            readonly max: NumberConstructor;
            readonly size: {
                readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", never>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly label: StringConstructor;
            readonly fill: StringConstructor;
            readonly textColor: StringConstructor;
            readonly tag: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "div", boolean>;
            readonly validateEvent: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        }, {
            props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
                readonly modelValue: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("element-plus/es/components/checkbox").CheckboxGroupValueType) | (() => import("element-plus/es/components/checkbox").CheckboxGroupValueType) | ((new (...args: any[]) => import("element-plus/es/components/checkbox").CheckboxGroupValueType) | (() => import("element-plus/es/components/checkbox").CheckboxGroupValueType))[], unknown, unknown, () => never[], boolean>;
                readonly disabled: BooleanConstructor;
                readonly min: NumberConstructor;
                readonly max: NumberConstructor;
                readonly size: {
                    readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", never>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly label: StringConstructor;
                readonly fill: StringConstructor;
                readonly textColor: StringConstructor;
                readonly tag: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "div", boolean>;
                readonly validateEvent: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            }>> & {
                onChange?: ((val: CheckboxValueType[]) => any) | undefined;
                "onUpdate:modelValue"?: ((val: import("element-plus/es/components/checkbox").CheckboxGroupValueType) => any) | undefined;
            }>>;
            emit: ((event: "update:modelValue", val: import("element-plus/es/components/checkbox").CheckboxGroupValueType) => void) & ((event: "change", val: CheckboxValueType[]) => void);
            ns: {
                namespace: import("vue").ComputedRef<string>;
                b: (blockSuffix?: string) => string;
                e: (element?: string | undefined) => string;
                m: (modifier?: string | undefined) => string;
                be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                em: (element?: string | undefined, modifier?: string | undefined) => string;
                bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                is: {
                    (name: string, state: boolean | undefined): string;
                    (name: string): string;
                };
                cssVar: (object: Record<string, string>) => Record<string, string>;
                cssVarName: (name: string) => string;
                cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                cssVarBlockName: (name: string) => string;
            };
            formItem: import("../..").FormItemContext | undefined;
            groupId: import("vue").Ref<string | undefined>;
            isLabeledByFormItem: import("vue").ComputedRef<boolean>;
            changeEvent: (value: import("element-plus/es/components/checkbox").CheckboxGroupValueType) => Promise<void>;
            modelValue: import("vue").WritableComputedRef<import("element-plus/es/components/checkbox").CheckboxGroupValueType>;
        }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
            "update:modelValue": (val: import("element-plus/es/components/checkbox").CheckboxGroupValueType) => boolean;
            change: (val: CheckboxValueType[]) => boolean;
        }, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
            readonly modelValue: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("element-plus/es/components/checkbox").CheckboxGroupValueType) | (() => import("element-plus/es/components/checkbox").CheckboxGroupValueType) | ((new (...args: any[]) => import("element-plus/es/components/checkbox").CheckboxGroupValueType) | (() => import("element-plus/es/components/checkbox").CheckboxGroupValueType))[], unknown, unknown, () => never[], boolean>;
            readonly disabled: BooleanConstructor;
            readonly min: NumberConstructor;
            readonly max: NumberConstructor;
            readonly size: {
                readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", never>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly label: StringConstructor;
            readonly fill: StringConstructor;
            readonly textColor: StringConstructor;
            readonly tag: import("../../../utils").EpPropFinalized<StringConstructor, unknown, unknown, "div", boolean>;
            readonly validateEvent: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        }>> & {
            onChange?: ((val: CheckboxValueType[]) => any) | undefined;
            "onUpdate:modelValue"?: ((val: import("element-plus/es/components/checkbox").CheckboxGroupValueType) => any) | undefined;
        }, {
            readonly modelValue: import("element-plus/es/components/checkbox").CheckboxGroupValueType;
            readonly disabled: boolean;
            readonly validateEvent: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly tag: string;
        }>;
    };
    ElNodeContent: import("vue").DefineComponent<{
        readonly node: {
            readonly type: import("vue").PropType<import("./types").TreeNode>;
            readonly required: true;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }> | import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>[], unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
        readonly node: {
            readonly type: import("vue").PropType<import("./types").TreeNode>;
            readonly required: true;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
    }>>, {}>;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    click: (node: import("./types").TreeNode, e: MouseEvent) => boolean;
    toggle: (node: import("./types").TreeNode) => boolean;
    check: (node: import("./types").TreeNode, checked: CheckboxValueType) => boolean;
}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    readonly node: import("../../../utils").EpPropFinalized<(new (...args: any[]) => import("./types").TreeNode) | (() => import("./types").TreeNode) | ((new (...args: any[]) => import("./types").TreeNode) | (() => import("./types").TreeNode))[], unknown, unknown, () => import("../../../utils").Mutable<{
        readonly key: -1;
        readonly level: -1;
        readonly data: {};
    }>, boolean>;
    readonly expanded: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly checked: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly indeterminate: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly showCheckbox: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly disabled: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly current: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly hiddenExpandIcon: import("../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly itemSize: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, number, boolean>;
}>> & {
    onClick?: ((node: import("./types").TreeNode, e: MouseEvent) => any) | undefined;
    onToggle?: ((node: import("./types").TreeNode) => any) | undefined;
    onCheck?: ((node: import("./types").TreeNode, checked: CheckboxValueType) => any) | undefined;
}, {
    readonly disabled: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    readonly expanded: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    readonly current: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    readonly indeterminate: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    readonly checked: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    readonly node: import("./types").TreeNode;
    readonly itemSize: number;
    readonly showCheckbox: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    readonly hiddenExpandIcon: import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
}>;
export default _default;
