{"version": 3, "file": "useCheck.mjs", "sources": ["../../../../../../../packages/components/tree-v2/src/composables/useCheck.ts"], "sourcesContent": ["import { getCurrentInstance, nextTick, ref, watch } from 'vue'\nimport {\n  NODE_CHECK,\n  NODE_CHECK_CHANGE,\n  SetOperationEnum,\n} from '../virtual-tree'\nimport type { CheckboxValueType } from '@element-plus/components/checkbox'\nimport type { Ref } from 'vue'\nimport type { Tree, TreeKey, TreeNode, TreeNodeData, TreeProps } from '../types'\n\nexport function useCheck(props: TreeProps, tree: Ref<Tree | undefined>) {\n  const checkedKeys = ref<Set<TreeKey>>(new Set())\n  const indeterminateKeys = ref<Set<TreeKey>>(new Set())\n  const { emit } = getCurrentInstance()!\n\n  watch(\n    [() => tree.value, () => props.defaultCheckedKeys],\n    () => {\n      return nextTick(() => {\n        _setCheckedKeys(props.defaultCheckedKeys)\n      })\n    },\n    {\n      immediate: true,\n    }\n  )\n\n  const updateCheckedKeys = () => {\n    if (!tree.value || !props.showCheckbox || props.checkStrictly) {\n      return\n    }\n    const { levelTreeNodeMap, maxLevel } = tree.value\n    const checkedKeySet = checkedKeys.value\n    const indeterminateKeySet = new Set<TreeKey>()\n    // It is easier to determine the indeterminate state by\n    // traversing from bottom to top\n    // leaf nodes not have indeterminate status and can be skipped\n    for (let level = maxLevel - 1; level >= 1; --level) {\n      const nodes = levelTreeNodeMap.get(level)\n      if (!nodes) continue\n      nodes.forEach((node) => {\n        const children = node.children\n        if (children) {\n          // Whether all child nodes are selected\n          let allChecked = true\n          // Whether a child node is selected\n          let hasChecked = false\n          for (const childNode of children) {\n            const key = childNode.key\n            if (checkedKeySet.has(key)) {\n              hasChecked = true\n            } else if (indeterminateKeySet.has(key)) {\n              allChecked = false\n              hasChecked = true\n              break\n            } else {\n              allChecked = false\n            }\n          }\n          if (allChecked) {\n            checkedKeySet.add(node.key)\n          } else if (hasChecked) {\n            indeterminateKeySet.add(node.key)\n            checkedKeySet.delete(node.key)\n          } else {\n            checkedKeySet.delete(node.key)\n            indeterminateKeySet.delete(node.key)\n          }\n        }\n      })\n    }\n    indeterminateKeys.value = indeterminateKeySet\n  }\n\n  const isChecked = (node: TreeNode) => checkedKeys.value.has(node.key)\n\n  const isIndeterminate = (node: TreeNode) =>\n    indeterminateKeys.value.has(node.key)\n\n  const toggleCheckbox = (\n    node: TreeNode,\n    isChecked: CheckboxValueType,\n    nodeClick = true\n  ) => {\n    const checkedKeySet = checkedKeys.value\n    const toggle = (node: TreeNode, checked: CheckboxValueType) => {\n      checkedKeySet[checked ? SetOperationEnum.ADD : SetOperationEnum.DELETE](\n        node.key\n      )\n      const children = node.children\n      if (!props.checkStrictly && children) {\n        children.forEach((childNode) => {\n          if (!childNode.disabled) {\n            toggle(childNode, checked)\n          }\n        })\n      }\n    }\n    toggle(node, isChecked)\n    updateCheckedKeys()\n    if (nodeClick) {\n      afterNodeCheck(node, isChecked)\n    }\n  }\n\n  const afterNodeCheck = (node: TreeNode, checked: CheckboxValueType) => {\n    const { checkedNodes, checkedKeys } = getChecked()\n    const { halfCheckedNodes, halfCheckedKeys } = getHalfChecked()\n    emit(NODE_CHECK, node.data, {\n      checkedKeys,\n      checkedNodes,\n      halfCheckedKeys,\n      halfCheckedNodes,\n    })\n    emit(NODE_CHECK_CHANGE, node.data, checked)\n  }\n\n  // expose\n  function getCheckedKeys(leafOnly = false): TreeKey[] {\n    return getChecked(leafOnly).checkedKeys\n  }\n\n  function getCheckedNodes(leafOnly = false): TreeNodeData[] {\n    return getChecked(leafOnly).checkedNodes\n  }\n\n  function getHalfCheckedKeys(): TreeKey[] {\n    return getHalfChecked().halfCheckedKeys\n  }\n\n  function getHalfCheckedNodes(): TreeNodeData[] {\n    return getHalfChecked().halfCheckedNodes\n  }\n\n  function getChecked(leafOnly = false): {\n    checkedKeys: TreeKey[]\n    checkedNodes: TreeNodeData[]\n  } {\n    const checkedNodes: TreeNodeData[] = []\n    const keys: TreeKey[] = []\n    if (tree?.value && props.showCheckbox) {\n      const { treeNodeMap } = tree.value\n      checkedKeys.value.forEach((key) => {\n        const node = treeNodeMap.get(key)\n        if (node && (!leafOnly || (leafOnly && node.isLeaf))) {\n          keys.push(key)\n          checkedNodes.push(node.data)\n        }\n      })\n    }\n    return {\n      checkedKeys: keys,\n      checkedNodes,\n    }\n  }\n\n  function getHalfChecked(): {\n    halfCheckedKeys: TreeKey[]\n    halfCheckedNodes: TreeNodeData[]\n  } {\n    const halfCheckedNodes: TreeNodeData[] = []\n    const halfCheckedKeys: TreeKey[] = []\n    if (tree?.value && props.showCheckbox) {\n      const { treeNodeMap } = tree.value\n      indeterminateKeys.value.forEach((key) => {\n        const node = treeNodeMap.get(key)\n        if (node) {\n          halfCheckedKeys.push(key)\n          halfCheckedNodes.push(node.data)\n        }\n      })\n    }\n    return {\n      halfCheckedNodes,\n      halfCheckedKeys,\n    }\n  }\n\n  function setCheckedKeys(keys: TreeKey[]) {\n    checkedKeys.value.clear()\n    indeterminateKeys.value.clear()\n    _setCheckedKeys(keys)\n  }\n\n  function setChecked(key: TreeKey, isChecked: boolean) {\n    if (tree?.value && props.showCheckbox) {\n      const node = tree.value.treeNodeMap.get(key)\n      if (node) {\n        toggleCheckbox(node, isChecked, false)\n      }\n    }\n  }\n\n  function _setCheckedKeys(keys: TreeKey[]) {\n    if (tree?.value) {\n      const { treeNodeMap } = tree.value\n      if (props.showCheckbox && treeNodeMap && keys) {\n        for (const key of keys) {\n          const node = treeNodeMap.get(key)\n          if (node && !isChecked(node)) {\n            toggleCheckbox(node, true, false)\n          }\n        }\n      }\n    }\n  }\n\n  return {\n    updateCheckedKeys,\n    toggleCheckbox,\n    isChecked,\n    isIndeterminate,\n    // expose\n    getCheckedKeys,\n    getCheckedNodes,\n    getHalfCheckedKeys,\n    getHalfCheckedNodes,\n    setChecked,\n    setCheckedKeys,\n  }\n}\n"], "names": [], "mappings": ";;;AAMO,SAAS,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE;AACtC,EAAE,MAAM,WAAW,GAAG,GAAG,iBAAiB,IAAI,GAAG,EAAE,CAAC,CAAC;AACrD,EAAE,MAAM,iBAAiB,GAAG,GAAG,iBAAiB,IAAI,GAAG,EAAE,CAAC,CAAC;AAC3D,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,kBAAkB,EAAE,CAAC;AACxC,EAAE,KAAK,CAAC,CAAC,MAAM,IAAI,CAAC,KAAK,EAAE,MAAM,KAAK,CAAC,kBAAkB,CAAC,EAAE,MAAM;AAClE,IAAI,OAAO,QAAQ,CAAC,MAAM;AAC1B,MAAM,eAAe,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;AAChD,KAAK,CAAC,CAAC;AACP,GAAG,EAAE;AACL,IAAI,SAAS,EAAE,IAAI;AACnB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,iBAAiB,GAAG,MAAM;AAClC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,YAAY,IAAI,KAAK,CAAC,aAAa,EAAE;AACnE,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,EAAE,gBAAgB,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;AACtD,IAAI,MAAM,aAAa,GAAG,WAAW,CAAC,KAAK,CAAC;AAC5C,IAAI,MAAM,mBAAmB,mBAAmB,IAAI,GAAG,EAAE,CAAC;AAC1D,IAAI,KAAK,IAAI,KAAK,GAAG,QAAQ,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,KAAK,EAAE;AACxD,MAAM,MAAM,KAAK,GAAG,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAChD,MAAM,IAAI,CAAC,KAAK;AAChB,QAAQ,SAAS;AACjB,MAAM,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AAC9B,QAAQ,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACvC,QAAQ,IAAI,QAAQ,EAAE;AACtB,UAAU,IAAI,UAAU,GAAG,IAAI,CAAC;AAChC,UAAU,IAAI,UAAU,GAAG,KAAK,CAAC;AACjC,UAAU,KAAK,MAAM,SAAS,IAAI,QAAQ,EAAE;AAC5C,YAAY,MAAM,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC;AACtC,YAAY,IAAI,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AACxC,cAAc,UAAU,GAAG,IAAI,CAAC;AAChC,aAAa,MAAM,IAAI,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AACrD,cAAc,UAAU,GAAG,KAAK,CAAC;AACjC,cAAc,UAAU,GAAG,IAAI,CAAC;AAChC,cAAc,MAAM;AACpB,aAAa,MAAM;AACnB,cAAc,UAAU,GAAG,KAAK,CAAC;AACjC,aAAa;AACb,WAAW;AACX,UAAU,IAAI,UAAU,EAAE;AAC1B,YAAY,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACxC,WAAW,MAAM,IAAI,UAAU,EAAE;AACjC,YAAY,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC9C,YAAY,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3C,WAAW,MAAM;AACjB,YAAY,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3C,YAAY,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACjD,WAAW;AACX,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,iBAAiB,CAAC,KAAK,GAAG,mBAAmB,CAAC;AAClD,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,CAAC,IAAI,KAAK,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC9D,EAAE,MAAM,eAAe,GAAG,CAAC,IAAI,KAAK,iBAAiB,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC1E,EAAE,MAAM,cAAc,GAAG,CAAC,IAAI,EAAE,UAAU,EAAE,SAAS,GAAG,IAAI,KAAK;AACjE,IAAI,MAAM,aAAa,GAAG,WAAW,CAAC,KAAK,CAAC;AAC5C,IAAI,MAAM,MAAM,GAAG,CAAC,KAAK,EAAE,OAAO,KAAK;AACvC,MAAM,aAAa,CAAC,OAAO,GAAG,gBAAgB,CAAC,GAAG,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACzF,MAAM,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;AACtC,MAAM,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI,QAAQ,EAAE;AAC5C,QAAQ,QAAQ,CAAC,OAAO,CAAC,CAAC,SAAS,KAAK;AACxC,UAAU,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;AACnC,YAAY,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AACvC,WAAW;AACX,SAAS,CAAC,CAAC;AACX,OAAO;AACP,KAAK,CAAC;AACN,IAAI,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;AAC7B,IAAI,iBAAiB,EAAE,CAAC;AACxB,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,cAAc,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;AACvC,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,CAAC,IAAI,EAAE,OAAO,KAAK;AAC5C,IAAI,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,UAAU,EAAE,CAAC;AACrE,IAAI,MAAM,EAAE,gBAAgB,EAAE,eAAe,EAAE,GAAG,cAAc,EAAE,CAAC;AACnE,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,EAAE;AAChC,MAAM,WAAW,EAAE,YAAY;AAC/B,MAAM,YAAY;AAClB,MAAM,eAAe;AACrB,MAAM,gBAAgB;AACtB,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAChD,GAAG,CAAC;AACJ,EAAE,SAAS,cAAc,CAAC,QAAQ,GAAG,KAAK,EAAE;AAC5C,IAAI,OAAO,UAAU,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC;AAC5C,GAAG;AACH,EAAE,SAAS,eAAe,CAAC,QAAQ,GAAG,KAAK,EAAE;AAC7C,IAAI,OAAO,UAAU,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC;AAC7C,GAAG;AACH,EAAE,SAAS,kBAAkB,GAAG;AAChC,IAAI,OAAO,cAAc,EAAE,CAAC,eAAe,CAAC;AAC5C,GAAG;AACH,EAAE,SAAS,mBAAmB,GAAG;AACjC,IAAI,OAAO,cAAc,EAAE,CAAC,gBAAgB,CAAC;AAC7C,GAAG;AACH,EAAE,SAAS,UAAU,CAAC,QAAQ,GAAG,KAAK,EAAE;AACxC,IAAI,MAAM,YAAY,GAAG,EAAE,CAAC;AAC5B,IAAI,MAAM,IAAI,GAAG,EAAE,CAAC;AACpB,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,YAAY,EAAE;AACpE,MAAM,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;AACzC,MAAM,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AACzC,QAAQ,MAAM,IAAI,GAAG,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC1C,QAAQ,IAAI,IAAI,KAAK,CAAC,QAAQ,IAAI,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE;AAC5D,UAAU,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACzB,UAAU,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACvC,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,OAAO;AACX,MAAM,WAAW,EAAE,IAAI;AACvB,MAAM,YAAY;AAClB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,SAAS,cAAc,GAAG;AAC5B,IAAI,MAAM,gBAAgB,GAAG,EAAE,CAAC;AAChC,IAAI,MAAM,eAAe,GAAG,EAAE,CAAC;AAC/B,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,YAAY,EAAE;AACpE,MAAM,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;AACzC,MAAM,iBAAiB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AAC/C,QAAQ,MAAM,IAAI,GAAG,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC1C,QAAQ,IAAI,IAAI,EAAE;AAClB,UAAU,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACpC,UAAU,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3C,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,OAAO;AACX,MAAM,gBAAgB;AACtB,MAAM,eAAe;AACrB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,SAAS,cAAc,CAAC,IAAI,EAAE;AAChC,IAAI,WAAW,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;AAC9B,IAAI,iBAAiB,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;AACpC,IAAI,eAAe,CAAC,IAAI,CAAC,CAAC;AAC1B,GAAG;AACH,EAAE,SAAS,UAAU,CAAC,GAAG,EAAE,UAAU,EAAE;AACvC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,YAAY,EAAE;AACpE,MAAM,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACnD,MAAM,IAAI,IAAI,EAAE;AAChB,QAAQ,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;AAChD,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,SAAS,eAAe,CAAC,IAAI,EAAE;AACjC,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE;AAC5C,MAAM,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;AACzC,MAAM,IAAI,KAAK,CAAC,YAAY,IAAI,WAAW,IAAI,IAAI,EAAE;AACrD,QAAQ,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;AAChC,UAAU,MAAM,IAAI,GAAG,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC5C,UAAU,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;AACxC,YAAY,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AAC9C,WAAW;AACX,SAAS;AACT,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,OAAO;AACT,IAAI,iBAAiB;AACrB,IAAI,cAAc;AAClB,IAAI,SAAS;AACb,IAAI,eAAe;AACnB,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAI,kBAAkB;AACtB,IAAI,mBAAmB;AACvB,IAAI,UAAU;AACd,IAAI,cAAc;AAClB,GAAG,CAAC;AACJ;;;;"}