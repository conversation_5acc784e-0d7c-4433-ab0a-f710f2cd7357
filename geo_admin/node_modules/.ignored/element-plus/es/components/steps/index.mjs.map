{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/steps/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON>oopInstall } from '@element-plus/utils'\n\nimport Steps from './src/steps.vue'\nimport Step from './src/item.vue'\n\nexport const ElSteps = withInstall(Steps, {\n  Step,\n})\nexport default ElSteps\nexport const ElStep = withNoopInstall(Step)\n\nexport * from './src/item'\nexport * from './src/steps'\n"], "names": [], "mappings": ";;;;;;;AAGY,MAAC,OAAO,GAAG,WAAW,CAAC,KAAK,EAAE;AAC1C,EAAE,IAAI;AACN,CAAC,EAAE;AAES,MAAC,MAAM,GAAG,eAAe,CAAC,IAAI;;;;"}