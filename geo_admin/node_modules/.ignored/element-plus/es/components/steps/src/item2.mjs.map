{"version": 3, "file": "item2.mjs", "sources": ["../../../../../../packages/components/steps/src/item.vue"], "sourcesContent": ["<template>\n  <div :style=\"style\" :class=\"containerKls\">\n    <!-- icon & line -->\n    <div :class=\"[ns.e('head'), ns.is(currentStatus)]\">\n      <div v-if=\"!isSimple\" :class=\"ns.e('line')\">\n        <i :class=\"ns.e('line-inner')\" :style=\"lineStyle\" />\n      </div>\n\n      <div\n        :class=\"[ns.e('icon'), ns.is(icon || $slots.icon ? 'icon' : 'text')]\"\n      >\n        <slot name=\"icon\">\n          <el-icon v-if=\"icon\" :class=\"ns.e('icon-inner')\">\n            <component :is=\"icon\" />\n          </el-icon>\n          <el-icon\n            v-else-if=\"currentStatus === 'success'\"\n            :class=\"[ns.e('icon-inner'), ns.is('status')]\"\n          >\n            <Check />\n          </el-icon>\n          <el-icon\n            v-else-if=\"currentStatus === 'error'\"\n            :class=\"[ns.e('icon-inner'), ns.is('status')]\"\n          >\n            <Close />\n          </el-icon>\n          <div v-else-if=\"!isSimple\" :class=\"ns.e('icon-inner')\">\n            {{ index + 1 }}\n          </div>\n        </slot>\n      </div>\n    </div>\n    <!-- title & description -->\n    <div :class=\"ns.e('main')\">\n      <div :class=\"[ns.e('title'), ns.is(currentStatus)]\">\n        <slot name=\"title\">{{ title }}</slot>\n      </div>\n      <div v-if=\"isSimple\" :class=\"ns.e('arrow')\" />\n      <div v-else :class=\"[ns.e('description'), ns.is(currentStatus)]\">\n        <slot name=\"description\">{{ description }}</slot>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  getCurrentInstance,\n  inject,\n  onBeforeUnmount,\n  onMounted,\n  reactive,\n  ref,\n  watch,\n} from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { Check, Close } from '@element-plus/icons-vue'\nimport { isNumber } from '@element-plus/utils'\nimport { stepProps } from './item'\n\nimport type { CSSProperties, Ref } from 'vue'\n\nexport interface IStepsProps {\n  space: number | string\n  active: number\n  direction: string\n  alignCenter: boolean\n  simple: boolean\n  finishStatus: string\n  processStatus: string\n}\n\nexport interface StepItemState {\n  uid: number\n  currentStatus: string\n  setIndex: (val: number) => void\n  calcProgress: (status: string) => void\n}\n\nexport interface IStepsInject {\n  props: IStepsProps\n  steps: Ref<StepItemState[]>\n  addStep: (item: StepItemState) => void\n  removeStep: (uid: number) => void\n}\n\ndefineOptions({\n  name: 'ElStep',\n})\n\nconst props = defineProps(stepProps)\nconst ns = useNamespace('step')\nconst index = ref(-1)\nconst lineStyle = ref({})\nconst internalStatus = ref('')\nconst parent = inject('ElSteps') as IStepsInject\nconst currentInstance = getCurrentInstance()\n\nonMounted(() => {\n  watch(\n    [\n      () => parent.props.active,\n      () => parent.props.processStatus,\n      () => parent.props.finishStatus,\n    ],\n    ([active]) => {\n      updateStatus(active)\n    },\n    { immediate: true }\n  )\n})\n\nonBeforeUnmount(() => {\n  parent.removeStep(stepItemState.uid)\n})\n\nconst currentStatus = computed(() => {\n  return props.status || internalStatus.value\n})\n\nconst prevStatus = computed(() => {\n  const prevStep = parent.steps.value[index.value - 1]\n  return prevStep ? prevStep.currentStatus : 'wait'\n})\n\nconst isCenter = computed(() => {\n  return parent.props.alignCenter\n})\n\nconst isVertical = computed(() => {\n  return parent.props.direction === 'vertical'\n})\n\nconst isSimple = computed(() => {\n  return parent.props.simple\n})\n\nconst stepsCount = computed(() => {\n  return parent.steps.value.length\n})\n\nconst isLast = computed(() => {\n  return parent.steps.value[stepsCount.value - 1]?.uid === currentInstance?.uid\n})\n\nconst space = computed(() => {\n  return isSimple.value ? '' : parent.props.space\n})\n\nconst containerKls = computed(() => {\n  return [\n    ns.b(),\n    ns.is(isSimple.value ? 'simple' : parent.props.direction),\n    ns.is('flex', isLast.value && !space.value && !isCenter.value),\n    ns.is('center', isCenter.value && !isVertical.value && !isSimple.value),\n  ]\n})\n\nconst style = computed(() => {\n  const style: CSSProperties = {\n    flexBasis: isNumber(space.value)\n      ? `${space.value}px`\n      : space.value\n      ? space.value\n      : `${100 / (stepsCount.value - (isCenter.value ? 0 : 1))}%`,\n  }\n  if (isVertical.value) return style\n  if (isLast.value) {\n    style.maxWidth = `${100 / stepsCount.value}%`\n  }\n  return style\n})\n\nconst setIndex = (val: number) => {\n  index.value = val\n}\n\nconst calcProgress = (status: string) => {\n  const isWait = status === 'wait'\n  const style: CSSProperties = {\n    transitionDelay: `${isWait ? '-' : ''}${150 * index.value}ms`,\n  }\n  const step = status === parent.props.processStatus || isWait ? 0 : 100\n\n  style.borderWidth = step && !isSimple.value ? '1px' : 0\n  style[parent.props.direction === 'vertical' ? 'height' : 'width'] = `${step}%`\n  lineStyle.value = style\n}\n\nconst updateStatus = (activeIndex: number) => {\n  if (activeIndex > index.value) {\n    internalStatus.value = parent.props.finishStatus\n  } else if (activeIndex === index.value && prevStatus.value !== 'error') {\n    internalStatus.value = parent.props.processStatus\n  } else {\n    internalStatus.value = 'wait'\n  }\n  const prevChild = parent.steps.value[index.value - 1]\n  if (prevChild) prevChild.calcProgress(internalStatus.value)\n}\n\nconst stepItemState = reactive({\n  uid: currentInstance!.uid,\n  currentStatus,\n  setIndex,\n  calcProgress,\n})\n\nparent.addStep(stepItemState)\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;mCAyFc,CAAA;AAAA,EACZ,IAAM,EAAA,QAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAGA,IAAM,MAAA,EAAA,GAAK,aAAa,MAAM,CAAA,CAAA;AAC9B,IAAM,MAAA,KAAA,GAAQ,IAAI,CAAE,CAAA,CAAA,CAAA;AACpB,IAAM,MAAA,SAAA,GAAY,GAAI,CAAA,EAAE,CAAA,CAAA;AACxB,IAAM,MAAA,cAAA,GAAiB,IAAI,EAAE,CAAA,CAAA;AAC7B,IAAM,MAAA,MAAA,GAAS,OAAO,SAAS,CAAA,CAAA;AAC/B,IAAA,MAAM,kBAAkB,kBAAmB,EAAA,CAAA;AAE3C,IAAA,SAAA,CAAU,MAAM;AACd,MACE,KAAA,CAAA;AAAA,QACE,MAAM,OAAO,KAAM,CAAA,MAAA;AAAA,QACnB,MAAM,OAAO,KAAM,CAAA,aAAA;AAAA,QACnB,MAAM,OAAO,KAAM,CAAA,YAAA;AAAA,OACrB,EACA,CAAC,CAAC,MAAY,CAAA,KAAA;AACZ,QAAA,YAAA,CAAa,MAAM,CAAA,CAAA;AAAA,OAErB,EAAA,EAAE,SAAW,EAAA,IAAA,EACf,CAAA,CAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAA,eAAA,CAAgB,MAAM;AACpB,MAAO,MAAA,CAAA,UAAA,CAAW,cAAc,GAAG,CAAA,CAAA;AAAA,KACpC,CAAA,CAAA;AAED,IAAM,MAAA,aAAA,GAAgB,SAAS,MAAM;AACnC,MAAO,OAAA,KAAA,CAAM,UAAU,cAAe,CAAA,KAAA,CAAA;AAAA,KACvC,CAAA,CAAA;AAED,IAAM,MAAA,UAAA,GAAa,SAAS,MAAM;AAChC,MAAA,MAAM,QAAW,GAAA,MAAA,CAAO,KAAM,CAAA,KAAA,CAAM,MAAM,KAAQ,GAAA,CAAA,CAAA,CAAA;AAClD,MAAO,OAAA,QAAA,GAAW,SAAS,aAAgB,GAAA,MAAA,CAAA;AAAA,KAC5C,CAAA,CAAA;AAED,IAAM,MAAA,QAAA,GAAW,SAAS,MAAM;AAC9B,MAAA,OAAO,OAAO,KAAM,CAAA,WAAA,CAAA;AAAA,KACrB,CAAA,CAAA;AAED,IAAM,MAAA,UAAA,GAAa,SAAS,MAAM;AAChC,MAAO,OAAA,MAAA,CAAO,MAAM,SAAc,KAAA,UAAA,CAAA;AAAA,KACnC,CAAA,CAAA;AAED,IAAM,MAAA,QAAA,GAAW,SAAS,MAAM;AAC9B,MAAA,OAAO,OAAO,KAAM,CAAA,MAAA,CAAA;AAAA,KACrB,CAAA,CAAA;AAED,IAAM,MAAA,UAAA,GAAa,SAAS,MAAM;AAChC,MAAO,OAAA,MAAA,CAAO,MAAM,KAAM,CAAA,MAAA,CAAA;AAAA,KAC3B,CAAA,CAAA;AAED,IAAM,MAAA,MAAA,GAAS,SAAS,MAAM;AAC5B,MAAA,IAAA,EAAA,CAAO;AAAmE,MAC3E,OAAA,CAAA,CAAA,EAAA,GAAA,MAAA,CAAA,KAAA,CAAA,KAAA,CAAA,UAAA,CAAA,KAAA,GAAA,CAAA,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,OAAA,eAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,eAAA,CAAA,GAAA,CAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAA,MAAA,KAAgB,GAAA,QAAA,CAAA,MAAa;AAAa,MAC3C,OAAA,QAAA,CAAA,KAAA,GAAA,EAAA,GAAA,MAAA,CAAA,KAAA,CAAA,KAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAO,MAAA,YAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MAAA,OACA;AAAA,QACL,GAAG,CAAG,EAAA;AAAkD,QACxD,EAAA,CAAG,EAAG,CAAA,QAAQ,CAAO,KAAA,GAAA,QAAU,GAAM,MAAA,CAAA,KAAS,CAAC,SAAS,CAAK;AAAA,QAC7D,EAAA,CAAG,EAAG,CAAA,MAAA,EAAA,MAAmB,CAAA,KAAA,IAAA,CAAA,KAAqB,CAAA,KAAA,IAAA,CAAA,QAAS,CAAC,KAAA,CAAA;AAAc,QACxE,EAAA,CAAA,EAAA,CAAA,QAAA,EAAA,QAAA,CAAA,KAAA,IAAA,CAAA,UAAA,CAAA,KAAA,IAAA,CAAA,QAAA,CAAA,KAAA,CAAA;AAAA,OACD,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAA,MAAA,KAA6B,GAAA,QAAA,CAAA,MAAA;AAAA,MAAA,eACP;AAImC,QACzD,SAAA,EAAA,QAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA,EAAA,KAAA,CAAA,KAAA,CAAA,EAAA,CAAA,GAAA,KAAA,CAAA,KAAA,GAAA,KAAA,CAAA,KAAA,GAAA,CAAA,EAAA,GAAA,IAAA,UAAA,CAAA,KAAA,IAAA,QAAA,CAAA,KAAA,GAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,OAAA,CAAA;AAAsB,MAAO,IAAA,UAAA,CAAA,KAAA;AAC7B,QAAA,aAAkB,CAAA;AAChB,MAAM,IAAA,MAAA,CAAA,KAAA,EAAA;AAA+B,QACvC,MAAA,CAAA,QAAA,GAAA,CAAA,EAAA,GAAA,GAAA,UAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AACA,OAAO;AAAA,MACR,OAAA,MAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAA,MAAA,QAAc,GAAA,CAAA,GAAA,KAAA;AAAA,MAChB,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAA,MAAA,YAA0B,GAAA,CAAA,MAAA,KAAA;AAC1B,MAAA,MAAM,MAAuB,GAAA,MAAA,KAAA,MAAA,CAAA;AAAA,MAAA;AACyB,QACtD,eAAA,EAAA,CAAA,EAAA,MAAA,GAAA,GAAA,GAAA,EAAA,CAAA,EAAA,GAAA,GAAA,KAAA,CAAA,KAAA,CAAA,EAAA,CAAA;AACA,OAAA,CAAA;AAEA,MAAA,MAAA,IAAoB,GAAA,MAAA,KAAA,MAAS,CAAA,KAAA,CAAA,aAAyB,IAAA,MAAA,GAAA,CAAA,GAAA,GAAA,CAAA;AACtD,MAAA,MAAA,CAAM,WAAa,GAAA,IAAA,IAAA,CAAA,QAA2B,CAAA,KAAA,GAAA,KAAA,GAAA,CAAA,CAAA;AAC9C,MAAA,MAAA,CAAA,MAAkB,CAAA,KAAA,CAAA,SAAA,KAAA,UAAA,GAAA,QAAA,GAAA,OAAA,CAAA,GAAA,CAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;AAAA,MACpB,SAAA,CAAA,KAAA,GAAA,MAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAI,MAAA,YAAA,eAA2B,KAAA;AAC7B,MAAe,IAAA,WAAA,GAAA,KAAA,CAAA;AAAqB,sBACX,CAAA,KAAA,GAAA,MAAA,CAAA,KAAe,CAAA,YAAA,CAAA;AACxC,OAAe,MAAA,IAAA,WAAA,UAAe,CAAM,KAAA,IAAA,UAAA,CAAA,KAAA,KAAA,OAAA,EAAA;AAAA,QAC/B,cAAA,CAAA,KAAA,GAAA,MAAA,CAAA,KAAA,CAAA,aAAA,CAAA;AACL,OAAA,MAAA;AAAuB,QACzB,cAAA,CAAA,KAAA,GAAA,MAAA,CAAA;AACA,OAAA;AACA,MAAI,MAAA,SAAA,GAAA,MAAA,CAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA,KAAA,GAAA,CAAA,CAAA,CAAA;AAAW,MAAU,IAAA,SAAA;AAAiC,QAC5D,SAAA,CAAA,YAAA,CAAA,cAAA,CAAA,KAAA,CAAA,CAAA;AAEA,KAAA,CAAA;AAA+B,IAAA,mBACP,GAAA,QAAA,CAAA;AAAA,MACtB,GAAA,EAAA,eAAA,CAAA,GAAA;AAAA,MACA,aAAA;AAAA,MACA,QAAA;AAAA,MACD,YAAA;AAED,KAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}