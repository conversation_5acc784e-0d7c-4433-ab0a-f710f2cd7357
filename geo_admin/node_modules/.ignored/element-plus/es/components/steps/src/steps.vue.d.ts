import type { StepItemState } from './item.vue';
declare const _default: import("vue").DefineComponent<{
    readonly space: import("../../../utils").EpPropFinalized<readonly [NumberConstructor, StringConstructor], unknown, unknown, "", boolean>;
    readonly active: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
    readonly direction: import("../../../utils").EpPropFinalized<StringConstructor, "horizontal" | "vertical", unknown, "horizontal", boolean>;
    readonly alignCenter: {
        readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly simple: {
        readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly finishStatus: import("../../../utils").EpPropFinalized<StringConstructor, "success" | "error" | "wait" | "finish" | "process", unknown, "finish", boolean>;
    readonly processStatus: import("../../../utils").EpPropFinalized<StringConstructor, "success" | "error" | "wait" | "finish" | "process", unknown, "process", boolean>;
}, {
    props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
        readonly space: import("../../../utils").EpPropFinalized<readonly [NumberConstructor, StringConstructor], unknown, unknown, "", boolean>;
        readonly active: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
        readonly direction: import("../../../utils").EpPropFinalized<StringConstructor, "horizontal" | "vertical", unknown, "horizontal", boolean>;
        readonly alignCenter: {
            readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly simple: {
            readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly finishStatus: import("../../../utils").EpPropFinalized<StringConstructor, "success" | "error" | "wait" | "finish" | "process", unknown, "finish", boolean>;
        readonly processStatus: import("../../../utils").EpPropFinalized<StringConstructor, "success" | "error" | "wait" | "finish" | "process", unknown, "process", boolean>;
    }>> & {
        onChange?: ((newVal: number, oldVal: number) => any) | undefined;
    }>>;
    emit: (event: "change", newVal: number, oldVal: number) => void;
    ns: {
        namespace: import("vue").ComputedRef<string>;
        b: (blockSuffix?: string) => string;
        e: (element?: string | undefined) => string;
        m: (modifier?: string | undefined) => string;
        be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
        em: (element?: string | undefined, modifier?: string | undefined) => string;
        bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
        bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
        is: {
            (name: string, state: boolean | undefined): string;
            (name: string): string;
        };
        cssVar: (object: Record<string, string>) => Record<string, string>;
        cssVarName: (name: string) => string;
        cssVarBlock: (object: Record<string, string>) => Record<string, string>;
        cssVarBlockName: (name: string) => string;
    };
    steps: import("vue").ShallowRef<StepItemState[]>;
    addStep: (child: StepItemState) => void;
    removeStep: (uid: number) => void;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    change: (newVal: number, oldVal: number) => boolean;
}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    readonly space: import("../../../utils").EpPropFinalized<readonly [NumberConstructor, StringConstructor], unknown, unknown, "", boolean>;
    readonly active: import("../../../utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
    readonly direction: import("../../../utils").EpPropFinalized<StringConstructor, "horizontal" | "vertical", unknown, "horizontal", boolean>;
    readonly alignCenter: {
        readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly simple: {
        readonly type: import("vue").PropType<import("../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly finishStatus: import("../../../utils").EpPropFinalized<StringConstructor, "success" | "error" | "wait" | "finish" | "process", unknown, "finish", boolean>;
    readonly processStatus: import("../../../utils").EpPropFinalized<StringConstructor, "success" | "error" | "wait" | "finish" | "process", unknown, "process", boolean>;
}>> & {
    onChange?: ((newVal: number, oldVal: number) => any) | undefined;
}, {
    readonly space: import("../../../utils").EpPropMergeType<readonly [NumberConstructor, StringConstructor], unknown, unknown>;
    readonly active: number;
    readonly direction: import("../../../utils").EpPropMergeType<StringConstructor, "horizontal" | "vertical", unknown>;
    readonly finishStatus: import("../../../utils").EpPropMergeType<StringConstructor, "success" | "error" | "wait" | "finish" | "process", unknown>;
    readonly processStatus: import("../../../utils").EpPropMergeType<StringConstructor, "success" | "error" | "wait" | "finish" | "process", unknown>;
}>;
export default _default;
