{"version": 3, "file": "teleport2.mjs", "sources": ["../../../../../../packages/components/teleport/src/teleport.vue"], "sourcesContent": ["<template>\n  <teleport v-if=\"container\" :to=\"container\" :disabled=\"disabled\">\n    <div ref=\"containerRef\" :class=\"ns.b()\" :style=\"containerStyle\">\n      <slot />\n    </div>\n  </teleport>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, ref } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { teleportProps } from './teleport'\nimport type { StyleValue } from 'vue'\n\nconst props = defineProps(teleportProps)\n\nconst ns = useNamespace('teleport')\nconst containerRef = ref<HTMLElement>()\n\nconst containerStyle = computed<StyleValue>(() => {\n  return props.container === 'body'\n    ? [\n        props.style,\n        {\n          position: 'absolute',\n          top: `0px`,\n          left: `0px`,\n          zIndex: props.zIndex,\n        },\n      ]\n    : {}\n})\n\ndefineExpose({\n  /** @description container element */\n  containerRef,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;AAgBA,IAAM,MAAA,EAAA,GAAK,aAAa,UAAU,CAAA,CAAA;AAClC,IAAA,MAAM,eAAe,GAAiB,EAAA,CAAA;AAEtC,IAAM,MAAA,cAAA,GAAiB,SAAqB,MAAM;AAChD,MAAO,OAAA,KAAA,CAAM,cAAc,MACvB,GAAA;AAAA,QACE,KAAM,CAAA,KAAA;AAAA,QACN;AAAA,UACE,QAAU,EAAA,UAAA;AAAA,UACV,GAAK,EAAA,CAAA,GAAA,CAAA;AAAA,UACL,IAAM,EAAA,CAAA,GAAA,CAAA;AAAA,UACN,QAAQ,KAAM,CAAA,MAAA;AAAA,SAChB;AAAA,UAEF,EAAC,CAAA;AAAA,KACN,CAAA,CAAA;AAED,IAAa,MAAA,CAAA;AAAA,MAEX,YAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;"}