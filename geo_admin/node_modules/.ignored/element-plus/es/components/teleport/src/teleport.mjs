import '../../../utils/index.mjs';
import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';

const teleportProps = buildProps({
  container: {
    type: definePropType(String),
    default: "body"
  },
  disabled: {
    type: Boolean,
    default: false
  },
  style: {
    type: definePropType([String, Array, Object])
  },
  zIndex: {
    type: String,
    default: "2000"
  }
});

export { teleportProps };
//# sourceMappingURL=teleport.mjs.map
