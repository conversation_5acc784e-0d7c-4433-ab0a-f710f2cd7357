export declare const ElUpload: import("element-plus/es/utils").SFCWithInstall<import("vue").DefineComponent<{
    readonly beforeUpload: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
        (): (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
        (): (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly beforeRemove: {
        readonly type: import("vue").PropType<(uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => import("element-plus/es/utils").Awaitable<boolean>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly onRemove: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
        (): (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
        (): (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly onChange: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
        (): (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
        (): (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly onPreview: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (uploadFile: import("./src/upload").UploadFile) => void) | (() => (uploadFile: import("./src/upload").UploadFile) => void) | {
        (): (uploadFile: import("./src/upload").UploadFile) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (uploadFile: import("./src/upload").UploadFile) => void) | (() => (uploadFile: import("./src/upload").UploadFile) => void) | {
        (): (uploadFile: import("./src/upload").UploadFile) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly onSuccess: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (response: any, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (response: any, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
        (): (response: any, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (response: any, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (response: any, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
        (): (response: any, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly onProgress: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (evt: import("./src/upload").UploadProgressEvent, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (evt: import("./src/upload").UploadProgressEvent, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
        (): (evt: import("./src/upload").UploadProgressEvent, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (evt: import("./src/upload").UploadProgressEvent, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (evt: import("./src/upload").UploadProgressEvent, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
        (): (evt: import("./src/upload").UploadProgressEvent, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly onError: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (error: Error, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (error: Error, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
        (): (error: Error, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (error: Error, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (error: Error, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
        (): (error: Error, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly onExceed: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | {
        (): (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | {
        (): (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly action: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "#", boolean>;
    readonly headers: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers) | ((new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers))[], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly method: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "post", boolean>;
    readonly data: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | ((new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
    readonly multiple: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly name: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "file", boolean>;
    readonly drag: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly withCredentials: BooleanConstructor;
    readonly showFileList: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
    readonly accept: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
    readonly fileList: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/upload").UploadUserFile[]) | (() => import("./src/upload").UploadUserFile[]) | ((new (...args: any[]) => import("./src/upload").UploadUserFile[]) | (() => import("./src/upload").UploadUserFile[]))[], unknown, unknown, () => [], boolean>;
    readonly autoUpload: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
    readonly listType: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
    readonly httpRequest: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/upload").UploadRequestHandler) | (() => import("./src/upload").UploadRequestHandler) | {
        (): import("./src/upload").UploadRequestHandler;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => import("./src/upload").UploadRequestHandler) | (() => import("./src/upload").UploadRequestHandler) | {
        (): import("./src/upload").UploadRequestHandler;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, import("./src/upload").UploadRequestHandler, boolean>;
    readonly disabled: BooleanConstructor;
    readonly limit: NumberConstructor;
}, {
    props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
        readonly beforeUpload: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
            (): (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
            (): (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly beforeRemove: {
            readonly type: import("vue").PropType<(uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => import("element-plus/es/utils").Awaitable<boolean>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly onRemove: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
            (): (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
            (): (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onChange: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
            (): (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
            (): (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onPreview: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (uploadFile: import("./src/upload").UploadFile) => void) | (() => (uploadFile: import("./src/upload").UploadFile) => void) | {
            (): (uploadFile: import("./src/upload").UploadFile) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (uploadFile: import("./src/upload").UploadFile) => void) | (() => (uploadFile: import("./src/upload").UploadFile) => void) | {
            (): (uploadFile: import("./src/upload").UploadFile) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onSuccess: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (response: any, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (response: any, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
            (): (response: any, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (response: any, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (response: any, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
            (): (response: any, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onProgress: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (evt: import("./src/upload").UploadProgressEvent, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (evt: import("./src/upload").UploadProgressEvent, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
            (): (evt: import("./src/upload").UploadProgressEvent, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (evt: import("./src/upload").UploadProgressEvent, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (evt: import("./src/upload").UploadProgressEvent, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
            (): (evt: import("./src/upload").UploadProgressEvent, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onError: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (error: Error, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (error: Error, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
            (): (error: Error, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (error: Error, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (error: Error, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
            (): (error: Error, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onExceed: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | {
            (): (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | {
            (): (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly action: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "#", boolean>;
        readonly headers: {
            readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers) | ((new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers))[], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly method: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "post", boolean>;
        readonly data: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | ((new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
        readonly multiple: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly name: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "file", boolean>;
        readonly drag: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly withCredentials: BooleanConstructor;
        readonly showFileList: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly accept: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
        readonly fileList: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/upload").UploadUserFile[]) | (() => import("./src/upload").UploadUserFile[]) | ((new (...args: any[]) => import("./src/upload").UploadUserFile[]) | (() => import("./src/upload").UploadUserFile[]))[], unknown, unknown, () => [], boolean>;
        readonly autoUpload: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly listType: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
        readonly httpRequest: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/upload").UploadRequestHandler) | (() => import("./src/upload").UploadRequestHandler) | {
            (): import("./src/upload").UploadRequestHandler;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => import("./src/upload").UploadRequestHandler) | (() => import("./src/upload").UploadRequestHandler) | {
            (): import("./src/upload").UploadRequestHandler;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, import("./src/upload").UploadRequestHandler, boolean>;
        readonly disabled: BooleanConstructor;
        readonly limit: NumberConstructor;
    }>> & {
        [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
    }>>;
    disabled: import("vue").ComputedRef<boolean>;
    uploadRef: import("vue").ShallowRef<({
        $: import("vue").ComponentInternalInstance;
        $data: {};
        $props: Partial<{
            readonly disabled: boolean;
            readonly name: string;
            readonly drag: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly multiple: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly data: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | ((new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>))[], unknown, unknown>;
            readonly onError: (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void;
            readonly onProgress: (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void;
            readonly action: string;
            readonly method: string;
            readonly showFileList: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly accept: string;
            readonly fileList: import("./src/upload").UploadUserFile[];
            readonly autoUpload: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly listType: import("element-plus/es/utils").EpPropMergeType<StringConstructor, "text" | "picture" | "picture-card", unknown>;
            readonly httpRequest: import("./src/upload").UploadRequestHandler;
            readonly withCredentials: boolean;
            readonly beforeUpload: (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>;
            readonly onRemove: (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void;
            readonly onSuccess: (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown;
            readonly onExceed: (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void;
            readonly onStart: (rawFile: import("./src/upload").UploadRawFile) => void;
        }> & Omit<Readonly<import("vue").ExtractPropTypes<{
            readonly beforeUpload: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
                (): (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
                (): (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onRemove: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | (() => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | {
                (): (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | (() => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | {
                (): (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onStart: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => void) | (() => (rawFile: import("./src/upload").UploadRawFile) => void) | {
                (): (rawFile: import("./src/upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => void) | (() => (rawFile: import("./src/upload").UploadRawFile) => void) | {
                (): (rawFile: import("./src/upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onSuccess: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | {
                (): (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | {
                (): (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onProgress: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | {
                (): (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | {
                (): (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onError: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | {
                (): (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | {
                (): (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onExceed: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | {
                (): (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | {
                (): (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly action: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "#", boolean>;
            readonly headers: {
                readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers) | ((new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers))[], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly method: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "post", boolean>;
            readonly data: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | ((new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
            readonly multiple: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly name: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "file", boolean>;
            readonly drag: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly withCredentials: BooleanConstructor;
            readonly showFileList: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly accept: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
            readonly fileList: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/upload").UploadUserFile[]) | (() => import("./src/upload").UploadUserFile[]) | ((new (...args: any[]) => import("./src/upload").UploadUserFile[]) | (() => import("./src/upload").UploadUserFile[]))[], unknown, unknown, () => [], boolean>;
            readonly autoUpload: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly listType: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
            readonly httpRequest: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/upload").UploadRequestHandler) | (() => import("./src/upload").UploadRequestHandler) | {
                (): import("./src/upload").UploadRequestHandler;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => import("./src/upload").UploadRequestHandler) | (() => import("./src/upload").UploadRequestHandler) | {
                (): import("./src/upload").UploadRequestHandler;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, import("./src/upload").UploadRequestHandler, boolean>;
            readonly disabled: BooleanConstructor;
            readonly limit: NumberConstructor;
        }>> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, "disabled" | "name" | "drag" | "multiple" | "data" | "onError" | "onProgress" | "action" | "method" | "showFileList" | "accept" | "fileList" | "autoUpload" | "listType" | "httpRequest" | "withCredentials" | "beforeUpload" | "onRemove" | "onSuccess" | "onExceed" | "onStart">;
        $attrs: {
            [x: string]: unknown;
        };
        $refs: {
            [x: string]: unknown;
        };
        $slots: Readonly<{
            [name: string]: import("vue").Slot | undefined;
        }>;
        $root: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null;
        $parent: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null;
        $emit: (event: string, ...args: any[]) => void;
        $el: any;
        $options: import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
            readonly beforeUpload: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
                (): (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
                (): (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onRemove: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | (() => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | {
                (): (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | (() => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | {
                (): (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onStart: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => void) | (() => (rawFile: import("./src/upload").UploadRawFile) => void) | {
                (): (rawFile: import("./src/upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => void) | (() => (rawFile: import("./src/upload").UploadRawFile) => void) | {
                (): (rawFile: import("./src/upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onSuccess: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | {
                (): (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | {
                (): (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onProgress: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | {
                (): (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | {
                (): (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onError: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | {
                (): (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | {
                (): (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onExceed: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | {
                (): (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | {
                (): (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly action: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "#", boolean>;
            readonly headers: {
                readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers) | ((new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers))[], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly method: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "post", boolean>;
            readonly data: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | ((new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
            readonly multiple: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly name: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "file", boolean>;
            readonly drag: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly withCredentials: BooleanConstructor;
            readonly showFileList: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly accept: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
            readonly fileList: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/upload").UploadUserFile[]) | (() => import("./src/upload").UploadUserFile[]) | ((new (...args: any[]) => import("./src/upload").UploadUserFile[]) | (() => import("./src/upload").UploadUserFile[]))[], unknown, unknown, () => [], boolean>;
            readonly autoUpload: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly listType: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
            readonly httpRequest: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/upload").UploadRequestHandler) | (() => import("./src/upload").UploadRequestHandler) | {
                (): import("./src/upload").UploadRequestHandler;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => import("./src/upload").UploadRequestHandler) | (() => import("./src/upload").UploadRequestHandler) | {
                (): import("./src/upload").UploadRequestHandler;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, import("./src/upload").UploadRequestHandler, boolean>;
            readonly disabled: BooleanConstructor;
            readonly limit: NumberConstructor;
        }>>, {
            props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
                readonly beforeUpload: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
                    (): (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>;
                    new (): any;
                    readonly prototype: any;
                } | ((new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
                    (): (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>;
                    new (): any;
                    readonly prototype: any;
                })[], unknown, unknown, () => void, boolean>;
                readonly onRemove: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | (() => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | {
                    (): (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void;
                    new (): any;
                    readonly prototype: any;
                } | ((new (...args: any[]) => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | (() => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | {
                    (): (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void;
                    new (): any;
                    readonly prototype: any;
                })[], unknown, unknown, () => void, boolean>;
                readonly onStart: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => void) | (() => (rawFile: import("./src/upload").UploadRawFile) => void) | {
                    (): (rawFile: import("./src/upload").UploadRawFile) => void;
                    new (): any;
                    readonly prototype: any;
                } | ((new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => void) | (() => (rawFile: import("./src/upload").UploadRawFile) => void) | {
                    (): (rawFile: import("./src/upload").UploadRawFile) => void;
                    new (): any;
                    readonly prototype: any;
                })[], unknown, unknown, () => void, boolean>;
                readonly onSuccess: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | {
                    (): (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown;
                    new (): any;
                    readonly prototype: any;
                } | ((new (...args: any[]) => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | {
                    (): (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown;
                    new (): any;
                    readonly prototype: any;
                })[], unknown, unknown, () => void, boolean>;
                readonly onProgress: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | {
                    (): (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void;
                    new (): any;
                    readonly prototype: any;
                } | ((new (...args: any[]) => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | {
                    (): (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void;
                    new (): any;
                    readonly prototype: any;
                })[], unknown, unknown, () => void, boolean>;
                readonly onError: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | {
                    (): (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void;
                    new (): any;
                    readonly prototype: any;
                } | ((new (...args: any[]) => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | {
                    (): (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void;
                    new (): any;
                    readonly prototype: any;
                })[], unknown, unknown, () => void, boolean>;
                readonly onExceed: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | {
                    (): (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void;
                    new (): any;
                    readonly prototype: any;
                } | ((new (...args: any[]) => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | {
                    (): (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void;
                    new (): any;
                    readonly prototype: any;
                })[], unknown, unknown, () => void, boolean>;
                readonly action: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "#", boolean>;
                readonly headers: {
                    readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers) | ((new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers))[], unknown, unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly method: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "post", boolean>;
                readonly data: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | ((new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
                readonly multiple: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
                readonly name: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "file", boolean>;
                readonly drag: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
                readonly withCredentials: BooleanConstructor;
                readonly showFileList: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
                readonly accept: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
                readonly fileList: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/upload").UploadUserFile[]) | (() => import("./src/upload").UploadUserFile[]) | ((new (...args: any[]) => import("./src/upload").UploadUserFile[]) | (() => import("./src/upload").UploadUserFile[]))[], unknown, unknown, () => [], boolean>;
                readonly autoUpload: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
                readonly listType: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
                readonly httpRequest: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/upload").UploadRequestHandler) | (() => import("./src/upload").UploadRequestHandler) | {
                    (): import("./src/upload").UploadRequestHandler;
                    new (): any;
                    readonly prototype: any;
                } | ((new (...args: any[]) => import("./src/upload").UploadRequestHandler) | (() => import("./src/upload").UploadRequestHandler) | {
                    (): import("./src/upload").UploadRequestHandler;
                    new (): any;
                    readonly prototype: any;
                })[], unknown, unknown, import("./src/upload").UploadRequestHandler, boolean>;
                readonly disabled: BooleanConstructor;
                readonly limit: NumberConstructor;
            }>> & {
                [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
            }>>;
            ns: {
                namespace: import("vue").ComputedRef<string>;
                b: (blockSuffix?: string) => string;
                e: (element?: string | undefined) => string;
                m: (modifier?: string | undefined) => string;
                be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                em: (element?: string | undefined, modifier?: string | undefined) => string;
                bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                is: {
                    (name: string, state: boolean | undefined): string;
                    (name: string): string;
                };
                cssVar: (object: Record<string, string>) => Record<string, string>;
                cssVarName: (name: string) => string;
                cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                cssVarBlockName: (name: string) => string;
            };
            disabled: import("vue").ComputedRef<boolean>;
            requests: import("vue").ShallowRef<Record<string, XMLHttpRequest | Promise<unknown>>>;
            inputRef: import("vue").ShallowRef<HTMLInputElement | undefined>;
            uploadFiles: (files: File[]) => void;
            upload: (rawFile: import("./src/upload").UploadRawFile) => Promise<void>;
            resolveData: (data: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | ((new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>))[], unknown, unknown>, rawFile: import("./src/upload").UploadRawFile) => Promise<Record<string, any>>;
            doUpload: (rawFile: import("./src/upload").UploadRawFile, beforeData?: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | ((new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>))[], unknown, unknown> | undefined) => Promise<void>;
            handleChange: (e: Event) => void;
            handleClick: () => void;
            handleKeydown: () => void;
            abort: (file?: import("./src/upload").UploadFile | undefined) => void;
            UploadDragger: import("vue").DefineComponent<{
                readonly disabled: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            }, {
                COMPONENT_NAME: string;
                emit: (event: "file", file: File[]) => void;
                uploaderContext: import("./src/constants").UploadContext;
                ns: {
                    namespace: import("vue").ComputedRef<string>;
                    b: (blockSuffix?: string) => string;
                    e: (element?: string | undefined) => string;
                    m: (modifier?: string | undefined) => string;
                    be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                    em: (element?: string | undefined, modifier?: string | undefined) => string;
                    bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                    bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                    is: {
                        (name: string, state: boolean | undefined): string;
                        (name: string): string;
                    };
                    cssVar: (object: Record<string, string>) => Record<string, string>;
                    cssVarName: (name: string) => string;
                    cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                    cssVarBlockName: (name: string) => string;
                };
                dragover: import("vue").Ref<boolean>;
                disabled: import("vue").ComputedRef<boolean>;
                onDrop: (e: DragEvent) => void;
                onDragover: () => void;
            }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
                file: (file: File[]) => boolean;
            }, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
                readonly disabled: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            }>> & {
                onFile?: ((file: File[]) => any) | undefined;
            }, {
                readonly disabled: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
            }>;
        }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, {
            readonly disabled: boolean;
            readonly name: string;
            readonly drag: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly multiple: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly data: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | ((new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>))[], unknown, unknown>;
            readonly onError: (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void;
            readonly onProgress: (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void;
            readonly action: string;
            readonly method: string;
            readonly showFileList: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly accept: string;
            readonly fileList: import("./src/upload").UploadUserFile[];
            readonly autoUpload: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly listType: import("element-plus/es/utils").EpPropMergeType<StringConstructor, "text" | "picture" | "picture-card", unknown>;
            readonly httpRequest: import("./src/upload").UploadRequestHandler;
            readonly withCredentials: boolean;
            readonly beforeUpload: (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>;
            readonly onRemove: (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void;
            readonly onSuccess: (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown;
            readonly onExceed: (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void;
            readonly onStart: (rawFile: import("./src/upload").UploadRawFile) => void;
        }> & {
            beforeCreate?: ((() => void) | (() => void)[]) | undefined;
            created?: ((() => void) | (() => void)[]) | undefined;
            beforeMount?: ((() => void) | (() => void)[]) | undefined;
            mounted?: ((() => void) | (() => void)[]) | undefined;
            beforeUpdate?: ((() => void) | (() => void)[]) | undefined;
            updated?: ((() => void) | (() => void)[]) | undefined;
            activated?: ((() => void) | (() => void)[]) | undefined;
            deactivated?: ((() => void) | (() => void)[]) | undefined;
            beforeDestroy?: ((() => void) | (() => void)[]) | undefined;
            beforeUnmount?: ((() => void) | (() => void)[]) | undefined;
            destroyed?: ((() => void) | (() => void)[]) | undefined;
            unmounted?: ((() => void) | (() => void)[]) | undefined;
            renderTracked?: (((e: import("vue").DebuggerEvent) => void) | ((e: import("vue").DebuggerEvent) => void)[]) | undefined;
            renderTriggered?: (((e: import("vue").DebuggerEvent) => void) | ((e: import("vue").DebuggerEvent) => void)[]) | undefined;
            errorCaptured?: (((err: unknown, instance: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null, info: string) => boolean | void) | ((err: unknown, instance: import("vue").ComponentPublicInstance<{}, {}, {}, {}, {}, {}, {}, {}, false, import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}>> | null, info: string) => boolean | void)[]) | undefined;
        };
        $forceUpdate: () => void;
        $nextTick: typeof import("vue").nextTick;
        $watch(source: string | Function, cb: Function, options?: import("vue").WatchOptions<boolean> | undefined): import("vue").WatchStopHandle;
    } & Readonly<import("vue").ExtractPropTypes<{
        readonly beforeUpload: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
            (): (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
            (): (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onRemove: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | (() => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | {
            (): (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | (() => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | {
            (): (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onStart: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => void) | (() => (rawFile: import("./src/upload").UploadRawFile) => void) | {
            (): (rawFile: import("./src/upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => void) | (() => (rawFile: import("./src/upload").UploadRawFile) => void) | {
            (): (rawFile: import("./src/upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onSuccess: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | {
            (): (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | {
            (): (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onProgress: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | {
            (): (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | {
            (): (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onError: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | {
            (): (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | {
            (): (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onExceed: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | {
            (): (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | {
            (): (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly action: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "#", boolean>;
        readonly headers: {
            readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers) | ((new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers))[], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly method: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "post", boolean>;
        readonly data: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | ((new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
        readonly multiple: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly name: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "file", boolean>;
        readonly drag: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly withCredentials: BooleanConstructor;
        readonly showFileList: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly accept: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
        readonly fileList: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/upload").UploadUserFile[]) | (() => import("./src/upload").UploadUserFile[]) | ((new (...args: any[]) => import("./src/upload").UploadUserFile[]) | (() => import("./src/upload").UploadUserFile[]))[], unknown, unknown, () => [], boolean>;
        readonly autoUpload: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly listType: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
        readonly httpRequest: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/upload").UploadRequestHandler) | (() => import("./src/upload").UploadRequestHandler) | {
            (): import("./src/upload").UploadRequestHandler;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => import("./src/upload").UploadRequestHandler) | (() => import("./src/upload").UploadRequestHandler) | {
            (): import("./src/upload").UploadRequestHandler;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, import("./src/upload").UploadRequestHandler, boolean>;
        readonly disabled: BooleanConstructor;
        readonly limit: NumberConstructor;
    }>> & import("vue").ShallowUnwrapRef<{
        props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
            readonly beforeUpload: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
                (): (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
                (): (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onRemove: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | (() => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | {
                (): (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | (() => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | {
                (): (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onStart: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => void) | (() => (rawFile: import("./src/upload").UploadRawFile) => void) | {
                (): (rawFile: import("./src/upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => void) | (() => (rawFile: import("./src/upload").UploadRawFile) => void) | {
                (): (rawFile: import("./src/upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onSuccess: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | {
                (): (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | {
                (): (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onProgress: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | {
                (): (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | {
                (): (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onError: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | {
                (): (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | {
                (): (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onExceed: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | {
                (): (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | {
                (): (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly action: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "#", boolean>;
            readonly headers: {
                readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers) | ((new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers))[], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly method: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "post", boolean>;
            readonly data: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | ((new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
            readonly multiple: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly name: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "file", boolean>;
            readonly drag: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly withCredentials: BooleanConstructor;
            readonly showFileList: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly accept: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
            readonly fileList: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/upload").UploadUserFile[]) | (() => import("./src/upload").UploadUserFile[]) | ((new (...args: any[]) => import("./src/upload").UploadUserFile[]) | (() => import("./src/upload").UploadUserFile[]))[], unknown, unknown, () => [], boolean>;
            readonly autoUpload: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly listType: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
            readonly httpRequest: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/upload").UploadRequestHandler) | (() => import("./src/upload").UploadRequestHandler) | {
                (): import("./src/upload").UploadRequestHandler;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => import("./src/upload").UploadRequestHandler) | (() => import("./src/upload").UploadRequestHandler) | {
                (): import("./src/upload").UploadRequestHandler;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, import("./src/upload").UploadRequestHandler, boolean>;
            readonly disabled: BooleanConstructor;
            readonly limit: NumberConstructor;
        }>> & {
            [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
        }>>;
        ns: {
            namespace: import("vue").ComputedRef<string>;
            b: (blockSuffix?: string) => string;
            e: (element?: string | undefined) => string;
            m: (modifier?: string | undefined) => string;
            be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
            em: (element?: string | undefined, modifier?: string | undefined) => string;
            bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
            bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        disabled: import("vue").ComputedRef<boolean>;
        requests: import("vue").ShallowRef<Record<string, XMLHttpRequest | Promise<unknown>>>;
        inputRef: import("vue").ShallowRef<HTMLInputElement | undefined>;
        uploadFiles: (files: File[]) => void;
        upload: (rawFile: import("./src/upload").UploadRawFile) => Promise<void>;
        resolveData: (data: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | ((new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>))[], unknown, unknown>, rawFile: import("./src/upload").UploadRawFile) => Promise<Record<string, any>>;
        doUpload: (rawFile: import("./src/upload").UploadRawFile, beforeData?: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | ((new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>))[], unknown, unknown> | undefined) => Promise<void>;
        handleChange: (e: Event) => void;
        handleClick: () => void;
        handleKeydown: () => void;
        abort: (file?: import("./src/upload").UploadFile | undefined) => void;
        UploadDragger: import("vue").DefineComponent<{
            readonly disabled: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        }, {
            COMPONENT_NAME: string;
            emit: (event: "file", file: File[]) => void;
            uploaderContext: import("./src/constants").UploadContext;
            ns: {
                namespace: import("vue").ComputedRef<string>;
                b: (blockSuffix?: string) => string;
                e: (element?: string | undefined) => string;
                m: (modifier?: string | undefined) => string;
                be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                em: (element?: string | undefined, modifier?: string | undefined) => string;
                bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                is: {
                    (name: string, state: boolean | undefined): string;
                    (name: string): string;
                };
                cssVar: (object: Record<string, string>) => Record<string, string>;
                cssVarName: (name: string) => string;
                cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                cssVarBlockName: (name: string) => string;
            };
            dragover: import("vue").Ref<boolean>;
            disabled: import("vue").ComputedRef<boolean>;
            onDrop: (e: DragEvent) => void;
            onDragover: () => void;
        }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
            file: (file: File[]) => boolean;
        }, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
            readonly disabled: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        }>> & {
            onFile?: ((file: File[]) => any) | undefined;
        }, {
            readonly disabled: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        }>;
    }> & {} & import("vue").ComponentCustomProperties) | undefined>;
    abort: (file: import("./src/upload").UploadFile) => void;
    submit: () => void;
    clearFiles: (states?: import("./src/upload").UploadStatus[]) => void;
    uploadFiles: import("vue").Ref<{
        name: string;
        percentage?: number | undefined;
        status: import("./src/upload").UploadStatus;
        size?: number | undefined;
        response?: unknown;
        uid: number;
        url?: string | undefined;
        raw?: {
            uid: number;
            readonly lastModified: number;
            readonly name: string;
            readonly webkitRelativePath: string;
            readonly size: number;
            readonly type: string;
            arrayBuffer: () => Promise<ArrayBuffer>;
            slice: (start?: number | undefined, end?: number | undefined, contentType?: string | undefined) => Blob;
            stream: () => ReadableStream<any>;
            text: () => Promise<string>;
        } | undefined;
    }[]> | import("vue").WritableComputedRef<import("./src/upload").UploadFiles>;
    handleStart: (rawFile: import("./src/upload").UploadRawFile) => void;
    handleError: (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void;
    handleRemove: (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void;
    handleSuccess: (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown;
    handleProgress: (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void;
    revokeFileObjectURL: (file: import("./src/upload").UploadFile) => void;
    isPictureCard: import("vue").ComputedRef<boolean>;
    uploadContentProps: import("vue").ComputedRef<import("./src/upload-content").UploadContentProps>;
    UploadList: import("vue").DefineComponent<{
        readonly files: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/upload").UploadFiles) | (() => import("./src/upload").UploadFiles) | ((new (...args: any[]) => import("./src/upload").UploadFiles) | (() => import("./src/upload").UploadFiles))[], unknown, unknown, () => never[], boolean>;
        readonly disabled: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly handlePreview: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (uploadFile: import("./src/upload").UploadFile) => void) | (() => (uploadFile: import("./src/upload").UploadFile) => void) | {
            (): (uploadFile: import("./src/upload").UploadFile) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (uploadFile: import("./src/upload").UploadFile) => void) | (() => (uploadFile: import("./src/upload").UploadFile) => void) | {
            (): (uploadFile: import("./src/upload").UploadFile) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly listType: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
    }, {
        props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
            readonly files: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/upload").UploadFiles) | (() => import("./src/upload").UploadFiles) | ((new (...args: any[]) => import("./src/upload").UploadFiles) | (() => import("./src/upload").UploadFiles))[], unknown, unknown, () => never[], boolean>;
            readonly disabled: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly handlePreview: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (uploadFile: import("./src/upload").UploadFile) => void) | (() => (uploadFile: import("./src/upload").UploadFile) => void) | {
                (): (uploadFile: import("./src/upload").UploadFile) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (uploadFile: import("./src/upload").UploadFile) => void) | (() => (uploadFile: import("./src/upload").UploadFile) => void) | {
                (): (uploadFile: import("./src/upload").UploadFile) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly listType: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
        }>> & {
            onRemove?: ((file: import("./src/upload").UploadFile) => any) | undefined;
        }>>;
        emit: (event: "remove", file: import("./src/upload").UploadFile) => void;
        t: import("../..").Translator;
        nsUpload: {
            namespace: import("vue").ComputedRef<string>;
            b: (blockSuffix?: string) => string;
            e: (element?: string | undefined) => string;
            m: (modifier?: string | undefined) => string;
            be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
            em: (element?: string | undefined, modifier?: string | undefined) => string;
            bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
            bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        nsIcon: {
            namespace: import("vue").ComputedRef<string>;
            b: (blockSuffix?: string) => string;
            e: (element?: string | undefined) => string;
            m: (modifier?: string | undefined) => string;
            be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
            em: (element?: string | undefined, modifier?: string | undefined) => string;
            bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
            bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        nsList: {
            namespace: import("vue").ComputedRef<string>;
            b: (blockSuffix?: string) => string;
            e: (element?: string | undefined) => string;
            m: (modifier?: string | undefined) => string;
            be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
            em: (element?: string | undefined, modifier?: string | undefined) => string;
            bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
            bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        disabled: import("vue").ComputedRef<boolean>;
        focusing: import("vue").Ref<boolean>;
        containerKls: import("vue").ComputedRef<string[]>;
        handleRemove: (file: import("./src/upload").UploadFile) => void;
        ElIcon: import("element-plus/es/utils").SFCWithInstall<import("vue").DefineComponent<{
            readonly size: {
                readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly color: {
                readonly type: import("vue").PropType<string>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
        }, {
            props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
                readonly size: {
                    readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly color: {
                    readonly type: import("vue").PropType<string>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
            }>> & {
                [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
            }>>;
            ns: {
                namespace: import("vue").ComputedRef<string>;
                b: (blockSuffix?: string) => string;
                e: (element?: string | undefined) => string;
                m: (modifier?: string | undefined) => string;
                be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                em: (element?: string | undefined, modifier?: string | undefined) => string;
                bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                is: {
                    (name: string, state: boolean | undefined): string;
                    (name: string): string;
                };
                cssVar: (object: Record<string, string>) => Record<string, string>;
                cssVarName: (name: string) => string;
                cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                cssVarBlockName: (name: string) => string;
            };
            style: import("vue").ComputedRef<import("vue").CSSProperties>;
        }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
            readonly size: {
                readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly color: {
                readonly type: import("vue").PropType<string>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
        }>>, {}>> & Record<string, any>;
        Check: any;
        CircleCheck: any;
        Close: any;
        Delete: any;
        Document: any;
        ZoomIn: any;
        ElProgress: import("element-plus/es/utils").SFCWithInstall<import("vue").DefineComponent<{
            readonly type: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "circle" | "line" | "dashboard", unknown, "line", boolean>;
            readonly percentage: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
            readonly status: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "" | "success" | "warning" | "exception", unknown, "", boolean>;
            readonly indeterminate: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly duration: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 3, boolean>;
            readonly strokeWidth: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 6, boolean>;
            readonly strokeLinecap: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => ("inherit" | "round" | "butt" | "square") & {}) | (() => "inherit" | "round" | "butt" | "square") | ((new (...args: any[]) => ("inherit" | "round" | "butt" | "square") & {}) | (() => "inherit" | "round" | "butt" | "square"))[], unknown, unknown, "round", boolean>;
            readonly textInside: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly width: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 126, boolean>;
            readonly showText: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly color: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (string | import("..").ProgressFn | import("..").ProgressColor[]) & {}) | (() => string | import("..").ProgressFn | import("..").ProgressColor[]) | ((new (...args: any[]) => (string | import("..").ProgressFn | import("..").ProgressColor[]) & {}) | (() => string | import("..").ProgressFn | import("..").ProgressColor[]))[], unknown, unknown, "", boolean>;
            readonly striped: BooleanConstructor;
            readonly stripedFlow: BooleanConstructor;
            readonly format: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("..").ProgressFn) | (() => import("..").ProgressFn) | {
                (): import("..").ProgressFn;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => import("..").ProgressFn) | (() => import("..").ProgressFn) | {
                (): import("..").ProgressFn;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, (percentage: number) => string, boolean>;
        }, {
            STATUS_COLOR_MAP: Record<string, string>;
            props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
                readonly type: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "circle" | "line" | "dashboard", unknown, "line", boolean>;
                readonly percentage: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
                readonly status: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "" | "success" | "warning" | "exception", unknown, "", boolean>;
                readonly indeterminate: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
                readonly duration: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 3, boolean>;
                readonly strokeWidth: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 6, boolean>;
                readonly strokeLinecap: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => ("inherit" | "round" | "butt" | "square") & {}) | (() => "inherit" | "round" | "butt" | "square") | ((new (...args: any[]) => ("inherit" | "round" | "butt" | "square") & {}) | (() => "inherit" | "round" | "butt" | "square"))[], unknown, unknown, "round", boolean>;
                readonly textInside: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
                readonly width: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 126, boolean>;
                readonly showText: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
                readonly color: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (string | import("..").ProgressFn | import("..").ProgressColor[]) & {}) | (() => string | import("..").ProgressFn | import("..").ProgressColor[]) | ((new (...args: any[]) => (string | import("..").ProgressFn | import("..").ProgressColor[]) & {}) | (() => string | import("..").ProgressFn | import("..").ProgressColor[]))[], unknown, unknown, "", boolean>;
                readonly striped: BooleanConstructor;
                readonly stripedFlow: BooleanConstructor;
                readonly format: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("..").ProgressFn) | (() => import("..").ProgressFn) | {
                    (): import("..").ProgressFn;
                    new (): any;
                    readonly prototype: any;
                } | ((new (...args: any[]) => import("..").ProgressFn) | (() => import("..").ProgressFn) | {
                    (): import("..").ProgressFn;
                    new (): any;
                    readonly prototype: any;
                })[], unknown, unknown, (percentage: number) => string, boolean>;
            }>> & {
                [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
            }>>;
            ns: {
                namespace: import("vue").ComputedRef<string>;
                b: (blockSuffix?: string) => string;
                e: (element?: string | undefined) => string;
                m: (modifier?: string | undefined) => string;
                be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                em: (element?: string | undefined, modifier?: string | undefined) => string;
                bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                is: {
                    (name: string, state: boolean | undefined): string;
                    (name: string): string;
                };
                cssVar: (object: Record<string, string>) => Record<string, string>;
                cssVarName: (name: string) => string;
                cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                cssVarBlockName: (name: string) => string;
            };
            barStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
            relativeStrokeWidth: import("vue").ComputedRef<string>;
            radius: import("vue").ComputedRef<number>;
            trackPath: import("vue").ComputedRef<string>;
            perimeter: import("vue").ComputedRef<number>;
            rate: import("vue").ComputedRef<1 | 0.75>;
            strokeDashoffset: import("vue").ComputedRef<string>;
            trailPathStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
            circlePathStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
            stroke: import("vue").ComputedRef<string>;
            statusIcon: import("vue").ComputedRef<any>;
            progressTextSize: import("vue").ComputedRef<number>;
            content: import("vue").ComputedRef<string>;
            getColors: (color: import("..").ProgressColor[]) => import("..").ProgressColor[];
            getCurrentColor: (percentage: number) => string;
            ElIcon: import("element-plus/es/utils").SFCWithInstall<import("vue").DefineComponent<{
                readonly size: {
                    readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly color: {
                    readonly type: import("vue").PropType<string>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
            }, {
                props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
                    readonly size: {
                        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    };
                    readonly color: {
                        readonly type: import("vue").PropType<string>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    };
                }>> & {
                    [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
                }>>;
                ns: {
                    namespace: import("vue").ComputedRef<string>;
                    b: (blockSuffix?: string) => string;
                    e: (element?: string | undefined) => string;
                    m: (modifier?: string | undefined) => string;
                    be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                    em: (element?: string | undefined, modifier?: string | undefined) => string;
                    bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                    bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                    is: {
                        (name: string, state: boolean | undefined): string;
                        (name: string): string;
                    };
                    cssVar: (object: Record<string, string>) => Record<string, string>;
                    cssVarName: (name: string) => string;
                    cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                    cssVarBlockName: (name: string) => string;
                };
                style: import("vue").ComputedRef<import("vue").CSSProperties>;
            }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
                readonly size: {
                    readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly color: {
                    readonly type: import("vue").PropType<string>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
            }>>, {}>> & Record<string, any>;
        }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
            readonly type: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "circle" | "line" | "dashboard", unknown, "line", boolean>;
            readonly percentage: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
            readonly status: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "" | "success" | "warning" | "exception", unknown, "", boolean>;
            readonly indeterminate: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly duration: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 3, boolean>;
            readonly strokeWidth: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 6, boolean>;
            readonly strokeLinecap: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => ("inherit" | "round" | "butt" | "square") & {}) | (() => "inherit" | "round" | "butt" | "square") | ((new (...args: any[]) => ("inherit" | "round" | "butt" | "square") & {}) | (() => "inherit" | "round" | "butt" | "square"))[], unknown, unknown, "round", boolean>;
            readonly textInside: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly width: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 126, boolean>;
            readonly showText: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly color: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (string | import("..").ProgressFn | import("..").ProgressColor[]) & {}) | (() => string | import("..").ProgressFn | import("..").ProgressColor[]) | ((new (...args: any[]) => (string | import("..").ProgressFn | import("..").ProgressColor[]) & {}) | (() => string | import("..").ProgressFn | import("..").ProgressColor[]))[], unknown, unknown, "", boolean>;
            readonly striped: BooleanConstructor;
            readonly stripedFlow: BooleanConstructor;
            readonly format: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("..").ProgressFn) | (() => import("..").ProgressFn) | {
                (): import("..").ProgressFn;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => import("..").ProgressFn) | (() => import("..").ProgressFn) | {
                (): import("..").ProgressFn;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, (percentage: number) => string, boolean>;
        }>>, {
            readonly type: import("element-plus/es/utils").EpPropMergeType<StringConstructor, "circle" | "line" | "dashboard", unknown>;
            readonly width: number;
            readonly color: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => (string | import("..").ProgressFn | import("..").ProgressColor[]) & {}) | (() => string | import("..").ProgressFn | import("..").ProgressColor[]) | ((new (...args: any[]) => (string | import("..").ProgressFn | import("..").ProgressColor[]) & {}) | (() => string | import("..").ProgressFn | import("..").ProgressColor[]))[], unknown, unknown>;
            readonly strokeLinecap: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => ("inherit" | "round" | "butt" | "square") & {}) | (() => "inherit" | "round" | "butt" | "square") | ((new (...args: any[]) => ("inherit" | "round" | "butt" | "square") & {}) | (() => "inherit" | "round" | "butt" | "square"))[], unknown, unknown>;
            readonly strokeWidth: number;
            readonly indeterminate: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly format: import("..").ProgressFn;
            readonly percentage: number;
            readonly status: import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "success" | "warning" | "exception", unknown>;
            readonly duration: number;
            readonly textInside: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly showText: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly striped: boolean;
            readonly stripedFlow: boolean;
        }>> & Record<string, any>;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
        remove: (file: import("./src/upload").UploadFile) => boolean;
    }, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
        readonly files: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/upload").UploadFiles) | (() => import("./src/upload").UploadFiles) | ((new (...args: any[]) => import("./src/upload").UploadFiles) | (() => import("./src/upload").UploadFiles))[], unknown, unknown, () => never[], boolean>;
        readonly disabled: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly handlePreview: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (uploadFile: import("./src/upload").UploadFile) => void) | (() => (uploadFile: import("./src/upload").UploadFile) => void) | {
            (): (uploadFile: import("./src/upload").UploadFile) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (uploadFile: import("./src/upload").UploadFile) => void) | (() => (uploadFile: import("./src/upload").UploadFile) => void) | {
            (): (uploadFile: import("./src/upload").UploadFile) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly listType: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
    }>> & {
        onRemove?: ((file: import("./src/upload").UploadFile) => any) | undefined;
    }, {
        readonly disabled: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly listType: import("element-plus/es/utils").EpPropMergeType<StringConstructor, "text" | "picture" | "picture-card", unknown>;
        readonly files: import("./src/upload").UploadFiles;
        readonly handlePreview: (uploadFile: import("./src/upload").UploadFile) => void;
    }>;
    UploadContent: import("vue").DefineComponent<{
        readonly beforeUpload: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
            (): (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
            (): (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onRemove: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | (() => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | {
            (): (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | (() => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | {
            (): (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onStart: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => void) | (() => (rawFile: import("./src/upload").UploadRawFile) => void) | {
            (): (rawFile: import("./src/upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => void) | (() => (rawFile: import("./src/upload").UploadRawFile) => void) | {
            (): (rawFile: import("./src/upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onSuccess: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | {
            (): (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | {
            (): (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onProgress: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | {
            (): (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | {
            (): (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onError: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | {
            (): (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | {
            (): (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onExceed: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | {
            (): (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | {
            (): (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly action: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "#", boolean>;
        readonly headers: {
            readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers) | ((new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers))[], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly method: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "post", boolean>;
        readonly data: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | ((new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
        readonly multiple: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly name: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "file", boolean>;
        readonly drag: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly withCredentials: BooleanConstructor;
        readonly showFileList: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly accept: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
        readonly fileList: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/upload").UploadUserFile[]) | (() => import("./src/upload").UploadUserFile[]) | ((new (...args: any[]) => import("./src/upload").UploadUserFile[]) | (() => import("./src/upload").UploadUserFile[]))[], unknown, unknown, () => [], boolean>;
        readonly autoUpload: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly listType: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
        readonly httpRequest: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/upload").UploadRequestHandler) | (() => import("./src/upload").UploadRequestHandler) | {
            (): import("./src/upload").UploadRequestHandler;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => import("./src/upload").UploadRequestHandler) | (() => import("./src/upload").UploadRequestHandler) | {
            (): import("./src/upload").UploadRequestHandler;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, import("./src/upload").UploadRequestHandler, boolean>;
        readonly disabled: BooleanConstructor;
        readonly limit: NumberConstructor;
    }, {
        props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
            readonly beforeUpload: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
                (): (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
                (): (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onRemove: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | (() => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | {
                (): (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | (() => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | {
                (): (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onStart: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => void) | (() => (rawFile: import("./src/upload").UploadRawFile) => void) | {
                (): (rawFile: import("./src/upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => void) | (() => (rawFile: import("./src/upload").UploadRawFile) => void) | {
                (): (rawFile: import("./src/upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onSuccess: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | {
                (): (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | {
                (): (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onProgress: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | {
                (): (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | {
                (): (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onError: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | {
                (): (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | {
                (): (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly onExceed: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | {
                (): (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | {
                (): (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, () => void, boolean>;
            readonly action: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "#", boolean>;
            readonly headers: {
                readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers) | ((new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers))[], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly method: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "post", boolean>;
            readonly data: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | ((new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
            readonly multiple: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly name: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "file", boolean>;
            readonly drag: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
            readonly withCredentials: BooleanConstructor;
            readonly showFileList: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly accept: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
            readonly fileList: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/upload").UploadUserFile[]) | (() => import("./src/upload").UploadUserFile[]) | ((new (...args: any[]) => import("./src/upload").UploadUserFile[]) | (() => import("./src/upload").UploadUserFile[]))[], unknown, unknown, () => [], boolean>;
            readonly autoUpload: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly listType: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
            readonly httpRequest: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/upload").UploadRequestHandler) | (() => import("./src/upload").UploadRequestHandler) | {
                (): import("./src/upload").UploadRequestHandler;
                new (): any;
                readonly prototype: any;
            } | ((new (...args: any[]) => import("./src/upload").UploadRequestHandler) | (() => import("./src/upload").UploadRequestHandler) | {
                (): import("./src/upload").UploadRequestHandler;
                new (): any;
                readonly prototype: any;
            })[], unknown, unknown, import("./src/upload").UploadRequestHandler, boolean>;
            readonly disabled: BooleanConstructor;
            readonly limit: NumberConstructor;
        }>> & {
            [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
        }>>;
        ns: {
            namespace: import("vue").ComputedRef<string>;
            b: (blockSuffix?: string) => string;
            e: (element?: string | undefined) => string;
            m: (modifier?: string | undefined) => string;
            be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
            em: (element?: string | undefined, modifier?: string | undefined) => string;
            bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
            bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        disabled: import("vue").ComputedRef<boolean>;
        requests: import("vue").ShallowRef<Record<string, XMLHttpRequest | Promise<unknown>>>;
        inputRef: import("vue").ShallowRef<HTMLInputElement | undefined>;
        uploadFiles: (files: File[]) => void;
        upload: (rawFile: import("./src/upload").UploadRawFile) => Promise<void>;
        resolveData: (data: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | ((new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>))[], unknown, unknown>, rawFile: import("./src/upload").UploadRawFile) => Promise<Record<string, any>>;
        doUpload: (rawFile: import("./src/upload").UploadRawFile, beforeData?: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | ((new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>))[], unknown, unknown> | undefined) => Promise<void>;
        handleChange: (e: Event) => void;
        handleClick: () => void;
        handleKeydown: () => void;
        abort: (file?: import("./src/upload").UploadFile | undefined) => void;
        UploadDragger: import("vue").DefineComponent<{
            readonly disabled: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        }, {
            COMPONENT_NAME: string;
            emit: (event: "file", file: File[]) => void;
            uploaderContext: import("./src/constants").UploadContext;
            ns: {
                namespace: import("vue").ComputedRef<string>;
                b: (blockSuffix?: string) => string;
                e: (element?: string | undefined) => string;
                m: (modifier?: string | undefined) => string;
                be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                em: (element?: string | undefined, modifier?: string | undefined) => string;
                bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                is: {
                    (name: string, state: boolean | undefined): string;
                    (name: string): string;
                };
                cssVar: (object: Record<string, string>) => Record<string, string>;
                cssVarName: (name: string) => string;
                cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                cssVarBlockName: (name: string) => string;
            };
            dragover: import("vue").Ref<boolean>;
            disabled: import("vue").ComputedRef<boolean>;
            onDrop: (e: DragEvent) => void;
            onDragover: () => void;
        }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
            file: (file: File[]) => boolean;
        }, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
            readonly disabled: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        }>> & {
            onFile?: ((file: File[]) => any) | undefined;
        }, {
            readonly disabled: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        }>;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
        readonly beforeUpload: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
            (): (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
            (): (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onRemove: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | (() => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | {
            (): (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | (() => (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void) | {
            (): (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onStart: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => void) | (() => (rawFile: import("./src/upload").UploadRawFile) => void) | {
            (): (rawFile: import("./src/upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => void) | (() => (rawFile: import("./src/upload").UploadRawFile) => void) | {
            (): (rawFile: import("./src/upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onSuccess: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | {
            (): (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | (() => (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown) | {
            (): (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onProgress: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | {
            (): (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void) | {
            (): (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onError: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | {
            (): (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | (() => (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void) | {
            (): (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly onExceed: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | {
            (): (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | {
            (): (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, () => void, boolean>;
        readonly action: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "#", boolean>;
        readonly headers: {
            readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers) | ((new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers))[], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly method: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "post", boolean>;
        readonly data: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | ((new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
        readonly multiple: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly name: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "file", boolean>;
        readonly drag: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
        readonly withCredentials: BooleanConstructor;
        readonly showFileList: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly accept: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
        readonly fileList: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/upload").UploadUserFile[]) | (() => import("./src/upload").UploadUserFile[]) | ((new (...args: any[]) => import("./src/upload").UploadUserFile[]) | (() => import("./src/upload").UploadUserFile[]))[], unknown, unknown, () => [], boolean>;
        readonly autoUpload: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly listType: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
        readonly httpRequest: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/upload").UploadRequestHandler) | (() => import("./src/upload").UploadRequestHandler) | {
            (): import("./src/upload").UploadRequestHandler;
            new (): any;
            readonly prototype: any;
        } | ((new (...args: any[]) => import("./src/upload").UploadRequestHandler) | (() => import("./src/upload").UploadRequestHandler) | {
            (): import("./src/upload").UploadRequestHandler;
            new (): any;
            readonly prototype: any;
        })[], unknown, unknown, import("./src/upload").UploadRequestHandler, boolean>;
        readonly disabled: BooleanConstructor;
        readonly limit: NumberConstructor;
    }>>, {
        readonly disabled: boolean;
        readonly name: string;
        readonly drag: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly multiple: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly data: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | ((new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>))[], unknown, unknown>;
        readonly onError: (err: import("./src/ajax").UploadAjaxError, rawFile: import("./src/upload").UploadRawFile) => void;
        readonly onProgress: (evt: import("./src/upload").UploadProgressEvent, rawFile: import("./src/upload").UploadRawFile) => void;
        readonly action: string;
        readonly method: string;
        readonly showFileList: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly accept: string;
        readonly fileList: import("./src/upload").UploadUserFile[];
        readonly autoUpload: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly listType: import("element-plus/es/utils").EpPropMergeType<StringConstructor, "text" | "picture" | "picture-card", unknown>;
        readonly httpRequest: import("./src/upload").UploadRequestHandler;
        readonly withCredentials: boolean;
        readonly beforeUpload: (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>;
        readonly onRemove: (file: import("./src/upload").UploadRawFile | import("./src/upload").UploadFile, rawFile?: import("./src/upload").UploadRawFile | undefined) => void;
        readonly onSuccess: (response: any, rawFile: import("./src/upload").UploadRawFile) => unknown;
        readonly onExceed: (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void;
        readonly onStart: (rawFile: import("./src/upload").UploadRawFile) => void;
    }>;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    readonly beforeUpload: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
        (): (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | (() => (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>) | {
        (): (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly beforeRemove: {
        readonly type: import("vue").PropType<(uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => import("element-plus/es/utils").Awaitable<boolean>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly onRemove: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
        (): (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
        (): (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly onChange: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
        (): (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
        (): (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly onPreview: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (uploadFile: import("./src/upload").UploadFile) => void) | (() => (uploadFile: import("./src/upload").UploadFile) => void) | {
        (): (uploadFile: import("./src/upload").UploadFile) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (uploadFile: import("./src/upload").UploadFile) => void) | (() => (uploadFile: import("./src/upload").UploadFile) => void) | {
        (): (uploadFile: import("./src/upload").UploadFile) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly onSuccess: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (response: any, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (response: any, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
        (): (response: any, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (response: any, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (response: any, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
        (): (response: any, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly onProgress: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (evt: import("./src/upload").UploadProgressEvent, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (evt: import("./src/upload").UploadProgressEvent, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
        (): (evt: import("./src/upload").UploadProgressEvent, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (evt: import("./src/upload").UploadProgressEvent, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (evt: import("./src/upload").UploadProgressEvent, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
        (): (evt: import("./src/upload").UploadProgressEvent, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly onError: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (error: Error, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (error: Error, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
        (): (error: Error, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (error: Error, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | (() => (error: Error, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void) | {
        (): (error: Error, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly onExceed: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | {
        (): (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | (() => (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void) | {
        (): (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => void, boolean>;
    readonly action: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "#", boolean>;
    readonly headers: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers) | ((new (...args: any[]) => Record<string, any> | Headers) | (() => Record<string, any> | Headers))[], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly method: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "post", boolean>;
    readonly data: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | ((new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
    readonly multiple: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly name: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "file", boolean>;
    readonly drag: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly withCredentials: BooleanConstructor;
    readonly showFileList: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
    readonly accept: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
    readonly fileList: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/upload").UploadUserFile[]) | (() => import("./src/upload").UploadUserFile[]) | ((new (...args: any[]) => import("./src/upload").UploadUserFile[]) | (() => import("./src/upload").UploadUserFile[]))[], unknown, unknown, () => [], boolean>;
    readonly autoUpload: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
    readonly listType: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "text" | "picture" | "picture-card", unknown, "text", boolean>;
    readonly httpRequest: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/upload").UploadRequestHandler) | (() => import("./src/upload").UploadRequestHandler) | {
        (): import("./src/upload").UploadRequestHandler;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => import("./src/upload").UploadRequestHandler) | (() => import("./src/upload").UploadRequestHandler) | {
        (): import("./src/upload").UploadRequestHandler;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, import("./src/upload").UploadRequestHandler, boolean>;
    readonly disabled: BooleanConstructor;
    readonly limit: NumberConstructor;
}>>, {
    readonly onChange: (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
    readonly disabled: boolean;
    readonly name: string;
    readonly drag: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    readonly multiple: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    readonly data: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | ((new (...args: any[]) => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Mutable<Record<string, any>> | Promise<import("element-plus/es/utils").Mutable<Record<string, any>>>) | (() => ((rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>) | import("element-plus/es/utils").Awaitable<import("element-plus/es/utils").Mutable<Record<string, any>>>))[], unknown, unknown>;
    readonly onError: (error: Error, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
    readonly onProgress: (evt: import("./src/upload").UploadProgressEvent, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
    readonly action: string;
    readonly method: string;
    readonly showFileList: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    readonly accept: string;
    readonly fileList: import("./src/upload").UploadUserFile[];
    readonly autoUpload: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
    readonly listType: import("element-plus/es/utils").EpPropMergeType<StringConstructor, "text" | "picture" | "picture-card", unknown>;
    readonly httpRequest: import("./src/upload").UploadRequestHandler;
    readonly withCredentials: boolean;
    readonly beforeUpload: (rawFile: import("./src/upload").UploadRawFile) => import("element-plus/es/utils").Awaitable<boolean | void | File | Blob | null | undefined>;
    readonly onRemove: (uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
    readonly onPreview: (uploadFile: import("./src/upload").UploadFile) => void;
    readonly onSuccess: (response: any, uploadFile: import("./src/upload").UploadFile, uploadFiles: import("./src/upload").UploadFiles) => void;
    readonly onExceed: (files: File[], uploadFiles: import("./src/upload").UploadUserFile[]) => void;
}>> & Record<string, any>;
export default ElUpload;
export * from './src/upload';
export * from './src/upload-content';
export * from './src/upload-list';
export * from './src/upload-dragger';
export * from './src/constants';
