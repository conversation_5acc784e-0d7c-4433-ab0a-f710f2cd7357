{"version": 3, "file": "upload-content2.mjs", "sources": ["../../../../../../packages/components/upload/src/upload-content.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[ns.b(), ns.m(listType), ns.is('drag', drag)]\"\n    tabindex=\"0\"\n    @click=\"handleClick\"\n    @keydown.self.enter.space=\"handleKeydown\"\n  >\n    <template v-if=\"drag\">\n      <upload-dragger :disabled=\"disabled\" @file=\"uploadFiles\">\n        <slot />\n      </upload-dragger>\n    </template>\n    <template v-else>\n      <slot />\n    </template>\n    <input\n      ref=\"inputRef\"\n      :class=\"ns.e('input')\"\n      :name=\"name\"\n      :multiple=\"multiple\"\n      :accept=\"accept\"\n      type=\"file\"\n      @change=\"handleChange\"\n      @click.stop\n    />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { shallowRef } from 'vue'\nimport { isPlainObject } from '@vue/shared'\nimport { cloneDeep, isEqual } from 'lodash-unified'\nimport { useNamespace } from '@element-plus/hooks'\nimport { entriesOf, isFunction } from '@element-plus/utils'\nimport { useFormDisabled } from '@element-plus/components/form'\nimport UploadDragger from './upload-dragger.vue'\nimport { uploadContentProps } from './upload-content'\nimport { genFileId } from './upload'\nimport type { UploadContentProps } from './upload-content'\n\nimport type {\n  UploadFile,\n  UploadHooks,\n  UploadRawFile,\n  UploadRequestOptions,\n} from './upload'\n\ndefineOptions({\n  name: 'ElUploadContent',\n  inheritAttrs: false,\n})\n\nconst props = defineProps(uploadContentProps)\nconst ns = useNamespace('upload')\nconst disabled = useFormDisabled()\n\nconst requests = shallowRef<Record<string, XMLHttpRequest | Promise<unknown>>>(\n  {}\n)\nconst inputRef = shallowRef<HTMLInputElement>()\n\nconst uploadFiles = (files: File[]) => {\n  if (files.length === 0) return\n\n  const { autoUpload, limit, fileList, multiple, onStart, onExceed } = props\n\n  if (limit && fileList.length + files.length > limit) {\n    onExceed(files, fileList)\n    return\n  }\n\n  if (!multiple) {\n    files = files.slice(0, 1)\n  }\n\n  for (const file of files) {\n    const rawFile = file as UploadRawFile\n    rawFile.uid = genFileId()\n    onStart(rawFile)\n    if (autoUpload) upload(rawFile)\n  }\n}\n\nconst upload = async (rawFile: UploadRawFile): Promise<void> => {\n  inputRef.value!.value = ''\n\n  if (!props.beforeUpload) {\n    return doUpload(rawFile)\n  }\n\n  let hookResult: Exclude<ReturnType<UploadHooks['beforeUpload']>, Promise<any>>\n  let beforeData: UploadContentProps['data'] = {}\n\n  try {\n    // origin data: Handle data changes after synchronization tasks are executed\n    const originData = props.data\n    const beforeUploadPromise = props.beforeUpload(rawFile)\n    beforeData = isPlainObject(props.data) ? cloneDeep(props.data) : props.data\n    hookResult = await beforeUploadPromise\n    if (isPlainObject(props.data) && isEqual(originData, beforeData)) {\n      beforeData = cloneDeep(props.data)\n    }\n  } catch {\n    hookResult = false\n  }\n\n  if (hookResult === false) {\n    props.onRemove(rawFile)\n    return\n  }\n\n  let file: File = rawFile\n  if (hookResult instanceof Blob) {\n    if (hookResult instanceof File) {\n      file = hookResult\n    } else {\n      file = new File([hookResult], rawFile.name, {\n        type: rawFile.type,\n      })\n    }\n  }\n\n  doUpload(\n    Object.assign(file, {\n      uid: rawFile.uid,\n    }),\n    beforeData\n  )\n}\n\nconst resolveData = async (\n  data: UploadContentProps['data'],\n  rawFile: UploadRawFile\n): Promise<Record<string, any>> => {\n  if (isFunction(data)) {\n    return data(rawFile)\n  }\n\n  return data\n}\n\nconst doUpload = async (\n  rawFile: UploadRawFile,\n  beforeData?: UploadContentProps['data']\n) => {\n  const {\n    headers,\n    data,\n    method,\n    withCredentials,\n    name: filename,\n    action,\n    onProgress,\n    onSuccess,\n    onError,\n    httpRequest,\n  } = props\n\n  try {\n    beforeData = await resolveData(beforeData ?? data, rawFile)\n  } catch {\n    props.onRemove(rawFile)\n    return\n  }\n\n  const { uid } = rawFile\n  const options: UploadRequestOptions = {\n    headers: headers || {},\n    withCredentials,\n    file: rawFile,\n    data: beforeData,\n    method,\n    filename,\n    action,\n    onProgress: (evt) => {\n      onProgress(evt, rawFile)\n    },\n    onSuccess: (res) => {\n      onSuccess(res, rawFile)\n      delete requests.value[uid]\n    },\n    onError: (err) => {\n      onError(err, rawFile)\n      delete requests.value[uid]\n    },\n  }\n  const request = httpRequest(options)\n  requests.value[uid] = request\n  if (request instanceof Promise) {\n    request.then(options.onSuccess, options.onError)\n  }\n}\n\nconst handleChange = (e: Event) => {\n  const files = (e.target as HTMLInputElement).files\n  if (!files) return\n  uploadFiles(Array.from(files))\n}\n\nconst handleClick = () => {\n  if (!disabled.value) {\n    inputRef.value!.value = ''\n    inputRef.value!.click()\n  }\n}\n\nconst handleKeydown = () => {\n  handleClick()\n}\n\nconst abort = (file?: UploadFile) => {\n  const _reqs = entriesOf(requests.value).filter(\n    file ? ([uid]) => String(file.uid) === uid : () => true\n  )\n  _reqs.forEach(([uid, req]) => {\n    if (req instanceof XMLHttpRequest) req.abort()\n    delete requests.value[uid]\n  })\n}\n\ndefineExpose({\n  abort,\n  upload,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;mCA+Cc,CAAA;AAAA,EACZ,IAAM,EAAA,iBAAA;AAAA,EACN,YAAc,EAAA,KAAA;AAChB,CAAA,CAAA,CAAA;;;;;;AAGA,IAAM,MAAA,EAAA,GAAK,aAAa,QAAQ,CAAA,CAAA;AAChC,IAAA,MAAM,WAAW,eAAgB,EAAA,CAAA;AAEjC,IAAM,MAAA,QAAA,GAAW,UACf,CAAA,EACF,CAAA,CAAA;AACA,IAAA,MAAM,WAAW,UAA6B,EAAA,CAAA;AAE9C,IAAM,MAAA,WAAA,GAAc,CAAC,KAAkB,KAAA;AACrC,MAAA,IAAI,MAAM,MAAW,KAAA,CAAA;AAAG,QAAA,OAAA;AAExB,MAAA,MAAM,EAAE,UAAY,EAAA,KAAA,EAAO,QAAU,EAAA,QAAA,EAAU,SAAS,QAAa,EAAA,GAAA,KAAA,CAAA;AAErE,MAAA,IAAI,KAAS,IAAA,QAAA,CAAS,MAAS,GAAA,KAAA,CAAM,SAAS,KAAO,EAAA;AACnD,QAAA,QAAA,CAAS,OAAO,QAAQ,CAAA,CAAA;AACxB,QAAA,OAAA;AAAA,OACF;AAEA,MAAA,IAAI,CAAC,QAAU,EAAA;AACb,QAAQ,KAAA,GAAA,KAAA,CAAM,KAAM,CAAA,CAAA,EAAG,CAAC,CAAA,CAAA;AAAA,OAC1B;AAEA,MAAA,KAAA,MAAW,QAAQ,KAAO,EAAA;AACxB,QAAA,MAAM,OAAU,GAAA,IAAA,CAAA;AAChB,QAAA,OAAA,CAAQ,MAAM,SAAU,EAAA,CAAA;AACxB,QAAA,OAAA,CAAQ,OAAO,CAAA,CAAA;AACf,QAAI,IAAA,UAAA;AAAY,UAAA,MAAA,CAAO,OAAO,CAAA,CAAA;AAAA,OAChC;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,MAAA,GAAS,OAAO,OAA0C,KAAA;AAC9D,MAAA,QAAA,CAAS,MAAO,KAAQ,GAAA,EAAA,CAAA;AAExB,MAAI,IAAA,CAAC,MAAM,YAAc,EAAA;AACvB,QAAA,OAAO,SAAS,OAAO,CAAA,CAAA;AAAA,OACzB;AAEA,MAAI,IAAA,UAAA,CAAA;AACJ,MAAA,IAAI,aAAyC,EAAC,CAAA;AAE9C,MAAI,IAAA;AAEF,QAAA,MAAM,aAAa,KAAM,CAAA,IAAA,CAAA;AACzB,QAAM,MAAA,mBAAA,GAAsB,KAAM,CAAA,YAAA,CAAa,OAAO,CAAA,CAAA;AACtD,QAAa,UAAA,GAAA,aAAA,CAAc,MAAM,IAAI,CAAA,GAAI,UAAU,KAAM,CAAA,IAAI,IAAI,KAAM,CAAA,IAAA,CAAA;AACvE,QAAA,UAAA,GAAa,MAAM,mBAAA,CAAA;AACnB,QAAA,IAAI,cAAc,KAAM,CAAA,IAAI,KAAK,OAAQ,CAAA,UAAA,EAAY,UAAU,CAAG,EAAA;AAChE,UAAa,UAAA,GAAA,SAAA,CAAU,MAAM,IAAI,CAAA,CAAA;AAAA,SACnC;AAAA,OACA,CAAA,OAAA,CAAA,EAAA;AACA,QAAa,UAAA,GAAA,KAAA,CAAA;AAAA,OACf;AAEA,MAAA,IAAI,eAAe,KAAO,EAAA;AACxB,QAAA,KAAA,CAAM,SAAS,OAAO,CAAA,CAAA;AACtB,QAAA,OAAA;AAAA,OACF;AAEA,MAAA,IAAI,IAAa,GAAA,OAAA,CAAA;AACjB,MAAA,IAAI,sBAAsB,IAAM,EAAA;AAC9B,QAAA,IAAI,sBAAsB,IAAM,EAAA;AAC9B,UAAO,IAAA,GAAA,UAAA,CAAA;AAAA,SACF,MAAA;AACL,UAAA,IAAA,GAAO,IAAI,IAAK,CAAA,CAAC,UAAU,CAAA,EAAG,QAAQ,IAAM,EAAA;AAAA,YAC1C,MAAM,OAAQ,CAAA,IAAA;AAAA,WACf,CAAA,CAAA;AAAA,SACH;AAAA,OACF;AAEA,MACE,QAAA,CAAA,MAAA,CAAO,OAAO,IAAM,EAAA;AAAA,QAClB,KAAK,OAAQ,CAAA,GAAA;AAAA,OACd,GACD,UACF,CAAA,CAAA;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,WAAA,GAAc,OAClB,IAAA,EACA,OACiC,KAAA;AACjC,MAAI,IAAA,UAAA,CAAW,IAAI,CAAG,EAAA;AACpB,QAAA,OAAO,KAAK,OAAO,CAAA,CAAA;AAAA,OACrB;AAEA,MAAO,OAAA,IAAA,CAAA;AAAA,KACT,CAAA;AAEA,IAAM,MAAA,QAAA,GAAW,OACf,OAAA,EACA,UACG,KAAA;AACH,MAAM,MAAA;AAAA,QACJ,OAAA;AAAA,QACA,IAAA;AAAA,QACA,MAAA;AAAA,QACA,eAAA;AAAA,QACA,IAAM,EAAA,QAAA;AAAA,QACN,MAAA;AAAA,QACA,UAAA;AAAA,QACA,SAAA;AAAA,QACA,OAAA;AAAA,QACA,WAAA;AAAA,OACE,GAAA,KAAA,CAAA;AAEJ,MAAI,IAAA;AACF,QAAA,UAAA,GAAa,MAAM,WAAA,CAAY,UAAc,IAAA,IAAA,GAAa,UAAA,GAAA,IAAA,EAAA,OAAA,CAAA,CAAA;AAAA,OAC1D,CAAA,OAAA,CAAA,EAAA;AACA,QAAA,KAAA,CAAM,SAAS,OAAO,CAAA,CAAA;AACtB,QAAA,OAAA;AAAA,OACF;AAEA,MAAA,MAAM,EAAE,GAAQ,EAAA,GAAA,OAAA,CAAA;AAChB,MAAA,MAAM,OAAgC,GAAA;AAAA,QACpC,OAAA,EAAS,WAAW,EAAC;AAAA,QACrB,eAAA;AAAA,QACA,IAAM,EAAA,OAAA;AAAA,QACN,IAAM,EAAA,UAAA;AAAA,QACN,MAAA;AAAA,QACA,QAAA;AAAA,QACA,MAAA;AAAA,QACA,UAAA,EAAY,CAAC,GAAQ,KAAA;AACnB,UAAA,UAAA,CAAW,KAAK,OAAO,CAAA,CAAA;AAAA,SACzB;AAAA,QACA,SAAA,EAAW,CAAC,GAAQ,KAAA;AAClB,UAAA,SAAA,CAAU,KAAK,OAAO,CAAA,CAAA;AACtB,UAAA,OAAO,SAAS,KAAM,CAAA,GAAA,CAAA,CAAA;AAAA,SACxB;AAAA,QACA,OAAA,EAAS,CAAC,GAAQ,KAAA;AAChB,UAAA,OAAA,CAAQ,KAAK,OAAO,CAAA,CAAA;AACpB,UAAA,OAAO,SAAS,KAAM,CAAA,GAAA,CAAA,CAAA;AAAA,SACxB;AAAA,OACF,CAAA;AACA,MAAM,MAAA,OAAA,GAAU,YAAY,OAAO,CAAA,CAAA;AACnC,MAAA,QAAA,CAAS,MAAM,GAAO,CAAA,GAAA,OAAA,CAAA;AACtB,MAAA,IAAI,mBAAmB,OAAS,EAAA;AAC9B,QAAA,OAAA,CAAQ,IAAK,CAAA,OAAA,CAAQ,SAAW,EAAA,OAAA,CAAQ,OAAO,CAAA,CAAA;AAAA,OACjD;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,YAAA,GAAe,CAAC,CAAa,KAAA;AACjC,MAAM,MAAA,KAAA,GAAS,EAAE,MAA4B,CAAA,KAAA,CAAA;AAC7C,MAAA,IAAI,CAAC,KAAA;AAAO,QAAA,OAAA;AACZ,MAAY,WAAA,CAAA,KAAA,CAAM,IAAK,CAAA,KAAK,CAAC,CAAA,CAAA;AAAA,KAC/B,CAAA;AAEA,IAAA,MAAM,cAAc,MAAM;AACxB,MAAI,IAAA,CAAC,SAAS,KAAO,EAAA;AACnB,QAAA,QAAA,CAAS,MAAO,KAAQ,GAAA,EAAA,CAAA;AACxB,QAAA,QAAA,CAAS,MAAO,KAAM,EAAA,CAAA;AAAA,OACxB;AAAA,KACF,CAAA;AAEA,IAAA,MAAM,gBAAgB,MAAM;AAC1B,MAAY,WAAA,EAAA,CAAA;AAAA,KACd,CAAA;AAEA,IAAM,MAAA,KAAA,GAAQ,CAAC,IAAsB,KAAA;AACnC,MAAA,MAAM,QAAQ,SAAU,CAAA,QAAA,CAAS,KAAK,CAAA,CAAE,OACtC,IAAO,GAAA,CAAC,CAAC,GAAA,CAAA,KAAS,OAAO,IAAK,CAAA,GAAG,CAAM,KAAA,GAAA,GAAM,MAAM,IACrD,CAAA,CAAA;AACA,MAAA,KAAA,CAAM,OAAQ,CAAA,CAAC,CAAC,GAAA,EAAK,GAAS,CAAA,KAAA;AAC5B,QAAA,IAAI,GAAe,YAAA,cAAA;AAAgB,UAAA,GAAA,CAAI,KAAM,EAAA,CAAA;AAC7C,QAAA,OAAO,SAAS,KAAM,CAAA,GAAA,CAAA,CAAA;AAAA,OACvB,CAAA,CAAA;AAAA,KACH,CAAA;AAEA,IAAa,MAAA,CAAA;AAAA,MACX,KAAA;AAAA,MACA,MAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}