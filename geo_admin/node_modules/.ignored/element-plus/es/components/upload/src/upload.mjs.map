{"version": 3, "file": "upload.mjs", "sources": ["../../../../../../packages/components/upload/src/upload.ts"], "sourcesContent": ["import { NOOP } from '@vue/shared'\nimport { buildProps, definePropType, mutable } from '@element-plus/utils'\nimport { ajaxUpload } from './ajax'\nimport type { Awaitable, Mutable } from '@element-plus/utils'\n\nimport type { UploadAjaxError } from './ajax'\nimport type { ExtractPropTypes } from 'vue'\nimport type Upload from './upload.vue'\n\nexport const uploadListTypes = ['text', 'picture', 'picture-card'] as const\n\nlet fileId = 1\nexport const genFileId = () => Date.now() + fileId++\n\nexport type UploadStatus = 'ready' | 'uploading' | 'success' | 'fail'\nexport interface UploadProgressEvent extends ProgressEvent {\n  percent: number\n}\n\nexport interface UploadRequestOptions {\n  action: string\n  method: string\n  data: Record<string, string | Blob | [string | Blob, string]>\n  filename: string\n  file: UploadRawFile\n  headers: Headers | Record<string, string | number | null | undefined>\n  onError: (evt: UploadAjaxError) => void\n  onProgress: (evt: UploadProgressEvent) => void\n  onSuccess: (response: any) => void\n  withCredentials: boolean\n}\nexport interface UploadFile {\n  name: string\n  percentage?: number\n  status: UploadStatus\n  size?: number\n  response?: unknown\n  uid: number\n  url?: string\n  raw?: UploadRawFile\n}\nexport type UploadUserFile = Omit<UploadFile, 'status' | 'uid'> &\n  Partial<Pick<UploadFile, 'status' | 'uid'>>\n\nexport type UploadFiles = UploadFile[]\nexport interface UploadRawFile extends File {\n  uid: number\n}\nexport type UploadRequestHandler = (\n  options: UploadRequestOptions\n) => XMLHttpRequest | Promise<unknown>\nexport interface UploadHooks {\n  beforeUpload: (\n    rawFile: UploadRawFile\n  ) => Awaitable<void | undefined | null | boolean | File | Blob>\n  beforeRemove: (\n    uploadFile: UploadFile,\n    uploadFiles: UploadFiles\n  ) => Awaitable<boolean>\n  onRemove: (uploadFile: UploadFile, uploadFiles: UploadFiles) => void\n  onChange: (uploadFile: UploadFile, uploadFiles: UploadFiles) => void\n  onPreview: (uploadFile: UploadFile) => void\n  onSuccess: (\n    response: any,\n    uploadFile: UploadFile,\n    uploadFiles: UploadFiles\n  ) => void\n  onProgress: (\n    evt: UploadProgressEvent,\n    uploadFile: UploadFile,\n    uploadFiles: UploadFiles\n  ) => void\n  onError: (\n    error: Error,\n    uploadFile: UploadFile,\n    uploadFiles: UploadFiles\n  ) => void\n  onExceed: (files: File[], uploadFiles: UploadUserFile[]) => void\n}\n\nexport type UploadData = Mutable<Record<string, any>>\n\nexport const uploadBaseProps = buildProps({\n  /**\n   * @description request URL\n   */\n  action: {\n    type: String,\n    default: '#',\n  },\n  /**\n   * @description request headers\n   */\n  headers: {\n    type: definePropType<Headers | Record<string, any>>(Object),\n  },\n  /**\n   * @description set upload request method\n   */\n  method: {\n    type: String,\n    default: 'post',\n  },\n  /**\n   * @description additions options of request\n   */\n  data: {\n    type: definePropType<\n      | Awaitable<UploadData>\n      | ((rawFile: UploadRawFile) => Awaitable<UploadData>)\n    >([Object, Function, Promise]),\n    default: () => mutable({} as const),\n  },\n  /**\n   * @description whether uploading multiple files is permitted\n   */\n  multiple: {\n    type: Boolean,\n    default: false,\n  },\n  /**\n   * @description key name for uploaded file\n   */\n  name: {\n    type: String,\n    default: 'file',\n  },\n  /**\n   * @description whether to activate drag and drop mode\n   */\n  drag: {\n    type: Boolean,\n    default: false,\n  },\n  /**\n   * @description whether cookies are sent\n   */\n  withCredentials: Boolean,\n  /**\n   * @description whether to show the uploaded file list\n   */\n  showFileList: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description accepted [file types](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#attr-accept), will not work when `thumbnail-mode === true`\n   */\n  accept: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description default uploaded files\n   */\n  fileList: {\n    type: definePropType<UploadUserFile[]>(Array),\n    default: () => mutable([] as const),\n  },\n  /**\n   * @description whether to auto upload file\n   */\n  autoUpload: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description type of file list\n   */\n  listType: {\n    type: String,\n    values: uploadListTypes,\n    default: 'text',\n  },\n  /**\n   * @description override default xhr behavior, allowing you to implement your own upload-file's request\n   */\n  httpRequest: {\n    type: definePropType<UploadRequestHandler>(Function),\n    default: ajaxUpload,\n  },\n  /**\n   * @description whether to disable upload\n   */\n  disabled: Boolean,\n  /**\n   * @description maximum number of uploads allowed\n   */\n  limit: Number,\n} as const)\n\nexport const uploadProps = buildProps({\n  ...uploadBaseProps,\n  /**\n   * @description hook function before uploading with the file to be uploaded as its parameter. If `false` is returned or a `Promise` is returned and then is rejected, uploading will be aborted\n   */\n  beforeUpload: {\n    type: definePropType<UploadHooks['beforeUpload']>(Function),\n    default: NOOP,\n  },\n  /**\n   * @description hook function before removing a file with the file and file list as its parameters. If `false` is returned or a `Promise` is returned and then is rejected, removing will be aborted\n   */\n  beforeRemove: {\n    type: definePropType<UploadHooks['beforeRemove']>(Function),\n  },\n  /**\n   * @description hook function when files are removed\n   */\n  onRemove: {\n    type: definePropType<UploadHooks['onRemove']>(Function),\n    default: NOOP,\n  },\n  /**\n   * @description hook function when select file or upload file success or upload file fail\n   */\n  onChange: {\n    type: definePropType<UploadHooks['onChange']>(Function),\n    default: NOOP,\n  },\n  /**\n   * @description hook function when clicking the uploaded files\n   */\n  onPreview: {\n    type: definePropType<UploadHooks['onPreview']>(Function),\n    default: NOOP,\n  },\n  /**\n   * @description hook function when uploaded successfully\n   */\n  onSuccess: {\n    type: definePropType<UploadHooks['onSuccess']>(Function),\n    default: NOOP,\n  },\n  /**\n   * @description hook function when some progress occurs\n   */\n  onProgress: {\n    type: definePropType<UploadHooks['onProgress']>(Function),\n    default: NOOP,\n  },\n  /**\n   * @description hook function when some errors occurs\n   */\n  onError: {\n    type: definePropType<UploadHooks['onError']>(Function),\n    default: NOOP,\n  },\n  /**\n   * @description hook function when limit is exceeded\n   */\n  onExceed: {\n    type: definePropType<UploadHooks['onExceed']>(Function),\n    default: NOOP,\n  },\n} as const)\n\nexport type UploadProps = ExtractPropTypes<typeof uploadProps>\n\nexport type UploadInstance = InstanceType<typeof Upload>\n"], "names": [], "mappings": ";;;;;;AAGY,MAAC,eAAe,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE;AACnE,IAAI,MAAM,GAAG,CAAC,CAAC;AACH,MAAC,SAAS,GAAG,MAAM,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,GAAG;AACzC,MAAC,eAAe,GAAG,UAAU,CAAC;AAC1C,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,MAAM;AACnB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;AACrD,IAAI,OAAO,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC;AAC9B,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,MAAM;AACnB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,eAAe,EAAE,OAAO;AAC1B,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC;AAC9B,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,eAAe;AAC3B,IAAI,OAAO,EAAE,MAAM;AACnB,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,IAAI,OAAO,EAAE,UAAU;AACvB,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,KAAK,EAAE,MAAM;AACf,CAAC,EAAE;AACS,MAAC,WAAW,GAAG,UAAU,CAAC;AACtC,EAAE,GAAG,eAAe;AACpB,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,CAAC;;;;"}