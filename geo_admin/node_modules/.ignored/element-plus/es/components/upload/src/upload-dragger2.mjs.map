{"version": 3, "file": "upload-dragger2.mjs", "sources": ["../../../../../../packages/components/upload/src/upload-dragger.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[ns.b('dragger'), ns.is('dragover', dragover)]\"\n    @drop.prevent=\"onDrop\"\n    @dragover.prevent=\"onDragover\"\n    @dragleave.prevent=\"dragover = false\"\n  >\n    <slot />\n  </div>\n</template>\n<script lang=\"ts\" setup>\nimport { inject, ref } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useFormDisabled } from '@element-plus/components/form'\nimport { throwError } from '@element-plus/utils/error'\nimport { uploadContextKey } from './constants'\nimport { uploadDraggerEmits, uploadDraggerProps } from './upload-dragger'\n\nconst COMPONENT_NAME = 'ElUploadDrag'\n\ndefineOptions({\n  name: COMPONENT_NAME,\n})\n\ndefineProps(uploadDraggerProps)\nconst emit = defineEmits(uploadDraggerEmits)\n\nconst uploaderContext = inject(uploadContextKey)\nif (!uploaderContext) {\n  throwError(\n    COMPONENT_NAME,\n    'usage: <el-upload><el-upload-dragger /></el-upload>'\n  )\n}\n\nconst ns = useNamespace('upload')\nconst dragover = ref(false)\nconst disabled = useFormDisabled()\n\nconst onDrop = (e: DragEvent) => {\n  if (disabled.value) return\n  dragover.value = false\n\n  e.stopPropagation()\n\n  const files = Array.from(e.dataTransfer!.files)\n  const accept = uploaderContext.accept.value\n  if (!accept) {\n    emit('file', files)\n    return\n  }\n\n  const filesFiltered = files.filter((file) => {\n    const { type, name } = file\n    const extension = name.includes('.') ? `.${name.split('.').pop()}` : ''\n    const baseType = type.replace(/\\/.*$/, '')\n    return accept\n      .split(',')\n      .map((type) => type.trim())\n      .filter((type) => type)\n      .some((acceptedType) => {\n        if (acceptedType.startsWith('.')) {\n          return extension === acceptedType\n        }\n        if (/\\/\\*$/.test(acceptedType)) {\n          return baseType === acceptedType.replace(/\\/\\*$/, '')\n        }\n        if (/^[^/]+\\/[^/]+$/.test(acceptedType)) {\n          return type === acceptedType\n        }\n        return false\n      })\n  })\n\n  emit('file', filesFiltered)\n}\n\nconst onDragover = () => {\n  if (!disabled.value) dragover.value = true\n}\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;mCAoBc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR;;;;;;AAKA,IAAM,MAAA,eAAA,GAAkB,OAAO,gBAAgB,CAAA,CAAA;AAC/C,IAAA,IAAI,CAAC,eAAiB,EAAA;AACpB,MAAA,UAAA,CACE,gBACA,qDACF,CAAA,CAAA;AAAA,KACF;AAEA,IAAM,MAAA,EAAA,GAAK,aAAa,QAAQ,CAAA,CAAA;AAChC,IAAM,MAAA,QAAA,GAAW,IAAI,KAAK,CAAA,CAAA;AAC1B,IAAA,MAAM,WAAW,eAAgB,EAAA,CAAA;AAEjC,IAAM,MAAA,MAAA,GAAS,CAAC,CAAiB,KAAA;AAC/B,MAAA,IAAI,QAAS,CAAA,KAAA;AAAO,QAAA,OAAA;AACpB,MAAA,QAAA,CAAS,KAAQ,GAAA,KAAA,CAAA;AAEjB,MAAA,CAAA,CAAE,eAAgB,EAAA,CAAA;AAElB,MAAA,MAAM,KAAQ,GAAA,KAAA,CAAM,IAAK,CAAA,CAAA,CAAE,aAAc,KAAK,CAAA,CAAA;AAC9C,MAAM,MAAA,MAAA,GAAS,gBAAgB,MAAO,CAAA,KAAA,CAAA;AACtC,MAAA,IAAI,CAAC,MAAQ,EAAA;AACX,QAAA,IAAA,CAAK,QAAQ,KAAK,CAAA,CAAA;AAClB,QAAA,OAAA;AAAA,OACF;AAEA,MAAA,MAAM,aAAgB,GAAA,KAAA,CAAM,MAAO,CAAA,CAAC,IAAS,KAAA;AAC3C,QAAM,MAAA,EAAE,MAAM,IAAS,EAAA,GAAA,IAAA,CAAA;AACvB,QAAM,MAAA,SAAA,GAAY,IAAK,CAAA,QAAA,CAAS,GAAG,CAAA,GAAI,CAAI,CAAA,EAAA,IAAA,CAAK,KAAM,CAAA,GAAG,CAAE,CAAA,GAAA,EAAU,CAAA,CAAA,GAAA,EAAA,CAAA;AACrE,QAAA,MAAM,QAAW,GAAA,IAAA,CAAK,OAAQ,CAAA,OAAA,EAAS,EAAE,CAAA,CAAA;AACzC,QAAA,OAAO,OACJ,KAAM,CAAA,GAAG,EACT,GAAI,CAAA,CAAC,UAAS,KAAK,CAAA,IAAA,EAAM,CAAA,CACzB,OAAO,CAAC,KAAA,KAAS,KAAI,CACrB,CAAA,IAAA,CAAK,CAAC,YAAiB,KAAA;AACtB,UAAI,IAAA,YAAA,CAAa,UAAW,CAAA,GAAG,CAAG,EAAA;AAChC,YAAA,OAAO,SAAc,KAAA,YAAA,CAAA;AAAA,WACvB;AACA,UAAI,IAAA,OAAA,CAAQ,IAAK,CAAA,YAAY,CAAG,EAAA;AAC9B,YAAA,OAAO,QAAa,KAAA,YAAA,CAAa,OAAQ,CAAA,OAAA,EAAS,EAAE,CAAA,CAAA;AAAA,WACtD;AACA,UAAI,IAAA,gBAAA,CAAiB,IAAK,CAAA,YAAY,CAAG,EAAA;AACvC,YAAA,OAAO,IAAS,KAAA,YAAA,CAAA;AAAA,WAClB;AACA,UAAO,OAAA,KAAA,CAAA;AAAA,SACR,CAAA,CAAA;AAAA,OACJ,CAAA,CAAA;AAED,MAAA,IAAA,CAAK,QAAQ,aAAa,CAAA,CAAA;AAAA,KAC5B,CAAA;AAEA,IAAA,MAAM,aAAa,MAAM;AACvB,MAAA,IAAI,CAAC,QAAS,CAAA,KAAA;AAAO,QAAA,QAAA,CAAS,KAAQ,GAAA,IAAA,CAAA;AAAA,KACxC,CAAA;;;;;;;;;;;;;;;;;"}