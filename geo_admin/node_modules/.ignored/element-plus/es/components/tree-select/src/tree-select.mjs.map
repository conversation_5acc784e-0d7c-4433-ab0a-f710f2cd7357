{"version": 3, "file": "tree-select.mjs", "sources": ["../../../../../../packages/components/tree-select/src/tree-select.vue"], "sourcesContent": ["<script lang=\"ts\">\n// @ts-nocheck\nimport { computed, defineComponent, h, onMounted, reactive, ref } from 'vue'\nimport { pick } from 'lodash-unified'\nimport ElSelect from '@element-plus/components/select'\nimport ElTree from '@element-plus/components/tree'\nimport { useSelect } from './select'\nimport { useTree } from './tree'\nimport CacheOptions from './cache-options'\n\nexport default defineComponent({\n  name: 'ElTreeSelect',\n  // disable `ElSelect` inherit current attrs\n  inheritAttrs: false,\n  props: {\n    ...ElSelect.props,\n    ...ElTree.props,\n    /**\n     * @description The cached data of the lazy node, the structure is the same as the data, used to get the label of the unloaded data\n     */\n    cacheData: {\n      type: Array,\n      default: () => [],\n    },\n  },\n  setup(props, context) {\n    const { slots, expose } = context\n\n    const select = ref<InstanceType<typeof ElSelect>>()\n    const tree = ref<InstanceType<typeof ElTree>>()\n\n    const key = computed(() => props.nodeKey || props.valueKey || 'value')\n\n    const selectProps = useSelect(props, context, { select, tree, key })\n    const { cacheOptions, ...treeProps } = useTree(props, context, {\n      select,\n      tree,\n      key,\n    })\n\n    // expose ElTree/ElSelect methods\n    const methods = reactive({})\n    expose(methods)\n    onMounted(() => {\n      Object.assign(methods, {\n        ...pick(tree.value, [\n          'filter',\n          'updateKeyChildren',\n          'getCheckedNodes',\n          'setCheckedNodes',\n          'getCheckedKeys',\n          'setCheckedKeys',\n          'setChecked',\n          'getHalfCheckedNodes',\n          'getHalfCheckedKeys',\n          'getCurrentKey',\n          'getCurrentNode',\n          'setCurrentKey',\n          'setCurrentNode',\n          'getNode',\n          'remove',\n          'append',\n          'insertBefore',\n          'insertAfter',\n        ]),\n        ...pick(select.value, ['focus', 'blur']),\n      })\n    })\n\n    return () =>\n      h(\n        ElSelect,\n        /**\n         * 1. The `props` is processed into `Refs`, but `v-bind` and\n         * render function props cannot read `Refs`, so use `reactive`\n         * unwrap the `Refs` and keep reactive.\n         * 2. The keyword `ref` requires `Ref`, but `reactive` broke it,\n         * so use function.\n         */\n        reactive({\n          ...selectProps,\n          ref: (ref) => (select.value = ref),\n        }),\n        {\n          ...slots,\n          default: () => [\n            h(CacheOptions, { data: cacheOptions.value }),\n            h(\n              ElTree,\n              reactive({\n                ...treeProps,\n                ref: (ref) => (tree.value = ref),\n              })\n            ),\n          ],\n        }\n      )\n  },\n})\n</script>\n"], "names": ["ElTree"], "mappings": ";;;;;;;;;AAUA,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,cAAA;AAAA,EAEN,YAAc,EAAA,KAAA;AAAA,EACd,KAAO,EAAA;AAAA,IACL,GAAG,QAAS,CAAA,KAAA;AAAA,IACZ,GAAGA,KAAO,CAAA,KAAA;AAAA,IAIV,SAAW,EAAA;AAAA,MACT,IAAM,EAAA,KAAA;AAAA,MACN,OAAA,EAAS,MAAM,EAAC;AAAA,KAClB;AAAA,GACF;AAAA,EACA,KAAA,CAAM,OAAO,OAAS,EAAA;AACpB,IAAM,MAAA,EAAE,OAAO,MAAW,EAAA,GAAA,OAAA,CAAA;AAE1B,IAAA,MAAM,SAAS,GAAmC,EAAA,CAAA;AAClD,IAAA,MAAM,OAAO,GAAiC,EAAA,CAAA;AAE9C,IAAA,MAAM,MAAM,QAAS,CAAA,MAAM,MAAM,OAAW,IAAA,KAAA,CAAM,YAAY,OAAO,CAAA,CAAA;AAErE,IAAM,MAAA,WAAA,GAAc,UAAU,KAAO,EAAA,OAAA,EAAS,EAAE,MAAQ,EAAA,IAAA,EAAM,KAAK,CAAA,CAAA;AACnE,IAAA,MAAM,EAAE,YAAA,EAAA,GAAiB,SAAc,EAAA,GAAA,OAAA,CAAQ,OAAO,OAAS,EAAA;AAAA,MAC7D,MAAA;AAAA,MACA,IAAA;AAAA,MACA,GAAA;AAAA,KACD,CAAA,CAAA;AAGD,IAAM,MAAA,OAAA,GAAU,QAAS,CAAA,EAAE,CAAA,CAAA;AAC3B,IAAA,MAAA,CAAO,OAAO,CAAA,CAAA;AACd,IAAA,SAAA,CAAU,MAAM;AACd,MAAA,MAAA,CAAO,OAAO,OAAS,EAAA;AAAA,QACrB,GAAG,IAAK,CAAA,IAAA,CAAK,KAAO,EAAA;AAAA,UAClB,QAAA;AAAA,UACA,mBAAA;AAAA,UACA,iBAAA;AAAA,UACA,iBAAA;AAAA,UACA,gBAAA;AAAA,UACA,gBAAA;AAAA,UACA,YAAA;AAAA,UACA,qBAAA;AAAA,UACA,oBAAA;AAAA,UACA,eAAA;AAAA,UACA,gBAAA;AAAA,UACA,eAAA;AAAA,UACA,gBAAA;AAAA,UACA,SAAA;AAAA,UACA,QAAA;AAAA,UACA,QAAA;AAAA,UACA,cAAA;AAAA,UACA,aAAA;AAAA,SACD,CAAA;AAAA,QACD,GAAG,IAAK,CAAA,MAAA,CAAO,OAAO,CAAC,OAAA,EAAS,MAAM,CAAC,CAAA;AAAA,OACxC,CAAA,CAAA;AAAA,KACF,CAAA,CAAA;AAED,IAAO,OAAA,MACL,CACE,CAAA,QAAA,EAQA,QAAS,CAAA;AAAA,MACP,GAAG,WAAA;AAAA,MACH,GAAK,EAAA,CAAC,IAAS,KAAA,MAAA,CAAO,KAAQ,GAAA,IAAA;AAAA,KAC/B,CACD,EAAA;AAAA,MACE,GAAG,KAAA;AAAA,MACH,SAAS,MAAM;AAAA,QACb,EAAE,YAAc,EAAA,EAAE,IAAM,EAAA,YAAA,CAAa,OAAO,CAAA;AAAA,QAC5C,CAAA,CACEA,OACA,QAAS,CAAA;AAAA,UACP,GAAG,SAAA;AAAA,UACH,GAAK,EAAA,CAAC,IAAS,KAAA,IAAA,CAAK,KAAQ,GAAA,IAAA;AAAA,SAC7B,CACH,CAAA;AAAA,OACF;AAAA,KAEJ,CAAA,CAAA;AAAA,GACJ;AACF,CAAC,CAAA,CAAA;;;;;"}