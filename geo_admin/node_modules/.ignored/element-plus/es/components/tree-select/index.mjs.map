{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/tree-select/index.ts"], "sourcesContent": ["import TreeSelect from './src/tree-select.vue'\n\nimport type { App } from 'vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nTreeSelect.install = (app: App): void => {\n  app.component(TreeSelect.name, TreeSelect)\n}\n\nconst _TreeSelect = TreeSelect as SFCWithInstall<typeof TreeSelect>\n\nexport default _TreeSelect\nexport const ElTreeSelect = _TreeSelect\n"], "names": [], "mappings": ";;AACA,UAAU,CAAC,OAAO,GAAG,CAAC,GAAG,KAAK;AAC9B,EAAE,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;AAC7C,CAAC,CAAC;AACG,MAAC,WAAW,GAAG,WAAW;AAEnB,MAAC,YAAY,GAAG;;;;"}