{"version": 3, "file": "tab-pane2.mjs", "sources": ["../../../../../../packages/components/tabs/src/tab-pane.vue"], "sourcesContent": ["<template>\n  <div\n    v-if=\"shouldBeRender\"\n    v-show=\"active\"\n    :id=\"`pane-${paneName}`\"\n    :class=\"ns.b()\"\n    role=\"tabpanel\"\n    :aria-hidden=\"!active\"\n    :aria-labelledby=\"`tab-${paneName}`\"\n  >\n    <slot />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  getCurrentInstance,\n  inject,\n  onMounted,\n  onUnmounted,\n  reactive,\n  ref,\n  useSlots,\n  watch,\n} from 'vue'\nimport { eagerComputed } from '@vueuse/core'\nimport { throwError } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { tabsRootContextKey } from './constants'\nimport { tabPaneProps } from './tab-pane'\n\nconst COMPONENT_NAME = 'ElTabPane'\ndefineOptions({\n  name: COMPONENT_NAME,\n})\nconst props = defineProps(tabPaneProps)\n\nconst instance = getCurrentInstance()!\nconst slots = useSlots()\n\nconst tabsRoot = inject(tabsRootContextKey)\nif (!tabsRoot)\n  throwError(COMPONENT_NAME, 'usage: <el-tabs><el-tab-pane /></el-tabs/>')\n\nconst ns = useNamespace('tab-pane')\n\nconst index = ref<string>()\nconst isClosable = computed(() => props.closable || tabsRoot.props.closable)\nconst active = eagerComputed(\n  () => tabsRoot.currentName.value === (props.name ?? index.value)\n)\nconst loaded = ref(active.value)\nconst paneName = computed(() => props.name ?? index.value)\nconst shouldBeRender = eagerComputed(\n  () => !props.lazy || loaded.value || active.value\n)\n\nwatch(active, (val) => {\n  if (val) loaded.value = true\n})\n\nconst pane = reactive({\n  uid: instance.uid,\n  slots,\n  props,\n  paneName,\n  active,\n  index,\n  isClosable,\n})\n\nonMounted(() => {\n  tabsRoot.registerPane(pane)\n})\n\nonUnmounted(() => {\n  tabsRoot.unregisterPane(pane.uid)\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;mCAiCc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR;;;;;;AAGA,IAAA,MAAM,WAAW,kBAAmB,EAAA,CAAA;AACpC,IAAA,MAAM,QAAQ,QAAS,EAAA,CAAA;AAEvB,IAAM,MAAA,QAAA,GAAW,OAAO,kBAAkB,CAAA,CAAA;AAC1C,IAAA,IAAI,CAAC,QAAA;AACH,MAAA,UAAA,CAAW,gBAAgB,4CAA4C,CAAA,CAAA;AAEzE,IAAM,MAAA,EAAA,GAAK,aAAa,UAAU,CAAA,CAAA;AAElC,IAAA,MAAM,QAAQ,GAAY,EAAA,CAAA;AAC1B,IAAA,MAAM,aAAa,QAAS,CAAA,MAAM,MAAM,QAAY,IAAA,QAAA,CAAS,MAAM,QAAQ,CAAA,CAAA;AAC3E,IAAM,MAAA,MAAA,GAAS,cACb,MAAM;AAER,MAAM,IAAA,EAAA,CAAA;AACN,MAAA,eAAiB,CAAS,WAAA,CAAA,KAAY,MAAA,CAAA,EAAA,QAAc,CAAK,IAAA,KAAA,IAAA,GAAA,EAAA,GAAA,KAAA,CAAA,KAAA,CAAA,CAAA;AACzD,KAAM,CAAA,CAAA;AAIN,IAAM,MAAA,MAAA,GAAS,GAAQ,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA;AACrB,IAAI,MAAA,QAAA,GAAA,QAAA,CAAA,MAAA;AAAK,MAAA,IAAA,EAAA,CAAA;AAAe,MACzB,OAAA,CAAA,EAAA,GAAA,KAAA,CAAA,IAAA,KAAA,IAAA,GAAA,EAAA,GAAA,KAAA,CAAA,KAAA,CAAA;AAED,KAAA,CAAA,CAAA;AAAsB,IAAA,oBACN,GAAA,aAAA,CAAA,MAAA,CAAA,KAAA,CAAA,IAAA,IAAA,MAAA,CAAA,KAAA,IAAA,MAAA,CAAA,KAAA,CAAA,CAAA;AAAA,IACd,KAAA,CAAA,MAAA,EAAA,CAAA,GAAA,KAAA;AAAA,MACA,IAAA,GAAA;AAAA,QACA,MAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAAA,KACA,CAAA,CAAA;AAAA,IACA,MAAA,IAAA,GAAA,QAAA,CAAA;AAAA,MACA,GAAA,EAAA,QAAA,CAAA,GAAA;AAAA,MACD,KAAA;AAED,MAAA,KAAA;AACE,MAAA,QAAA;AAA0B,MAC3B,MAAA;AAED,MAAA,KAAA;AACE,MAAS,UAAA;AAAuB,KACjC,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;"}