import '../../../utils/index.mjs';
import { buildProps } from '../../../utils/vue/props/runtime.mjs';

const tabPaneProps = buildProps({
  label: {
    type: String,
    default: ""
  },
  name: {
    type: [String, Number]
  },
  closable: <PERSON><PERSON><PERSON>,
  disabled: <PERSON><PERSON><PERSON>,
  lazy: <PERSON><PERSON><PERSON>
});

export { tabPaneProps };
//# sourceMappingURL=tab-pane.mjs.map
