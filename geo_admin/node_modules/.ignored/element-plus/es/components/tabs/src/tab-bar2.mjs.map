{"version": 3, "file": "tab-bar2.mjs", "sources": ["../../../../../../packages/components/tabs/src/tab-bar.vue"], "sourcesContent": ["<template>\n  <div\n    ref=\"barRef\"\n    :class=\"[ns.e('active-bar'), ns.is(rootTabs.props.tabPosition)]\"\n    :style=\"barStyle\"\n  />\n</template>\n\n<script lang=\"ts\" setup>\nimport { getCurrentInstance, inject, nextTick, ref, watch } from 'vue'\nimport { useResizeObserver } from '@vueuse/core'\nimport { capitalize, throwError } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { tabsRootContextKey } from './constants'\nimport { tabBarProps } from './tab-bar'\n\nimport type { CSSProperties } from 'vue'\n\nconst COMPONENT_NAME = 'ElTabBar'\ndefineOptions({\n  name: COMPONENT_NAME,\n})\nconst props = defineProps(tabBarProps)\n\nconst instance = getCurrentInstance()!\nconst rootTabs = inject(tabsRootContextKey)\nif (!rootTabs) throwError(COMPONENT_NAME, '<el-tabs><el-tab-bar /></el-tabs>')\n\nconst ns = useNamespace('tabs')\n\nconst barRef = ref<HTMLDivElement>()\nconst barStyle = ref<CSSProperties>()\n\nconst getBarStyle = (): CSSProperties => {\n  let offset = 0\n  let tabSize = 0\n\n  const sizeName = ['top', 'bottom'].includes(rootTabs.props.tabPosition)\n    ? 'width'\n    : 'height'\n  const sizeDir = sizeName === 'width' ? 'x' : 'y'\n  const position = sizeDir === 'x' ? 'left' : 'top'\n\n  props.tabs.every((tab) => {\n    const $el = instance.parent?.refs?.[`tab-${tab.uid}`] as HTMLElement\n    if (!$el) return false\n\n    if (!tab.active) {\n      return true\n    }\n\n    offset = $el[`offset${capitalize(position)}`]\n    tabSize = $el[`client${capitalize(sizeName)}`]\n\n    const tabStyles = window.getComputedStyle($el)\n\n    if (sizeName === 'width') {\n      if (props.tabs.length > 1) {\n        tabSize -=\n          Number.parseFloat(tabStyles.paddingLeft) +\n          Number.parseFloat(tabStyles.paddingRight)\n      }\n      offset += Number.parseFloat(tabStyles.paddingLeft)\n    }\n    return false\n  })\n\n  return {\n    [sizeName]: `${tabSize}px`,\n    transform: `translate${capitalize(sizeDir)}(${offset}px)`,\n  }\n}\n\nconst update = () => (barStyle.value = getBarStyle())\n\nwatch(\n  () => props.tabs,\n  async () => {\n    await nextTick()\n    update()\n  },\n  { immediate: true }\n)\nuseResizeObserver(barRef, () => update())\n\ndefineExpose({\n  /** @description tab root html element */\n  ref: barRef,\n  /** @description method to manually update tab bar style */\n  update,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;mCAmBc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR;;;;;;AAGA,IAAA,MAAM,WAAW,kBAAmB,EAAA,CAAA;AACpC,IAAM,MAAA,QAAA,GAAW,OAAO,kBAAkB,CAAA,CAAA;AAC1C,IAAA,IAAI,CAAC,QAAA;AAAU,MAAA,UAAA,CAAW,gBAAgB,mCAAmC,CAAA,CAAA;AAE7E,IAAM,MAAA,EAAA,GAAK,aAAa,MAAM,CAAA,CAAA;AAE9B,IAAA,MAAM,SAAS,GAAoB,EAAA,CAAA;AACnC,IAAA,MAAM,WAAW,GAAmB,EAAA,CAAA;AAEpC,IAAA,MAAM,cAAc,MAAqB;AACvC,MAAA,IAAI,MAAS,GAAA,CAAA,CAAA;AACb,MAAA,IAAI,OAAU,GAAA,CAAA,CAAA;AAEd,MAAM,MAAA,QAAA,GAAW,CAAC,KAAA,EAAO,QAAQ,CAAA,CAAE,SAAS,QAAS,CAAA,KAAA,CAAM,WAAW,CAAA,GAClE,OACA,GAAA,QAAA,CAAA;AACJ,MAAM,MAAA,OAAA,GAAU,QAAa,KAAA,OAAA,GAAU,GAAM,GAAA,GAAA,CAAA;AAC7C,MAAM,MAAA,QAAA,GAAW,OAAY,KAAA,GAAA,GAAM,MAAS,GAAA,KAAA,CAAA;AAE5C,MAAM,KAAA,CAAA,IAAA,CAAK,KAAM,CAAA,CAAC,GAAQ,KAAA;AACxB,QAAA,IAAA,EAAM,EAAM,EAAA,CAAA;AACZ,QAAA,MAAK,GAAA,GAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,QAAA,CAAA,MAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA,IAAA,EAAA,GAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA;AAAK,QAAO,IAAA,CAAA,GAAA;AAEjB,UAAI,OAAK,KAAQ,CAAA;AACf,QAAO,IAAA,CAAA,GAAA,CAAA,MAAA,EAAA;AAAA,UACT,OAAA,IAAA,CAAA;AAEA,SAAS;AACT,QAAU,MAAA,GAAA,GAAA,CAAA,CAAI,MAAS,EAAA,UAAA,CAAA,QAAmB,CAAA,CAAA,CAAA,CAAA,CAAA;AAE1C,QAAM,OAAA,GAAA,GAAA,CAAA,CAAA,MAAmB,EAAA,UAAA,CAAA,QAAiB,CAAG,CAAA,CAAA,CAAA,CAAA;AAE7C,QAAA,kBAA0B,MAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,CAAA;AACxB,QAAI,IAAA,QAAM,KAAK,OAAA,EAAS;AACtB,UACE,IAAA,KAAA,CAAA,IAAA,CAAA;AACwC,YAC5C,OAAA,IAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,WAAA,CAAA,GAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,YAAA,CAAA,CAAA;AACA,WAAU;AAAuC,UACnD,MAAA,IAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,WAAA,CAAA,CAAA;AACA,SAAO;AAAA,QACR,OAAA,KAAA,CAAA;AAED,OAAO,CAAA,CAAA;AAAA,MACL;AAAe,QACf,CAAW,QAAA,GAAA,CAAA,EAAA,OAAA,CAAY,EAAW,CAAA;AAAY,QAChD,SAAA,EAAA,CAAA,SAAA,EAAA,UAAA,CAAA,OAAA,CAAA,CAAA,CAAA,EAAA,MAAA,CAAA,GAAA,CAAA;AAAA,OACF,CAAA;AAEA,KAAA,CAAA;AAEA,IACE,MAAA,MAAM,GAAM,MAAA,QACA,CAAA,KAAA,GAAA,WAAA,EAAA,CAAA;AACV,IAAA,KAAA,CAAA,MAAe,KAAA,CAAA,IAAA,EAAA,YAAA;AACf,MAAO,MAAA,QAAA,EAAA,CAAA;AAAA,MAET,MAAa,EAAA,CAAA;AAEf,KAAkB,EAAA,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AAElB,IAAa,iBAAA,CAAA,MAAA,EAAA,MAAA,MAAA,EAAA,CAAA,CAAA;AAAA,IAAA,MAEN,CAAA;AAAA,MAEL,GAAA,EAAA,MAAA;AAAA,MACD,MAAA;;;;;;;;;;;;;;;;"}