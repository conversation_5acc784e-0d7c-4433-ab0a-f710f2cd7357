{"version": 3, "file": "tabs.mjs", "sources": ["../../../../../../packages/components/tabs/src/tabs.tsx"], "sourcesContent": ["import {\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  nextTick,\n  provide,\n  ref,\n  renderSlot,\n  watch,\n} from 'vue'\nimport {\n  buildProps,\n  definePropType,\n  isNumber,\n  isString,\n  isUndefined,\n} from '@element-plus/utils'\nimport { EVENT_CODE, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport ElIcon from '@element-plus/components/icon'\nimport { Plus } from '@element-plus/icons-vue'\nimport {\n  useDeprecated,\n  useNamespace,\n  useOrderedChildren,\n} from '@element-plus/hooks'\nimport { tabsRootContextKey } from './constants'\nimport TabNav from './tab-nav'\n\nimport type { TabNavInstance } from './tab-nav'\nimport type { TabsPaneContext } from './constants'\nimport type { ExtractPropTypes } from 'vue'\nimport type { Awaitable } from '@element-plus/utils'\n\nexport type TabPaneName = string | number\n\nexport const tabsProps = buildProps({\n  type: {\n    type: String,\n    values: ['card', 'border-card', ''],\n    default: '',\n  },\n  activeName: {\n    type: [String, Number],\n  },\n  closable: Boolean,\n  addable: Boolean,\n  modelValue: {\n    type: [String, Number],\n  },\n  editable: Boolean,\n  tabPosition: {\n    type: String,\n    values: ['top', 'right', 'bottom', 'left'],\n    default: 'top',\n  },\n  beforeLeave: {\n    type: definePropType<\n      (newName: TabPaneName, oldName: TabPaneName) => Awaitable<void | boolean>\n    >(Function),\n    default: () => true,\n  },\n  stretch: Boolean,\n} as const)\nexport type TabsProps = ExtractPropTypes<typeof tabsProps>\n\nconst isPaneName = (value: unknown): value is string | number =>\n  isString(value) || isNumber(value)\n\nexport const tabsEmits = {\n  [UPDATE_MODEL_EVENT]: (name: TabPaneName) => isPaneName(name),\n  tabClick: (pane: TabsPaneContext, ev: Event) => ev instanceof Event,\n  tabChange: (name: TabPaneName) => isPaneName(name),\n  edit: (paneName: TabPaneName | undefined, action: 'remove' | 'add') =>\n    ['remove', 'add'].includes(action),\n  tabRemove: (name: TabPaneName) => isPaneName(name),\n  tabAdd: () => true,\n}\nexport type TabsEmits = typeof tabsEmits\n\nexport type TabsPanes = Record<number, TabsPaneContext>\n\nconst Tabs = defineComponent({\n  name: 'ElTabs',\n\n  props: tabsProps,\n  emits: tabsEmits,\n\n  setup(props, { emit, slots, expose }) {\n    const ns = useNamespace('tabs')\n\n    const {\n      children: panes,\n      addChild: registerPane,\n      removeChild: unregisterPane,\n    } = useOrderedChildren<TabsPaneContext>(getCurrentInstance()!, 'ElTabPane')\n\n    const nav$ = ref<TabNavInstance>()\n    const currentName = ref<TabPaneName>(\n      props.modelValue ?? props.activeName ?? '0'\n    )\n\n    const setCurrentName = async (value?: TabPaneName, trigger = false) => {\n      // should do nothing.\n      if (currentName.value === value || isUndefined(value)) return\n\n      try {\n        const canLeave = await props.beforeLeave?.(value, currentName.value)\n        if (canLeave !== false) {\n          currentName.value = value\n          if (trigger) {\n            emit(UPDATE_MODEL_EVENT, value)\n            emit('tabChange', value)\n          }\n\n          nav$.value?.removeFocus?.()\n        }\n      } catch {}\n    }\n\n    const handleTabClick = (\n      tab: TabsPaneContext,\n      tabName: TabPaneName,\n      event: Event\n    ) => {\n      if (tab.props.disabled) return\n      setCurrentName(tabName, true)\n      emit('tabClick', tab, event)\n    }\n\n    const handleTabRemove = (pane: TabsPaneContext, ev: Event) => {\n      if (pane.props.disabled || isUndefined(pane.props.name)) return\n      ev.stopPropagation()\n      emit('edit', pane.props.name, 'remove')\n      emit('tabRemove', pane.props.name)\n    }\n\n    const handleTabAdd = () => {\n      emit('edit', undefined, 'add')\n      emit('tabAdd')\n    }\n\n    useDeprecated(\n      {\n        from: '\"activeName\"',\n        replacement: '\"model-value\" or \"v-model\"',\n        scope: 'ElTabs',\n        version: '2.3.0',\n        ref: 'https://element-plus.org/en-US/component/tabs.html#attributes',\n        type: 'Attribute',\n      },\n      computed(() => !!props.activeName)\n    )\n\n    watch(\n      () => props.activeName,\n      (modelValue) => setCurrentName(modelValue)\n    )\n\n    watch(\n      () => props.modelValue,\n      (modelValue) => setCurrentName(modelValue)\n    )\n\n    watch(currentName, async () => {\n      await nextTick()\n      nav$.value?.scrollToActiveTab()\n    })\n\n    provide(tabsRootContextKey, {\n      props,\n      currentName,\n      registerPane,\n      unregisterPane,\n    })\n\n    expose({\n      currentName,\n    })\n\n    return () => {\n      const addSlot = slots.addIcon\n      const newButton =\n        props.editable || props.addable ? (\n          <span\n            class={ns.e('new-tab')}\n            tabindex=\"0\"\n            onClick={handleTabAdd}\n            onKeydown={(ev: KeyboardEvent) => {\n              if (ev.code === EVENT_CODE.enter) handleTabAdd()\n            }}\n          >\n            {addSlot ? (\n              renderSlot(slots, 'addIcon')\n            ) : (\n              <ElIcon class={ns.is('icon-plus')}>\n                <Plus />\n              </ElIcon>\n            )}\n          </span>\n        ) : null\n\n      const header = (\n        <div class={[ns.e('header'), ns.is(props.tabPosition)]}>\n          {newButton}\n          <TabNav\n            ref={nav$}\n            currentName={currentName.value}\n            editable={props.editable}\n            type={props.type}\n            panes={panes.value}\n            stretch={props.stretch}\n            onTabClick={handleTabClick}\n            onTabRemove={handleTabRemove}\n          />\n        </div>\n      )\n\n      const panels = (\n        <div class={ns.e('content')}>{renderSlot(slots, 'default')}</div>\n      )\n\n      return (\n        <div\n          class={[\n            ns.b(),\n            ns.m(props.tabPosition),\n            {\n              [ns.m('card')]: props.type === 'card',\n              [ns.m('border-card')]: props.type === 'border-card',\n            },\n          ]}\n        >\n          {...props.tabPosition !== 'bottom'\n            ? [header, panels]\n            : [panels, header]}\n        </div>\n      )\n    }\n  },\n})\n\nexport type TabsInstance = InstanceType<typeof Tabs> & {\n  currentName: TabPaneName\n}\n\nexport default Tabs\n"], "names": ["tabsProps", "buildProps", "type", "String", "values", "default", "activeName", "Number", "closable", "Boolean", "addable", "modelValue", "editable", "tabPosition", "beforeLeave", "definePropType", "Function", "stretch", "tabsEmits", "name", "tabClick", "ev", "tabChange", "edit", "tabRemove", "tabAdd", "Tabs", "defineComponent", "props", "emits", "slots", "expose", "children", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "unregisterPane", "getCurrentInstance", "nav$", "currentName", "setCurrentName", "value", "canLeave", "emit", "UPDATE_MODEL_EVENT", "handleTabClick", "tab", "tabName", "event", "disabled", "pane", "isUndefined", "handleTabAdd", "useDeprecated", "from", "replacement", "scope", "version", "ref", "computed", "watch", "provide", "_createVNode", "newButton", "code", "enter", "renderSlot", "ns", "is", "panes", "handleTabRemove"], "mappings": ";;;;;;;;;;;;;;;;;AAmCaA,MAAAA,SAAS,GAAGC,UAAU,CAAC;AAClCC,EAAAA,IAAI,EAAE;AACJA,IAAAA,IAAI,EAAEC,MADF;AAEJC,IAAAA,MAAM,EAAE,CAAC,MAAD,EAAS,aAAT,EAAwB,EAAxB,CAFJ;AAGJC,IAAAA,OAAO,EAAE,EAAA;GAJuB;AAMlCC,EAAAA,UAAU,EAAE;AACVJ,IAAAA,IAAI,EAAE,CAACC,MAAD,EAASI,MAAT,CAAA;GAP0B;AASlCC,EAAAA,QAAQ,EAAEC,OATwB;AAUlCC,EAAAA,OAAO,EAAED,OAVyB;AAWlCE,EAAAA,UAAU,EAAE;AACVT,IAAAA,IAAI,EAAE,CAACC,MAAD,EAASI,MAAT,CAAA;GAZ0B;AAclCK,EAAAA,QAAQ,EAAEH,OAdwB;AAelCI,EAAAA,WAAW,EAAE;AACXX,IAAAA,IAAI,EAAEC,MADK;IAEXC,MAAM,EAAE,CAAC,KAAD,EAAQ,OAAR,EAAiB,QAAjB,EAA2B,MAA3B,CAFG;AAGXC,IAAAA,OAAO,EAAE,KAAA;GAlBuB;AAoBlCS,EAAAA,WAAW,EAAE;AACXZ,IAAAA,IAAI,EAAEa,cAAc,CAElBC,QAFkB,CADT;AAIXX,IAAAA,OAAO,EAAE,MAAM,IAAA;GAxBiB;AA0BlCY,EAAAA,OAAO,EAAER,OAAAA;AA1ByB,CAAD,EAA5B;;AA8BS,MAAA,SAAG,GAAA;;AAGnB,EAAO,QAAA,EAAA,CAAMS,aAAY,EAAA,YAAA,KAAA;AACvB,EAAA,qBAAuBC,UAAgC,CAAA,IAAA,CAAA;EACvDC,IAAQ,EAAA,CAAA,QAAE,EAAwBC,MAAcA,gBAFzB,KAAA,CAAA,CAAA,QAAA,CAAA,MAAA,CAAA;AAGvBC,EAAAA,SAAS,EAAGH,CAAD,IAAA,KAAiC,eAHrB,CAAA;AAIvBI,EAAAA,MAAM,EAAA,MAAA;AAENC,EAAAA;AACAC,MAAAA,IAAQ,GAAM,eAAA,CAAA;AAPS,EAAlB,IAAA,EAAA,QAAA;AAaP,EAAMC,KAAAA,EAAAA,SAAOC;AACXR,EAAAA,OAD2B,SAAA;AAG3BS,EAAAA,KAAK,MAHsB,EAAA;AAI3BC,IAAAA;;IAEK;KAAQ;IAAQC,IAAR,EAAA,EAAA,EAAA,CAAA;AAAeC,IAAAA,MAAAA,EAAAA,GAAAA,YAAAA,CAAAA,MAAAA,CAAAA,CAAAA;AAAf,IAAyB,MAAA;AACpC,MAAA,QAAQ,EAAe,KAAA;MAEjB,QAAA,EAAA,YAAA;AACJC,MAAAA,WADI,EAAA,cAAA;AAEJC,KAAAA,GAAAA,kBAFI,CAAA,kBAAA,EAAA,EAAA,WAAA,CAAA,CAAA;AAGJC,IAAAA,MAAAA,IAAAA,GAAW,GAAEC,EAAAA,CAAAA;AAHT,IAAA,uBAIkCC,CAAAA,CAAAA,EAAAA,GAAAA,CAAAA,EAAAA,GAAAA,KAAkB,CAApC,eAJtB,IAAA,GAAA,EAAA,GAAA,KAAA,CAAA,UAAA,KAAA,IAAA,GAAA,EAAA,GAAA,GAAA,CAAA,CAAA;IAMA,MAAMC,cAAN,GAAA,OAAA,KAAA,EAAA,OAAA,GAAA,KAAA,KAAA;AACA,MAAA,IAAMC,GAAW,EAAA,GAAA,EAAA,EAAA,CAAA;;QAIXC,OAAAA;AACJ,MAAA,IAAA;QACID,MAAAA,QAAYE,GAAAA,OAAUA,CAAAA,GAAAA,QAAoB,CAAA,WAAA,KAAS,IAAA,GAAA,KAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA,EAAA,KAAA,EAAA,WAAA,CAAA,KAAA,CAAA,CAAA,CAAA;;UAEnD,WAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AACF,UAAA,IAAMC,OAAQ,EAAA;;YACVA,IAAAA,CAAAA,WAAa,EAAjB,KAAwB,CAAA,CAAA;WACX;;AACX,SAAA;AACEC,OAAAA,CAAAA,OAAAA,CAAI,EAACC;AACLD,OAAAA;AACD,KAAA,CAAA;;UAEDL,GAAI,CAAA,KAAJ,CAAA,QAAA;AACD,QAAA,OAAA;MACF,cAAS,CAAA,OAAA,EAAA,IAAA,CAAA,CAAA;MAfZ,IAAA,CAAA,UAAA,EAAA,GAAA,EAAA,KAAA,CAAA,CAAA;;IAkBA,MAAMO,kBACJC,CAAAA,IACAC,EAFqB,EAAA,KAGrBC;AAEA,MAAA,IAAIF,IAAIjB,CAAJ,KAAUoB,SAAU,IAAA,WAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA;AACxBT,QAAAA,OAAAA;AACAG,MAAAA,EAAAA,CAAAA,eAAI,EAAA,CAAA;MAPN,IAAA,CAAA,MAAA,EAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,QAAA,CAAA,CAAA;;AAUA,KAAA,CAAA;AACE,IAAA,MAAIO,qBAAuBC;AAC3B7B,MAAAA,IAAA,CAAA,MAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,CAAA;MACAqB,IAAI,CAAC,QAAQO,CAAI,CAAA;MACjBP;IACD,aALD,CAAA;;MAOMS,WAAAA,EAAAA,4BAAqB;AACzBT,MAAAA,KAAK,EAAD,QAAA;MACJA,gBAAA;MAFF,GAAA,EAAA,+DAAA;;AAKAU,KAAAA,EAAAA,QAAAA,CAAAA,MACE,CAAA,CAAA,KAAA,CAAA,UAAA,CAAA,CAAA,CAAA;AACEC,IAAAA,KAAAA,CAAI,MADN,KAAA,CAAA,UAAA,EAAA,CAAA,UAAA,KAAA,cAAA,CAAA,UAAA,CAAA,CAAA,CAAA;AAEEC,IAAAA,KAAAA,CAAAA,MAAAA,KAFF,CAAA,UAAA,EAAA,CAAA,UAAA,KAAA,cAAA,CAAA,UAAA,CAAA,CAAA,CAAA;AAGEC,IAAAA,KAAAA,CAAAA,WAHF,EAAA,YAAA;AAIEC,MAAAA,IAAAA,GAAO;AACPC,MAAAA,MALF,QAAA,EAAA,CAAA;AAMEvD,MAAAA,CAAAA,GAAI,GAAE,IAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,GAAA,CAAA,iBAAA,EAAA,CAAA;KAPG,CAAA,CASXwD;AAGFC,IAAAA,OACE,CAAA,kBADG,EAAA;AAKLA,MAAAA,KACE;MAIG,WAAA;AACH,MAAA;MACAtB,cAAA;AACD,KAHI,CAAL,CAAA;IAKAuB,MAAO,CAAA;MACLhC,WAD0B;MAE1BU,CAF0B;WAAA,MAAA;AAI1BH,MAAAA,MAAAA,OAAAA,GAAAA,KAAAA,CAAAA,OAAAA,CAAAA;AAJ0B,MAA5B,MAAA,SAAA,GAAA,KAAA,CAAA,QAAA,IAAA,KAAA,CAAA,OAAA,GAAA0B,WAAA,CAAA,MAAA,EAAA;AAOA9B,QAAAA,OAAO,EAAA,EAAA,CAAA,CAAA,CAAA,SAAA,CAAA;AACLO,QAAAA,UAAAA,EAAAA,GAAAA;AADK,QAAP,SAAA,EAAA,YAAA;AAIA,QAAA,WAAa,EAAA,CAAA,EAAA,KAAA;AACX,UAAA,IAAa,EAAA,CAAA,IAAA,KAAQ,UAArB,CAAA,KAAA;YACMwB,YACJlC,EAAK,CAAA;AAAL,SAAA;AAAA,OAAA,EAAA,CAAA,OAAA,GAGa,UAHb,CAAA,KAAA,EAAA,SAAA,CAAA,GAAAiC,WAAA,CAAA,MAAA,EAAA;AAAA,QAAA,OAAA,EAAA,EAIaV,CAJb,EAAA,CAAA,WAAA,CAAA;AAAA,OAAA,EAAA;eAMU9B,EAAG0C,MAAH,CAAAF,WAAsB,CAACG,IAAAA,MAAmB,EAAA,IAAA,CAAA,CAAA;AAC/C,OAAA,CAAA,CAAA,CAAA,GAAA,IAAA,CAAA;AAPL,MAAA,YAUMC,GAAAA,WAAU,CAAA;AADJ,QAAA,OAAA,EAGSC,CAAE,EAACC,CAAH,CAAA,CAAM,QAAN,CAAA,EAAA,EAAA,CAAA,EAAA,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA;AAHT,OAAA,EAAA,CAAA,SAAA,EAAAN,WAAA,CAAA,MAAA,EAAA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QATZ,aADF,EAAA,WAAA,CAAA,KAAA;;AAoBA,QAAA,MAAY,EAAA,KAAA,CAAA,IAAA;AAAA,QAAA,OAAA,EACE,KAAC,CAAK,KAAL;AADH,QAAA,SAAA,EAAA,KAAA,CAAA,OAAA;AAAA,QAAA,YAAA,EAAA,cAAA;QAAA,aAKOvB,EAAAA,eALP;SAMIV,IAAAA,CAAAA,CAAAA,CAAAA,CAAAA;YACJA,MAAAA,GAAKiC,WAPL,CAAA,KAAA,EAAA;QAAA,OAQCO,EAAAA,EAAAA,CAAAA,CAAAA,CAAK,SARN,CAAA;SASGxC,CAAAA,UAAAA,CAAAA,KATH,EAAA,SAAA,CAAA,CAAA,CAAA,CAAA;AAAA,MAAA,OAAAiC,WAAA,CAAA,KAAA,EAAA;QAAA,OAWOQ,EAAAA,CAAAA,EAAAA,CAAAA,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAAA,CAAAA,KAAAA,CAAAA,WAAAA,CAAAA,EAAAA;UAXnB,CAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,GAAA,KAAA,CAAA,IAAA,KAAA,MAAA;;AAgBA,SAAA,CAAA;AAAY,OAAA,EAAA,CAAA,GAAA,KACI,CAAA,WAAF,KAAA,QAAA,GAAA,CAAA,MAAA,EAAA,MAAA,CAAA,GAAA,CAAA,MAAA,EAAA,MAAA,CAAA,CAAA,CAAA,CAAA;AADF,KAAA,CAAA;;AAIZ,CAAA;;;;"}