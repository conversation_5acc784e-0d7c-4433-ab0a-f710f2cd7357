export declare const ElTabs: import("element-plus/es/utils").SFCWithInstall<import("vue").DefineComponent<{
    readonly type: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "" | "card" | "border-card", unknown, "", boolean>;
    readonly activeName: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly closable: BooleanConstructor;
    readonly addable: BooleanConstructor;
    readonly modelValue: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly editable: BooleanConstructor;
    readonly tabPosition: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "top" | "bottom" | "right" | "left", unknown, "top", boolean>;
    readonly beforeLeave: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (newName: import("./src/tabs").TabPaneName, oldName: import("./src/tabs").TabPaneName) => import("element-plus/es/utils").Awaitable<boolean | void>) | (() => (newName: import("./src/tabs").TabPaneName, oldName: import("./src/tabs").TabPaneName) => import("element-plus/es/utils").Awaitable<boolean | void>) | {
        (): (newName: import("./src/tabs").TabPaneName, oldName: import("./src/tabs").TabPaneName) => import("element-plus/es/utils").Awaitable<boolean | void>;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (newName: import("./src/tabs").TabPaneName, oldName: import("./src/tabs").TabPaneName) => import("element-plus/es/utils").Awaitable<boolean | void>) | (() => (newName: import("./src/tabs").TabPaneName, oldName: import("./src/tabs").TabPaneName) => import("element-plus/es/utils").Awaitable<boolean | void>) | {
        (): (newName: import("./src/tabs").TabPaneName, oldName: import("./src/tabs").TabPaneName) => import("element-plus/es/utils").Awaitable<boolean | void>;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => true, boolean>;
    readonly stretch: BooleanConstructor;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    "update:modelValue": (name: import("./src/tabs").TabPaneName) => boolean;
    tabClick: (pane: {
        uid: number;
        slots: Readonly<{
            [name: string]: import("vue").Slot | undefined;
        }>;
        props: {
            readonly closable: boolean;
            readonly label: string;
            readonly disabled: boolean;
            readonly lazy: boolean;
            readonly name?: import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown> | undefined;
        };
        paneName: string | number | undefined;
        active: boolean;
        index: string | undefined;
        isClosable: boolean;
    }, ev: Event) => boolean;
    tabChange: (name: import("./src/tabs").TabPaneName) => boolean;
    edit: (paneName: import("./src/tabs").TabPaneName | undefined, action: "add" | "remove") => boolean;
    tabRemove: (name: import("./src/tabs").TabPaneName) => boolean;
    tabAdd: () => boolean;
}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    readonly type: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "" | "card" | "border-card", unknown, "", boolean>;
    readonly activeName: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly closable: BooleanConstructor;
    readonly addable: BooleanConstructor;
    readonly modelValue: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly editable: BooleanConstructor;
    readonly tabPosition: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "top" | "bottom" | "right" | "left", unknown, "top", boolean>;
    readonly beforeLeave: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => (newName: import("./src/tabs").TabPaneName, oldName: import("./src/tabs").TabPaneName) => import("element-plus/es/utils").Awaitable<boolean | void>) | (() => (newName: import("./src/tabs").TabPaneName, oldName: import("./src/tabs").TabPaneName) => import("element-plus/es/utils").Awaitable<boolean | void>) | {
        (): (newName: import("./src/tabs").TabPaneName, oldName: import("./src/tabs").TabPaneName) => import("element-plus/es/utils").Awaitable<boolean | void>;
        new (): any;
        readonly prototype: any;
    } | ((new (...args: any[]) => (newName: import("./src/tabs").TabPaneName, oldName: import("./src/tabs").TabPaneName) => import("element-plus/es/utils").Awaitable<boolean | void>) | (() => (newName: import("./src/tabs").TabPaneName, oldName: import("./src/tabs").TabPaneName) => import("element-plus/es/utils").Awaitable<boolean | void>) | {
        (): (newName: import("./src/tabs").TabPaneName, oldName: import("./src/tabs").TabPaneName) => import("element-plus/es/utils").Awaitable<boolean | void>;
        new (): any;
        readonly prototype: any;
    })[], unknown, unknown, () => true, boolean>;
    readonly stretch: BooleanConstructor;
}>> & {
    "onUpdate:modelValue"?: ((name: import("./src/tabs").TabPaneName) => any) | undefined;
    onTabClick?: ((pane: {
        uid: number;
        slots: Readonly<{
            [name: string]: import("vue").Slot | undefined;
        }>;
        props: {
            readonly closable: boolean;
            readonly label: string;
            readonly disabled: boolean;
            readonly lazy: boolean;
            readonly name?: import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown> | undefined;
        };
        paneName: string | number | undefined;
        active: boolean;
        index: string | undefined;
        isClosable: boolean;
    }, ev: Event) => any) | undefined;
    onTabChange?: ((name: import("./src/tabs").TabPaneName) => any) | undefined;
    onEdit?: ((paneName: import("./src/tabs").TabPaneName | undefined, action: "add" | "remove") => any) | undefined;
    onTabRemove?: ((name: import("./src/tabs").TabPaneName) => any) | undefined;
    onTabAdd?: (() => any) | undefined;
}, {
    readonly type: import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "card" | "border-card", unknown>;
    readonly closable: boolean;
    readonly stretch: boolean;
    readonly editable: boolean;
    readonly beforeLeave: (newName: import("./src/tabs").TabPaneName, oldName: import("./src/tabs").TabPaneName) => import("element-plus/es/utils").Awaitable<boolean | void>;
    readonly tabPosition: import("element-plus/es/utils").EpPropMergeType<StringConstructor, "top" | "bottom" | "right" | "left", unknown>;
    readonly addable: boolean;
}>> & {
    TabPane: import("vue").DefineComponent<{
        readonly label: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
        readonly name: {
            readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly closable: BooleanConstructor;
        readonly disabled: BooleanConstructor;
        readonly lazy: BooleanConstructor;
    }, {
        COMPONENT_NAME: string;
        props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
            readonly label: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
            readonly name: {
                readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly closable: BooleanConstructor;
            readonly disabled: BooleanConstructor;
            readonly lazy: BooleanConstructor;
        }>> & {
            [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
        }>>;
        instance: import("vue").ComponentInternalInstance;
        slots: Readonly<{
            [name: string]: import("vue").Slot | undefined;
        }>;
        tabsRoot: import("./src/constants").TabsRootContext;
        ns: {
            namespace: import("vue").ComputedRef<string>;
            b: (blockSuffix?: string) => string;
            e: (element?: string | undefined) => string;
            m: (modifier?: string | undefined) => string;
            be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
            em: (element?: string | undefined, modifier?: string | undefined) => string;
            bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
            bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        index: import("vue").Ref<string | undefined>;
        isClosable: import("vue").ComputedRef<boolean>;
        active: Readonly<import("vue").Ref<boolean>>;
        loaded: import("vue").Ref<boolean>;
        paneName: import("vue").ComputedRef<import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown> | undefined>;
        shouldBeRender: Readonly<import("vue").Ref<boolean>>;
        pane: {
            uid: number;
            slots: Readonly<{
                [name: string]: import("vue").Slot | undefined;
            }>;
            props: {
                [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
                readonly closable: boolean;
                readonly label: string;
                readonly disabled: boolean;
                readonly name: import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown> | undefined;
                readonly lazy: boolean;
            };
            paneName: import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown> | undefined;
            active: boolean;
            index: string | undefined;
            isClosable: boolean;
        };
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
        readonly label: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
        readonly name: {
            readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly closable: BooleanConstructor;
        readonly disabled: BooleanConstructor;
        readonly lazy: BooleanConstructor;
    }>>, {
        readonly closable: boolean;
        readonly label: string;
        readonly disabled: boolean;
        readonly lazy: boolean;
    }>;
};
export declare const ElTabPane: import("element-plus/es/utils").SFCWithInstall<import("vue").DefineComponent<{
    readonly label: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
    readonly name: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly closable: BooleanConstructor;
    readonly disabled: BooleanConstructor;
    readonly lazy: BooleanConstructor;
}, {
    COMPONENT_NAME: string;
    props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
        readonly label: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
        readonly name: {
            readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly closable: BooleanConstructor;
        readonly disabled: BooleanConstructor;
        readonly lazy: BooleanConstructor;
    }>> & {
        [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
    }>>;
    instance: import("vue").ComponentInternalInstance;
    slots: Readonly<{
        [name: string]: import("vue").Slot | undefined;
    }>;
    tabsRoot: import("./src/constants").TabsRootContext;
    ns: {
        namespace: import("vue").ComputedRef<string>;
        b: (blockSuffix?: string) => string;
        e: (element?: string | undefined) => string;
        m: (modifier?: string | undefined) => string;
        be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
        em: (element?: string | undefined, modifier?: string | undefined) => string;
        bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
        bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
        is: {
            (name: string, state: boolean | undefined): string;
            (name: string): string;
        };
        cssVar: (object: Record<string, string>) => Record<string, string>;
        cssVarName: (name: string) => string;
        cssVarBlock: (object: Record<string, string>) => Record<string, string>;
        cssVarBlockName: (name: string) => string;
    };
    index: import("vue").Ref<string | undefined>;
    isClosable: import("vue").ComputedRef<boolean>;
    active: Readonly<import("vue").Ref<boolean>>;
    loaded: import("vue").Ref<boolean>;
    paneName: import("vue").ComputedRef<import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown> | undefined>;
    shouldBeRender: Readonly<import("vue").Ref<boolean>>;
    pane: {
        uid: number;
        slots: Readonly<{
            [name: string]: import("vue").Slot | undefined;
        }>;
        props: {
            [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
            readonly closable: boolean;
            readonly label: string;
            readonly disabled: boolean;
            readonly name: import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown> | undefined;
            readonly lazy: boolean;
        };
        paneName: import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown> | undefined;
        active: boolean;
        index: string | undefined;
        isClosable: boolean;
    };
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    readonly label: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
    readonly name: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly closable: BooleanConstructor;
    readonly disabled: BooleanConstructor;
    readonly lazy: BooleanConstructor;
}>>, {
    readonly closable: boolean;
    readonly label: string;
    readonly disabled: boolean;
    readonly lazy: boolean;
}>>;
export default ElTabs;
export * from './src/tabs';
export * from './src/tab-bar';
export * from './src/tab-nav';
export * from './src/tab-pane';
export * from './src/constants';
