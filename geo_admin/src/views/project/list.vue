<template>
  <div class="project-list">
    <div class="operation-bar">
      <el-button type="primary" @click="createProject">新建项目</el-button>
      <el-button type="danger" @click="deleteSelected" :disabled="!selectedProjects.length">批量删除</el-button>
    </div>

    <el-table
      :data="projects"
      style="width: 100%"
      @selection-change="handleSelectionChange"
      border
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="项目编号" width="100" />
      <el-table-column prop="name" label="项目名称" width="180">
        <template #default="scope">
          <el-input
            v-if="scope.row.editing"
            v-model="scope.row.editName"
            size="small"
            @blur="saveProjectName(scope.row)"
            @keyup.enter="saveProjectName(scope.row)"
          />
          <span v-else @dblclick="editProjectName(scope.row)">{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="180" />
      <el-table-column prop="lastCollectTime" label="最后采集时间" width="180" />
      <el-table-column prop="promptCount" label="提示词数量" width="100" />
      <el-table-column prop="collectCount" label="采集轮次" width="100" />
      <el-table-column label="操作">
        <template #default="scope">
          <el-button type="primary" size="small" @click="viewProject(scope.row)">查看详情</el-button>
          <el-button type="success" size="small" @click="collectProject(scope.row)">采集</el-button>
          <el-button type="warning" size="small" @click="editProjectName(scope.row)">重命名</el-button>
          <el-button type="danger" size="small" @click="deleteProject(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-if="total > 0"
      :current-page="currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      class="pagination"
    />

    <!-- 新建项目对话框 -->
    <el-dialog v-model="createDialog" title="新建项目" width="40%">
      <el-form :model="newProject" label-width="120px">
        <el-form-item label="项目名称">
          <el-input v-model="newProject.name" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="提示词集合">
          <el-select v-model="newProject.collectionId" placeholder="请选择提示词集合" style="width: 100%">
            <el-option
              v-for="item in collections"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="外部提示词">
          <el-input
            v-model="newProject.externalPrompts"
            type="textarea"
            :rows="5"
            placeholder="请输入外部提示词，每行一个"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="createDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmCreate">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 项目详情对话框 -->
    <el-dialog v-model="detailDialog" :title="`项目详情: ${currentProject.name}`" width="70%">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="基本信息" name="info">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="项目编号">{{ currentProject.id }}</el-descriptions-item>
            <el-descriptions-item label="项目名称">{{ currentProject.name }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ currentProject.createTime }}</el-descriptions-item>
            <el-descriptions-item label="最后采集时间">{{ currentProject.lastCollectTime || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="提示词数量">{{ currentProject.promptCount }}</el-descriptions-item>
            <el-descriptions-item label="采集轮次">{{ currentProject.collectCount }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        
        <el-tab-pane label="提示词列表" name="prompts">
          <el-table :data="projectPrompts" style="width: 100%" border>
            <el-table-column type="index" width="50" />
            <el-table-column prop="content" label="提示词内容" />
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button type="primary" size="small" @click="copyPrompt(scope.row)">复制</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        
        <el-tab-pane label="采集记录" name="collections">
          <el-table :data="collectRecords" style="width: 100%" border>
            <el-table-column prop="roundId" label="轮次ID" width="80" />
            <el-table-column prop="platform" label="采集平台" width="120" />
            <el-table-column prop="startTime" label="开始时间" width="180" />
            <el-table-column prop="endTime" label="结束时间" width="180" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.status === '完成' ? 'success' : 'warning'">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                <el-button type="primary" size="small" @click="viewCollectDetail(scope.row)">查看详情</el-button>
                <el-button type="success" size="small" @click="viewSnapshot(scope.row)">查看快照</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <!-- 采集配置对话框 -->
    <el-dialog v-model="collectDialog" title="采集配置" width="50%">
      <el-form :model="collectForm" label-width="120px">
        <el-form-item label="采集平台">
          <el-select v-model="collectForm.platform" placeholder="请选择采集平台">
            <el-option label="DeepSeek" value="deepseek" />
            <el-option label="百度" value="baidu" />
            <el-option label="克洛诺斯" value="kimi" />
            <el-option label="文心一言" value="wenxin" />
            <el-option label="智谱" value="zhipu" />
            <el-option label="通义" value="tongyi" />
            <el-option label="讯飞" value="xunfei" />
            <el-option label="ChatGPT" value="chatgpt" />
          </el-select>
        </el-form-item>
        <el-form-item label="采集条件范围">
          <el-radio-group v-model="collectForm.mode">
            <el-radio label="deep">深度模式</el-radio>
            <el-radio label="shallow">非深度模式</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="采集优先级">
          <el-select v-model="collectForm.priority" placeholder="请选择采集优先级">
            <el-option label="高" value="high" />
            <el-option label="中" value="medium" />
            <el-option label="低" value="low" />
          </el-select>
        </el-form-item>
        <el-form-item label="采集数量">
          <el-radio-group v-model="collectForm.countType">
            <el-radio label="new">对应创建1个采集轮次</el-radio>
            <el-radio label="all">10次创建10个</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="collectDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmCollect">开始采集</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      projects: [],
      selectedProjects: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      
      // 对话框控制
      createDialog: false,
      detailDialog: false,
      collectDialog: false,
      
      // 当前选中的项目
      currentProject: {},
      activeTab: 'info',
      
      // 项目提示词列表
      projectPrompts: [],
      
      // 采集记录
      collectRecords: [],
      
      // 表单数据
      newProject: {
        name: '',
        collectionId: '',
        externalPrompts: ''
      },
      collectForm: {
        platform: 'deepseek',
        mode: 'deep',
        priority: 'medium',
        countType: 'new'
      },
      
      // 提示词集合列表
      collections: []
    }
  },
  mounted() {
    this.fetchProjects()
    this.fetchCollections()
  },
  methods: {
    // 获取项目列表
    fetchProjects() {
      this.$http.get('/projects', {
        params: {
          page: this.currentPage,
          pageSize: this.pageSize
        }
      }).then(res => {
        if (res.data.code === 0) {
          this.projects = res.data.data.list.map(item => ({
            ...item,
            editing: false,
            editName: item.name
          }))
          this.total = res.data.data.total
        } else {
          this.$message.error(res.data.msg || '获取项目列表失败')
        }
      }).catch(err => {
        console.error(err)
        this.$message.error('获取项目列表失败')
      })
    },
    
    // 获取提示词集合列表
    fetchCollections() {
      this.$http.get('/prompts/collections/all').then(res => {
        if (res.data.code === 0) {
          this.collections = res.data.data
        } else {
          this.$message.error(res.data.msg || '获取提示词集合失败')
        }
      }).catch(err => {
        console.error(err)
        this.$message.error('获取提示词集合失败')
      })
    },
    
    // 分页处理
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchProjects()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchProjects()
    },
    
    // 表格选择处理
    handleSelectionChange(val) {
      this.selectedProjects = val
    },
    
    // 项目操作
    createProject() {
      this.newProject = {
        name: '',
        collectionId: '',
        externalPrompts: ''
      }
      this.createDialog = true
    },
    confirmCreate() {
      if (!this.newProject.name) {
        this.$message.error('请输入项目名称')
        return
      }
      
      if (!this.newProject.collectionId && !this.newProject.externalPrompts) {
        this.$message.error('请选择提示词集合或输入外部提示词')
        return
      }
      
      this.$http.post('/projects', this.newProject).then(res => {
        if (res.data.code === 0) {
          this.$message.success('创建成功')
          this.createDialog = false
          this.fetchProjects()
        } else {
          this.$message.error(res.data.msg || '创建失败')
        }
      }).catch(err => {
        console.error(err)
        this.$message.error('创建失败')
      })
    },
    editProjectName(row) {
      row.editing = true
      row.editName = row.name
      this.$nextTick(() => {
        document.querySelector('.el-table .el-input__inner').focus()
      })
    },
    saveProjectName(row) {
      if (row.editName === row.name) {
        row.editing = false
        return
      }
      
      this.$http.put(`/projects/${row.id}`, {
        name: row.editName
      }).then(res => {
        if (res.data.code === 0) {
          this.$message.success('修改成功')
          row.name = row.editName
          row.editing = false
        } else {
          this.$message.error(res.data.msg || '修改失败')
          row.editName = row.name
          row.editing = false
        }
      }).catch(err => {
        console.error(err)
        this.$message.error('修改失败')
        row.editName = row.name
        row.editing = false
      })
    },
    deleteProject(row) {
      this.$confirm('确认删除该项目吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.delete(`/projects/${row.id}`).then(res => {
          if (res.data.code === 0) {
            this.$message.success('删除成功')
            this.fetchProjects()
          } else {
            this.$message.error(res.data.msg || '删除失败')
          }
        }).catch(err => {
          console.error(err)
          this.$message.error('删除失败')
        })
      }).catch(() => {})
    },
    deleteSelected() {
      if (this.selectedProjects.length === 0) return
      
      this.$confirm(`确认删除选中的 ${this.selectedProjects.length} 个项目吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = this.selectedProjects.map(item => item.id)
        this.$http.post('/projects/batch-delete', { ids }).then(res => {
          if (res.data.code === 0) {
            this.$message.success('删除成功')
            this.fetchProjects()
          } else {
            this.$message.error(res.data.msg || '删除失败')
          }
        }).catch(err => {
          console.error(err)
          this.$message.error('删除失败')
        })
      }).catch(() => {})
    },
    
    // 项目详情
    viewProject(row) {
      this.currentProject = row
      this.detailDialog = true
      this.activeTab = 'info'
      this.fetchProjectPrompts(row.id)
      this.fetchCollectRecords(row.id)
    },
    fetchProjectPrompts(projectId) {
      this.$http.get(`/projects/${projectId}/prompts`).then(res => {
        if (res.data.code === 0) {
          this.projectPrompts = res.data.data
        } else {
          this.$message.error(res.data.msg || '获取项目提示词失败')
        }
      }).catch(err => {
        console.error(err)
        this.$message.error('获取项目提示词失败')
      })
    },
    fetchCollectRecords(projectId) {
      this.$http.get(`/projects/${projectId}/collect-records`).then(res => {
        if (res.data.code === 0) {
          this.collectRecords = res.data.data
        } else {
          this.$message.error(res.data.msg || '获取采集记录失败')
        }
      }).catch(err => {
        console.error(err)
        this.$message.error('获取采集记录失败')
      })
    },
    copyPrompt(row) {
      navigator.clipboard.writeText(row.content).then(() => {
        this.$message.success('复制成功')
      }).catch(() => {
        this.$message.error('复制失败')
      })
    },
    
    // 采集操作
    collectProject(row) {
      this.currentProject = row
      this.collectForm = {
        platform: 'deepseek',
        mode: 'deep',
        priority: 'medium',
        countType: 'new'
      }
      this.collectDialog = true
    },
    confirmCollect() {
      const params = {
        ...this.collectForm,
        projectId: this.currentProject.id
      }
      
      this.$http.post('/collect/start', params).then(res => {
        if (res.data.code === 0) {
          this.$message.success('采集任务已提交')
          this.collectDialog = false
          this.fetchProjects()
        } else {
          this.$message.error(res.data.msg || '采集任务提交失败')
        }
      }).catch(err => {
        console.error(err)
        this.$message.error('采集任务提交失败')
      })
    },
    viewCollectDetail(row) {
      this.$router.push(`/collect/task?roundId=${row.roundId}`)
    },
    viewSnapshot(row) {
      this.$router.push(`/collect/snapshot?roundId=${row.roundId}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.project-list {
  .operation-bar {
    margin-bottom: 20px;
  }
  
  .pagination {
    margin-top: 20px;
    text-align: right;
  }
}
</style>