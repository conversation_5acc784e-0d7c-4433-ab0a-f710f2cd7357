<template>
  <div class="prompt-generate">
    <el-form :model="form" label-width="120px">
      <el-card class="mb-20">
        <template #header>
          <div class="card-header">
            <span>输入内容</span>
          </div>
        </template>
        
        <el-form-item label="品牌名/公司名">
          <el-input v-model="form.brandName" placeholder="请输入品牌名或公司名"></el-input>
        </el-form-item>
        
        <el-form-item label="核心语义词">
          <el-input v-model="form.semanticWords" placeholder="请输入核心语义词，多个词用逗号分隔"></el-input>
        </el-form-item>
        
        <el-form-item label="提示词生成数量">
          <el-input-number v-model="form.count" :min="1" :max="100"></el-input-number>
        </el-form-item>
        
        <el-form-item label="行业导向">
          <el-select v-model="form.industry" placeholder="请选择行业导向">
            <el-option label="拍照手机推荐" value="photo_phone"></el-option>
            <el-option label="品牌官网/相关网站" value="brand_website"></el-option>
            <!-- 可以根据需要添加更多选项 -->
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="generatePrompts">生成提示词</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-card>
      
      <el-card v-if="results.length > 0">
        <template #header>
          <div class="card-header">
            <span>生成结果</span>
            <div>
              <el-button type="success" size="small" @click="saveToCollection">保存到提示词集合</el-button>
              <el-button type="info" size="small" @click="exportPrompts">导出提示词</el-button>
            </div>
          </div>
        </template>
        
        <el-table :data="results" style="width: 100%" border>
          <el-table-column type="index" width="50" />
          <el-table-column prop="prompt" label="提示词" />
          <el-table-column label="操作" width="150">
            <template #default="scope">
              <el-button type="primary" size="small" @click="copyPrompt(scope.row.prompt)">复制</el-button>
              <el-button type="danger" size="small" @click="removePrompt(scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </el-form>
    
    <!-- 保存到提示词集合对话框 -->
    <el-dialog v-model="saveDialogVisible" title="保存到提示词集合" width="30%">
      <el-form :model="saveForm" label-width="120px">
        <el-form-item label="选择集合">
          <el-select v-model="saveForm.collectionId" placeholder="请选择提示词集合">
            <el-option 
              v-for="item in collections" 
              :key="item.id" 
              :label="item.name" 
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="新建集合">
          <el-input v-model="saveForm.newCollection" placeholder="输入新集合名称"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="saveDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmSave">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      form: {
        brandName: '',
        semanticWords: '',
        count: 10,
        industry: ''
      },
      results: [],
      saveDialogVisible: false,
      saveForm: {
        collectionId: '',
        newCollection: ''
      },
      collections: [] // 这里应该从API获取提示词集合列表
    }
  },
  mounted() {
    this.fetchCollections()
  },
  methods: {
    generatePrompts() {
      // 这里应该调用API生成提示词
      // 模拟API调用
      if (!this.form.brandName && !this.form.semanticWords) {
        this.$message.error('请至少输入品牌名或核心语义词')
        return
      }
      
      this.$http.post('/api/prompts/generate', this.form).then(res => {
        if (res.data.code === 0) {
          this.results = res.data.data.map(item => ({ prompt: item }))
          this.$message.success('提示词生成成功')
        } else {
          this.$message.error(res.data.msg || '提示词生成失败')
        }
      }).catch(err => {
        console.error(err)
        this.$message.error('提示词生成失败')
      })
    },
    resetForm() {
      this.form = {
        brandName: '',
        semanticWords: '',
        count: 10,
        industry: ''
      }
      this.results = []
    },
    copyPrompt(prompt) {
      navigator.clipboard.writeText(prompt).then(() => {
        this.$message.success('复制成功')
      }).catch(() => {
        this.$message.error('复制失败')
      })
    },
    removePrompt(index) {
      this.results.splice(index, 1)
    },
    saveToCollection() {
      this.saveDialogVisible = true
    },
    exportPrompts() {
      const content = this.results.map(item => item.prompt).join('\n')
      const blob = new Blob([content], { type: 'text/plain' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `提示词_${new Date().toISOString().split('T')[0]}.txt`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    },
    fetchCollections() {
      // 这里应该调用API获取提示词集合列表
      // 模拟API调用
      this.$http.get('/prompts/collections').then(res => {
        if (res.data.code === 0) {
          this.collections = res.data.data
        }
      }).catch(err => {
        console.error(err)
      })
    },
    confirmSave() {
      // 这里应该调用API保存提示词到集合
      // 模拟API调用
      const params = {
        prompts: this.results.map(item => item.prompt)
      }
      
      if (this.saveForm.newCollection) {
        params.collectionName = this.saveForm.newCollection
      } else if (this.saveForm.collectionId) {
        params.collectionId = this.saveForm.collectionId
      } else {
        this.$message.error('请选择集合或创建新集合')
        return
      }
      
      this.$http.post('/api/prompts/save-to-collection', params).then(res => {
        if (res.data.code === 0) {
          this.$message.success('保存成功')
          this.saveDialogVisible = false
        } else {
          this.$message.error(res.data.msg || '保存失败')
        }
      }).catch(err => {
        console.error(err)
        this.$message.error('保存失败')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.prompt-generate {
  .mb-20 {
    margin-bottom: 20px;
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>