<template>
  <div class="collect-task">
    <div class="filter-container">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="项目名称">
          <el-select v-model="filterForm.projectId" placeholder="请选择项目" clearable>
            <el-option
              v-for="item in projects"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="采集平台">
          <el-select v-model="filterForm.platform" placeholder="请选择平台" clearable>
            <el-option label="DeepSeek" value="deepseek" />
            <el-option label="百度" value="baidu" />
            <el-option label="克洛诺斯" value="kimi" />
            <el-option label="文心一言" value="wenxin" />
            <el-option label="智谱" value="zhipu" />
            <el-option label="通义" value="tongyi" />
            <el-option label="讯飞" value="xunfei" />
            <el-option label="ChatGPT" value="chatgpt" />
          </el-select>
        </el-form-item>
        <el-form-item label="采集状态">
          <el-select v-model="filterForm.status" placeholder="请选择状态" clearable>
            <el-option label="进行中" value="running" />
            <el-option label="已完成" value="completed" />
            <el-option label="已暂停" value="paused" />
            <el-option label="已失败" value="failed" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filterForm.timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="operation-bar">
      <el-button type="primary" @click="createTask">新建采集任务</el-button>
      <el-button type="warning" @click="pauseSelected" :disabled="!canPauseSelected">暂停选中</el-button>
      <el-button type="success" @click="resumeSelected" :disabled="!canResumeSelected">恢复选中</el-button>
      <el-button type="danger" @click="deleteSelected" :disabled="!selectedTasks.length">删除选中</el-button>
    </div>

    <el-table
      :data="tasks"
      style="width: 100%"
      @selection-change="handleSelectionChange"
      border
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="roundId" label="轮次ID" width="80" />
      <el-table-column prop="projectName" label="项目名称" width="150" />
      <el-table-column prop="platform" label="采集平台" width="100">
        <template #default="scope">
          <el-tag>{{ getPlatformName(scope.row.platform) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="mode" label="采集模式" width="100">
        <template #default="scope">
          <el-tag type="success" v-if="scope.row.mode === 'deep'">深度模式</el-tag>
          <el-tag type="info" v-else>非深度模式</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="priority" label="优先级" width="80">
        <template #default="scope">
          <el-tag type="danger" v-if="scope.row.priority === 'high'">高</el-tag>
          <el-tag type="warning" v-else-if="scope.row.priority === 'medium'">中</el-tag>
          <el-tag type="info" v-else>低</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="progress" label="进度" width="180">
        <template #default="scope">
          <el-progress :percentage="scope.row.progress" :status="getProgressStatus(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column prop="startTime" label="开始时间" width="180" />
      <el-table-column prop="endTime" label="结束时间" width="180" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button type="primary" size="small" @click="viewTaskDetail(scope.row)">查看详情</el-button>
          <el-button type="success" size="small" @click="viewSnapshot(scope.row)">查看快照</el-button>
          <el-button 
            v-if="scope.row.status === 'running'" 
            type="warning" 
            size="small" 
            @click="pauseTask(scope.row)"
          >暂停</el-button>
          <el-button 
            v-if="scope.row.status === 'paused'" 
            type="success" 
            size="small" 
            @click="resumeTask(scope.row)"
          >恢复</el-button>
          <el-button 
            v-if="scope.row.status === 'completed' || scope.row.status === 'failed'" 
            type="danger" 
            size="small" 
            @click="deleteTask(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-if="total > 0"
      :current-page="currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      class="pagination"
    />

    <!-- 新建采集任务对话框 -->
    <el-dialog v-model="createDialog" title="新建采集任务" width="50%">
      <el-form :model="newTask" label-width="120px">
        <el-form-item label="项目">
          <el-select v-model="newTask.projectId" placeholder="请选择项目" style="width: 100%">
            <el-option
              v-for="item in projects"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="采集平台">
          <el-select v-model="newTask.platform" placeholder="请选择采集平台">
            <el-option label="DeepSeek" value="deepseek" />
            <el-option label="百度" value="baidu" />
            <el-option label="克洛诺斯" value="kimi" />
            <el-option label="文心一言" value="wenxin" />
            <el-option label="智谱" value="zhipu" />
            <el-option label="通义" value="tongyi" />
            <el-option label="讯飞" value="xunfei" />
            <el-option label="ChatGPT" value="chatgpt" />
          </el-select>
        </el-form-item>
        <el-form-item label="采集条件范围">
          <el-radio-group v-model="newTask.mode">
            <el-radio label="deep">深度模式</el-radio>
            <el-radio label="shallow">非深度模式</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="采集优先级">
          <el-select v-model="newTask.priority" placeholder="请选择采集优先级">
            <el-option label="高" value="high" />
            <el-option label="中" value="medium" />
            <el-option label="低" value="low" />
          </el-select>
        </el-form-item>
        <el-form-item label="采集数量">
          <el-radio-group v-model="newTask.countType">
            <el-radio label="new">对应创建1个采集轮次</el-radio>
            <el-radio label="all">10次创建10个</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="createDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmCreate">开始采集</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 任务详情对话框 -->
    <el-dialog v-model="detailDialog" :title="`采集任务详情: 轮次${currentTask.roundId}`" width="70%">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="项目名称">{{ currentTask.projectName }}</el-descriptions-item>
        <el-descriptions-item label="采集平台">{{ getPlatformName(currentTask.platform) }}</el-descriptions-item>
        <el-descriptions-item label="采集模式">{{ currentTask.mode === 'deep' ? '深度模式' : '非深度模式' }}</el-descriptions-item>
        <el-descriptions-item label="优先级">
          <el-tag :type="getPriorityType(currentTask.priority)">{{ getPriorityText(currentTask.priority) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="开始时间">{{ currentTask.startTime }}</el-descriptions-item>
        <el-descriptions-item label="结束时间">{{ currentTask.endTime || '未结束' }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(currentTask.status)">{{ getStatusText(currentTask.status) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="进度">
          <el-progress :percentage="currentTask.progress" :status="getProgressStatus(currentTask)" />
        </el-descriptions-item>
      </el-descriptions>

      <div class="task-detail-content">
        <h3>采集结果</h3>
        <el-table :data="taskResults" style="width: 100%" border>
          <el-table-column prop="promptId" label="提示词ID" width="80" />
          <el-table-column prop="prompt" label="提示词" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getResultStatusType(scope.row.status)">{{ getResultStatusText(scope.row.status) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template #default="scope">
              <el-button type="primary" size="small" @click="viewResult(scope.row)">查看结果</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 采集结果对话框 -->
    <el-dialog v-model="resultDialog" :title="`采集结果: 提示词${currentResult.promptId}`" width="80%">
      <div class="result-container">
        <div class="result-prompt">
          <h4>提示词</h4>
          <div class="prompt-content">{{ currentResult.prompt }}</div>
        </div>
        <div class="result-response">
          <h4>响应内容</h4>
          <div class="response-content">{{ currentResult.response }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // 筛选表单
      filterForm: {
        projectId: '',
        platform: '',
        status: '',
        timeRange: []
      },
      
      // 任务列表
      tasks: [],
      selectedTasks: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      
      // 对话框控制
      createDialog: false,
      detailDialog: false,
      resultDialog: false,
      
      // 当前选中的任务
      currentTask: {},
      taskResults: [],
      currentResult: {},
      
      // 表单数据
      newTask: {
        projectId: '',
        platform: 'deepseek',
        mode: 'deep',
        priority: 'medium',
        countType: 'new'
      },
      
      // 项目列表
      projects: []
    }
  },
  computed: {
    canPauseSelected() {
      return this.selectedTasks.some(task => task.status === 'running')
    },
    canResumeSelected() {
      return this.selectedTasks.some(task => task.status === 'paused')
    }
  },
  mounted() {
    this.fetchTasks()
    this.fetchProjects()
    
    // 检查URL参数，如果有roundId，则自动打开对应的任务详情
    const roundId = this.$route.query.roundId
    if (roundId) {
      this.fetchTaskByRoundId(roundId)
    }
  },
  methods: {
    // 获取采集任务列表
    fetchTasks() {
      const params = {
        page: this.currentPage,
        pageSize: this.pageSize,
        ...this.filterForm
      }
      
      if (this.filterForm.timeRange && this.filterForm.timeRange.length === 2) {
        params.startDate = this.filterForm.timeRange[0]
        params.endDate = this.filterForm.timeRange[1]
        delete params.timeRange
      }
      
      this.$http.get('/api/collect/tasks', { params }).then(res => {
        if (res.data.code === 0) {
          this.tasks = res.data.data.list
          this.total = res.data.data.total
        } else {
          this.$message.error(res.data.msg || '获取采集任务列表失败')
        }
      }).catch(err => {
        console.error(err)
        this.$message.error('获取采集任务列表失败')
      })
    },
    
    // 根据轮次ID获取任务详情
    fetchTaskByRoundId(roundId) {
      this.$http.get(`/api/collect/tasks/${roundId}`).then(res => {
        if (res.data.code === 0) {
          this.currentTask = res.data.data
          this.viewTaskDetail(this.currentTask)
        } else {
          this.$message.error(res.data.msg || '获取任务详情失败')
        }
      }).catch(err => {
        console.error(err)
        this.$message.error('获取任务详情失败')
      })
    },
    
    // 获取项目列表
    fetchProjects() {
      this.$http.get('/projects/all').then(res => {
        if (res.data.code === 0) {
          this.projects = res.data.data
        } else {
          this.$message.error(res.data.msg || '获取项目列表失败')
        }
      }).catch(err => {
        console.error(err)
        this.$message.error('获取项目列表失败')
      })
    },
    
    // 分页处理
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchTasks()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchTasks()
    },
    
    // 筛选处理
    handleFilter() {
      this.currentPage = 1
      this.fetchTasks()
    },
    resetFilter() {
      this.filterForm = {
        projectId: '',
        platform: '',
        status: '',
        timeRange: []
      }
      this.handleFilter()
    },
    
    // 表格选择处理
    handleSelectionChange(val) {
      this.selectedTasks = val
    },
    
    // 任务操作
    createTask() {
      this.newTask = {
        projectId: '',
        platform: 'deepseek',
        mode: 'deep',
        priority: 'medium',
        countType: 'new'
      }
      this.createDialog = true
    },
    confirmCreate() {
      if (!this.newTask.projectId) {
        this.$message.error('请选择项目')
        return
      }
      
      this.$http.post('/api/collect/start', this.newTask).then(res => {
        if (res.data.code === 0) {
          this.$message.success('采集任务已提交')
          this.createDialog = false
          this.fetchTasks()
        } else {
          this.$message.error(res.data.msg || '采集任务提交失败')
        }
      }).catch(err => {
        console.error(err)
        this.$message.error('采集任务提交失败')
      })
    },
    viewTaskDetail(row) {
      this.currentTask = row
      this.detailDialog = true
      this.fetchTaskResults(row.roundId)
    },
    fetchTaskResults(roundId) {
      this.$http.get(`/api/collect/tasks/${roundId}/results`).then(res => {
        if (res.data.code === 0) {
          this.taskResults = res.data.data
        } else {
          this.$message.error(res.data.msg || '获取采集结果失败')
        }
      }).catch(err => {
        console.error(err)
        this.$message.error('获取采集结果失败')
      })
    },
    viewResult(row) {
      this.currentResult = row
      this.resultDialog = true
    },
    viewSnapshot(row) {
      this.$router.push(`/collect/snapshot?roundId=${row.roundId}`)
    },
    pauseTask(row) {
      this.$http.post(`/api/collect/tasks/${row.roundId}/pause`).then(res => {
        if (res.data.code === 0) {
          this.$message.success('任务已暂停')
          this.fetchTasks()
        } else {
          this.$message.error(res.data.msg || '暂停任务失败')
        }
      }).catch(err => {
        console.error(err)
        this.$message.error('暂停任务失败')
      })
    },
    resumeTask(row) {
      this.$http.post(`/api/collect/tasks/${row.roundId}/resume`).then(res => {
        if (res.data.code === 0) {
          this.$message.success('任务已恢复')
          this.fetchTasks()
        } else {
          this.$message.error(res.data.msg || '恢复任务失败')
        }
      }).catch(err => {
        console.error(err)
        this.$message.error('恢复任务失败')
      })
    },
    deleteTask(row) {
      this.$confirm('确认删除该采集任务吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.delete(`/api/collect/tasks/${row.roundId}`).then(res => {
          if (res.data.code === 0) {
            this.$message.success('删除成功')
            this.fetchTasks()
          } else {
            this.$message.error(res.data.msg || '删除失败')
          }
        }).catch(err => {
          console.error(err)
          this.$message.error('删除失败')
        })
      }).catch(() => {})
    },
    pauseSelected() {
      const runningTasks = this.selectedTasks.filter(task => task.status === 'running')
      if (runningTasks.length === 0) return
      
      this.$confirm(`确认暂停选中的 ${runningTasks.length} 个运行中的任务吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = runningTasks.map(task => task.roundId)
        this.$http.post('/api/collect/tasks/batch-pause', { ids }).then(res => {
          if (res.data.code === 0) {
            this.$message.success('批量暂停成功')
            this.fetchTasks()
          } else {
            this.$message.error(res.data.msg || '批量暂停失败')
          }
        }).catch(err => {
          console.error(err)
          this.$message.error('批量暂停失败')
        })
      }).catch(() => {})
    },
    resumeSelected() {
      const pausedTasks = this.selectedTasks.filter(task => task.status === 'paused')
      if (pausedTasks.length === 0) return
      
      this.$confirm(`确认恢复选中的 ${pausedTasks.length} 个已暂停的任务吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = pausedTasks.map(task => task.roundId)
        this.$http.post('/api/collect/tasks/batch-resume', { ids }).then(res => {
          if (res.data.code === 0) {
            this.$message.success('批量恢复成功')
            this.fetchTasks()
          } else {
            this.$message.error(res.data.msg || '批量恢复失败')
          }
        }).catch(err => {
          console.error(err)
          this.$message.error('批量恢复失败')
        })
      }).catch(() => {})
    },
    deleteSelected() {
      if (this.selectedTasks.length === 0) return
      
      this.$confirm(`确认删除选中的 ${this.selectedTasks.length} 个采集任务吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = this.selectedTasks.map(task => task.roundId)
        this.$http.post('/api/collect/tasks/batch-delete', { ids }).then(res => {
          if (res.data.code === 0) {
            this.$message.success('批量删除成功')
            this.fetchTasks()
          } else {
            this.$message.error(res.data.msg || '批量删除失败')
          }
        }).catch(err => {
          console.error(err)
          this.$message.error('批量删除失败')
        })
      }).catch(() => {})
    },
    
    // 辅助方法
    getPlatformName(platform) {
      const platformMap = {
        deepseek: 'DeepSeek',
        baidu: '百度',
        kimi: '克洛诺斯',
        wenxin: '文心一言',
        zhipu: '智谱',
        tongyi: '通义',
        xunfei: '讯飞',
        chatgpt: 'ChatGPT'
      }
      return platformMap[platform] || platform
    },
    getStatusType(status) {
      const statusMap = {
        running: 'primary',
        completed: 'success',
        paused: 'warning',
        failed: 'danger'
      }
      return statusMap[status] || 'info'
    },
    getStatusText(status) {
      const statusMap = {
        running: '进行中',
        completed: '已完成',
        paused: '已暂停',
        failed: '已失败'
      }
      return statusMap[status] || status
    },
    getPriorityType(priority) {
      const priorityMap = {
        high: 'danger',
        medium: 'warning',
        low: 'info'
      }
      return priorityMap[priority] || 'info'
    },
    getPriorityText(priority) {
      const priorityMap = {
        high: '高',
        medium: '中',
        low: '低'
      }
      return priorityMap[priority] || priority
    },
    getProgressStatus(row) {
      if (row.status === 'failed') return 'exception'
      if (row.status === 'completed') return 'success'
      return ''
    },
    getResultStatusType(status) {
      const statusMap = {
        success: 'success',
        failed: 'danger',
        pending: 'info',
        processing: 'warning'
      }
      return statusMap[status] || 'info'
    },
    getResultStatusText(status) {
      const statusMap = {
        success: '成功',
        failed: '失败',
        pending: '等待中',
        processing: '处理中'
      }
      return statusMap[status] || status
    }
  }
}
</script>

<style lang="scss" scoped>
.collect-task {
  .filter-container {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
  
  .operation-bar {
    margin-bottom: 20px;
  }
  
  .pagination {
    margin-top: 20px;
    text-align: right;
  }
  
  .task-detail-content {
    margin-top: 20px;
    
    h3 {
      margin-bottom: 15px;
      font-weight: 500;
    }
  }
  
  .result-container {
    .result-prompt, .result-response {
      margin-bottom: 20px;
      
      h4 {
        margin-bottom: 10px;
        font-weight: 500;
      }
      
      .prompt-content, .response-content {
        padding: 10px;
        background-color: #f5f7fa;
        border-radius: 4px;
        white-space: pre-wrap;
        word-break: break-word;
      }
    }
  }
}
</style>