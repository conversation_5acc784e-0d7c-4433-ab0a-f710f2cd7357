import axios from 'axios'
import router from './router'
import { ElMessage, ElMessageBox } from 'element-plus'

const http = axios.create({
    baseURL: '/api',
});



http.interceptors.request.use(function (conf) {
    conf.headers.Authorization = localStorage.jwt;
    return conf;
}, function (error) {
    return Promise.reject(error);
});

http.interceptors.response.use(function (response) {
    return response.data;
}, function (error) {
    if (error.response && error.response.status == 401) {
        ElMessage.closeAll()
        localStorage.clear();
        sessionStorage.clear()
        ElMessage({
            message: '您的登录已过期，请重新登录！',
            type: 'warning',
        });
        router.replace('/login');
    } else {
        // 处理其他错误
        console.error('API请求错误:', error);
        ElMessage({
            message: '请求失败，请稍后重试',
            type: 'error',
        });
    }
    return Promise.reject(error);
});


export default http;